export interface OrderItem {
  code: string;
  dose: string;
  qty: number;
  buyingPrice?: number;
  sellingPrice?: number;
}

export interface CreateOrderRequest {
  customerName: string;
  email?: string;
  street1: string;
  street2?: string;
  city: string;
  state?: string;
  postalCode: string;
  country?: string;
  items: OrderItem[];
  paymentMethod: 'BTC' | 'USDT' | 'USDC' | 'PayPal';
  paymentUrl?: string;
  orderDate?: string;
}

export interface UpdateOrderRequest extends CreateOrderRequest {}

export interface OrderCalculations {
  subtotal: number;
  shipping: number;
  discount: number;
  totalUsd: number;
  totalBtc: number;
  profitUsd: number;
  profitInr: number;
  profitMargin: number;
}

export type OrderStatus = 'pending' | 'sent' | 'shipped' | 'in_transit' | 'delivered';

export interface OrderHistory {
  id: string;
  orderId: string;
  editedAt: Date;
  changeType: 'added' | 'removed' | 'modified';
  itemCode: string;
  itemDose: string;
  oldQty?: number | null;
  newQty?: number | null;
  oldPrice?: number | null;
  newPrice?: number | null;
}

export interface Order {
  id: string;
  orderNumber: string;
  placedAt: Date;
  customerName: string;
  email?: string;
  street1: string;
  street2?: string;
  city: string;
  state?: string;
  postalCode: string;
  country: string;
  totalUsd: number;
  totalBtc: number;
  profitUsd: number;
  profitInr: number;
  profitMargin: number;
  paymentMethod: string;
  paymentUrl?: string;
  tracking17?: string;
  trackingUrl?: string;
  sentToSupplier: boolean;
  sentAt?: Date;
  status: OrderStatus;
  // Two-category system
  supplierStatus?: 'sent' | 'not_sent';
  shippingStatus?: 'Info Received' | 'In Transit' | 'Delivered' | null;
  trackingStatus?: string;
  items: OrderItem[];
  // Edit tracking
  isEdited?: boolean;
  editedAt?: Date;
  history?: OrderHistory[];
}