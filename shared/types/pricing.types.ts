export interface CreatePricingRequest {
  product: string;
  dose: string;
  code: string;
  buyingPrice: number;
  sellingPrice: number;
}

export interface UpdatePricingRequest {
  product?: string;
  dose?: string;
  buyingPrice?: number;
  sellingPrice?: number;
}

export interface BulkImportRequest {
  mode: 'append' | 'replace';
  products: CreatePricingRequest[];
}

export interface PricingItem {
  code: string;
  product: string;
  dose: string;
  buyingPrice: number;
  sellingPrice: number;
  createdAt?: Date;
}

export interface PricingValidationError {
  field: string;
  message: string;
  value?: any;
}

export interface BulkImportResult {
  success: boolean;
  imported: number;
  errors?: {
    row: number;
    errors: PricingValidationError[];
  }[];
}

export interface ExportFormat {
  format: 'csv' | 'json';
}