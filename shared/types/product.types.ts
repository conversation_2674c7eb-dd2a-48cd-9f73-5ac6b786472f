// Product and Variant types for the new variant-based system

export interface ProductVariant {
  id: string;
  productId: string;
  dose: string;
  code: string;
  buyingPrice: number;
  sellingPrice: number;
  sortOrder: number;
  createdAt: Date;
  updatedAt: Date;
  profitMargin?: number; // Calculated field
}

export interface Product {
  id: string;
  name: string;
  createdAt: Date;
  updatedAt: Date;
  variants: ProductVariant[];
}

// Request types
export interface CreateProductRequest {
  name: string;
  variants: CreateVariantData[];
}

export interface CreateVariantData {
  dose: string;
  code: string;
  buyingPrice: number;
  sellingPrice: number;
}

export interface CreateVariantRequest extends CreateVariantData {}

export interface UpdateProductRequest {
  name: string;
}

export interface UpdateVariantRequest {
  dose?: string;
  buyingPrice?: number;
  sellingPrice?: number;
  sortOrder?: number;
}

export interface ReorderVariantsRequest {
  variantIds: string[];
}

// Response types
export interface ProductsResponse {
  products: Product[];
}

export interface ProductResponse {
  product: Product;
}

export interface VariantResponse {
  success: boolean;
  variant: ProductVariant;
}

export interface DeleteResponse {
  success: boolean;
}

// Error types
export interface VariantValidationError {
  index: number;
  errors: string[];
}

export interface ProductError {
  error: string;
  variantErrors?: VariantValidationError[];
  duplicates?: string[];
}