import * as dotenv from 'dotenv';
import { ParcelsAppService } from '../services/parcelsapp.service.js';

// Load environment variables
dotenv.config({ path: '.env' });

// Mock fastify instance
const mockFastify = {
  log: {
    info: (message: string, ...args: any[]) => console.log('INFO:', message, ...args),
    warn: (message: string, ...args: any[]) => console.warn('WARN:', message, ...args),
    error: (message: string, ...args: any[]) => console.error('ERROR:', message, ...args),
  },
  config: {
    PARCELS_APP_API_KEY: process.env.PARCELS_APP_API_KEY || ''
  }
} as any;

async function testParcelsAPI() {
  console.log('🚀 Testing Parcels App API Integration\n');
  
  const apiKey = process.env.PARCELS_APP_API_KEY || '';
  
  if (!apiKey) {
    console.error('❌ PARCELS_APP_API_KEY not found in .env file');
    return;
  }
  
  console.log('✅ API Key found:', apiKey.substring(0, 20) + '...');
  
  const service = new ParcelsAppService(mockFastify, apiKey);
  
  // Test tracking numbers
  const testTrackingNumbers = [
    'USP000658256',  // Development test tracking number 1
    '390596036979',  // Development test tracking number 2
  ];
  
  console.log('\n📦 Testing Tracking with Parcels API:');
  console.log('Note: Parcels API uses 2-phase tracking (create request → poll for results)\n');
  
  // First pass - create tracking requests (should consume credits)
  console.log('=== FIRST PASS: Creating new tracking requests ===\n');
  for (const trackingNumber of testTrackingNumbers) {
    console.log(`\nTracking: ${trackingNumber}`);
    console.log('-'.repeat(50));
    
    try {
      // Note: registerTrackingNumber is a no-op for Parcels API
      await service.registerTrackingNumber(trackingNumber);
      
      // Get tracking status (this does the 2-phase flow internally)
      console.log('⏳ Creating tracking request and polling for results...');
      const status = await service.getTrackingStatus(trackingNumber);
      
      if (status) {
        console.log('✅ Status retrieved:');
        console.log('  - Status:', status.statusText);
        console.log('  - Code:', status.status);
        console.log('  - Last Update:', status.lastUpdate);
        console.log('  - Tracking URL:', service.generateTrackingUrl(trackingNumber));
      } else {
        console.log('❌ No status returned');
      }
    } catch (error) {
      console.error('❌ Tracking failed:', error);
    }
  }
  
  // Second pass - should reuse UUIDs (no credits consumed)
  console.log('\n\n=== SECOND PASS: Reusing existing UUIDs (no credits consumed) ===\n');
  for (const trackingNumber of testTrackingNumbers) {
    console.log(`\nTracking: ${trackingNumber}`);
    console.log('-'.repeat(50));
    
    try {
      console.log('⏳ Checking tracking status (should reuse UUID)...');
      const status = await service.getTrackingStatus(trackingNumber);
      
      if (status) {
        console.log('✅ Status retrieved (from cache or reused UUID):');
        console.log('  - Status:', status.statusText);
        console.log('  - Code:', status.status);
      } else {
        console.log('❌ No status returned');
      }
    } catch (error) {
      console.error('❌ Tracking failed:', error);
    }
  }
  
  console.log('\n\n🔍 API Usage Statistics:');
  const stats = service.getApiUsageStats();
  console.log(`- Total API credits used: ${stats.requests}`);
  console.log(`- Unique tracking numbers: ${stats.uuidCount}`);
  console.log(`- Average credits per tracking: ${stats.averageCreditsPerTracking.toFixed(2)}`);
  console.log(`- Estimated cost: $${stats.estimatedCost.toFixed(2)}`);
  console.log(`- Cache entries: ${stats.cacheSize}`);
  
  console.log('\n✨ Testing complete!');
  console.log('\n💡 Key insights:');
  console.log('- First API call for a tracking number = 1 credit (creates UUID)');
  console.log('- Subsequent calls for same tracking number = 0 credits (reuses UUID)');
  console.log('- Cache stores results for 30 minutes');
  console.log('- UUIDs can be reused indefinitely until they expire');
}

// Run the test
testParcelsAPI().catch(console.error);