import axios from 'axios';

const API_URL = 'http://localhost:3000/api';

async function testProductManagement() {
  console.log('Testing Product Management Features...\n');
  
  try {
    // 1. Test viewing product list
    console.log('1. Testing product list view...');
    const listResponse = await axios.get(`${API_URL}/pricing`);
    const initialCount = listResponse.data.pricing.length;
    console.log(`✓ Successfully fetched ${initialCount} products\n`);
    
    // 2. Test adding a new product
    console.log('2. Testing add new product...');
    const newProduct = {
      code: 'TEST123',
      product: 'Test Peptide',
      dose: '25 mg',
      buyingPrice: 100,
      sellingPrice: 200
    };
    
    const createResponse = await axios.post(`${API_URL}/pricing`, newProduct);
    console.log(`✓ Successfully created product: ${createResponse.data.code}\n`);
    
    // 3. Test editing a product
    console.log('3. Testing edit product...');
    const updatedProduct = {
      ...newProduct,
      sellingPrice: 250
    };
    
    const updateResponse = await axios.put(`${API_URL}/pricing/${newProduct.code}`, updatedProduct);
    console.log(`✓ Successfully updated product: ${updateResponse.data.code} (new price: $${updateResponse.data.sellingPrice})\n`);
    
    // 4. Test searching products
    console.log('4. Testing product search...');
    const searchResponse = await axios.get(`${API_URL}/pricing`);
    const testProduct = searchResponse.data.pricing.find((p: any) => p.code === 'TEST123');
    if (testProduct) {
      console.log(`✓ Successfully found test product with updated price: $${testProduct.sellingPrice}\n`);
    }
    
    // 5. Test bulk import
    console.log('5. Testing bulk import...');
    const bulkProducts = [
      { code: 'BULK1', product: 'Bulk Test 1', dose: '10 mg', buyingPrice: 50, sellingPrice: 100 },
      { code: 'BULK2', product: 'Bulk Test 2', dose: '20 mg', buyingPrice: 75, sellingPrice: 150 }
    ];
    
    const bulkResponse = await axios.post(`${API_URL}/pricing/bulk`, { 
      mode: 'append',
      products: bulkProducts 
    });
    console.log(`✓ Successfully imported ${bulkResponse.data.imported} products\n`);
    
    // 6. Test deleting products
    console.log('6. Testing delete product...');
    await axios.delete(`${API_URL}/pricing/TEST123`);
    console.log('✓ Successfully deleted TEST123');
    
    // Clean up bulk imported products
    await axios.delete(`${API_URL}/pricing/BULK1`);
    await axios.delete(`${API_URL}/pricing/BULK2`);
    console.log('✓ Cleaned up bulk imported products\n');
    
    // 7. Verify final count
    console.log('7. Verifying final product count...');
    const finalResponse = await axios.get(`${API_URL}/pricing`);
    const finalCount = finalResponse.data.pricing.length;
    console.log(`✓ Final product count: ${finalCount} (should match initial: ${initialCount})\n`);
    
    // 8. Test dashboard product count
    console.log('8. Testing dashboard product count...');
    const dashboardResponse = await axios.get(`${API_URL}/dashboard/metrics`);
    console.log(`✓ Dashboard shows ${dashboardResponse.data.products.total} products\n`);
    
    console.log('✅ All product management tests passed!');
    
  } catch (error: any) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    process.exit(1);
  }
}

// Run the test
testProductManagement();