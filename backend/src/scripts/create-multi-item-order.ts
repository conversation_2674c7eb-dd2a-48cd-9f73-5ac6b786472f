import axios from 'axios';

const API_BASE = 'http://localhost:3000/api';

async function createMultiItemOrder() {
  try {
    const orderData = {
      customerName: 'Multi-Item Excel Format Test',
      street1: '789 Testing Boulevard',
      street2: 'Unit 3B',
      city: 'San Francisco',
      state: 'CA',
      postalCode: '94105',
      country: 'USA',
      paymentMethod: 'BTC',
      paymentUrl: 'https://blockchain.info/tx/test-multi-excel-format',
      items: [
        {
          code: 'TR20',
          dose: '20 mg',
          qty: 2
        },
        {
          code: 'SM15',
          dose: '15 mg',
          qty: 3
        },
        {
          code: 'BA10',
          dose: '10 ml × 10 vials',
          qty: 1
        }
      ]
    };

    const response = await axios.post(`${API_BASE}/orders`, orderData);
    console.log('✅ Multi-item order created successfully!');
    console.log('Response:', JSON.stringify(response.data, null, 2));
  } catch (error: any) {
    console.error('❌ Failed to create order:', error.response?.data || error.message);
  }
}

createMultiItemOrder();
