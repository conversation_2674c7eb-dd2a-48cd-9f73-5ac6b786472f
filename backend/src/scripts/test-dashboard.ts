import axios from 'axios';

const API_BASE = 'http://localhost:3000/api';

async function testDashboardMetrics() {
  try {
    console.log('Testing dashboard metrics endpoint...\n');
    
    const response = await axios.get(`${API_BASE}/dashboard/metrics`);
    
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(response.data, null, 2));
    
    // Validate response structure
    const { profit, orders } = response.data;
    
    console.log('\n=== Profit Summary ===');
    console.log(`Total USD: $${profit.totalUSD.toLocaleString()}`);
    console.log(`Total INR: ₹${profit.totalINR.toLocaleString()}`);
    console.log('\nProfit by Product:');
    Object.entries(profit.byProduct).forEach(([product, amount]) => {
      console.log(`  ${product}: $${(amount as number).toLocaleString()}`);
    });
    
    console.log('\n=== Orders Summary ===');
    console.log(`Total Orders: ${orders.total}`);
    console.log('Orders by Status:');
    console.log(`  Pending: ${orders.byStatus.pending}`);
    console.log(`  Sent: ${orders.byStatus.sent}`);
    console.log(`  Shipped: ${orders.byStatus.shipped}`);
    console.log(`\nRecent Orders: ${orders.recent.length} orders returned`);
    
    if (orders.recent.length > 0) {
      console.log('\n=== Sample Recent Order ===');
      const sampleOrder = orders.recent[0];
      console.log(`Order Number: ${sampleOrder.orderNumber}`);
      console.log(`Customer: ${sampleOrder.customerName}`);
      console.log(`Total USD: $${sampleOrder.totalUsd.toLocaleString()}`);
      console.log(`Profit USD: $${sampleOrder.profitUsd.toLocaleString()}`);
      console.log(`Status: ${sampleOrder.status}`);
      console.log(`Items: ${sampleOrder.items.length}`);
    }
    
  } catch (error) {
    console.error('Error testing dashboard metrics:', error);
    if (axios.isAxiosError(error)) {
      console.error('Response:', error.response?.data);
    }
  }
}

testDashboardMetrics();