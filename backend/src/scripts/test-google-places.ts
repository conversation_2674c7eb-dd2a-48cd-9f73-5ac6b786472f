import axios from 'axios';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

// Load environment variables
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
dotenv.config({ path: path.join(__dirname, '../../.env') });

async function testGooglePlacesAPI() {
  const apiKey = process.env.GOOGLE_API_KEY;
  
  if (!apiKey) {
    console.error('❌ GOOGLE_API_KEY not found in environment variables');
    return;
  }

  console.log('🔍 Testing Google Places API...');
  
  try {
    // Test with a simple autocomplete query
    const response = await axios.get('https://maps.googleapis.com/maps/api/place/autocomplete/json', {
      params: {
        input: '123 Main Street New York',
        types: 'address',
        components: 'country:us',
        key: apiKey
      }
    });

    if (response.data.status === 'OK') {
      console.log('✅ Google Places API is working!');
      console.log(`Found ${response.data.predictions.length} predictions`);
      
      // Show first prediction as example
      if (response.data.predictions.length > 0) {
        console.log('\nExample prediction:');
        console.log('- Description:', response.data.predictions[0].description);
        console.log('- Place ID:', response.data.predictions[0].place_id);
      }
    } else {
      console.error('❌ API returned status:', response.data.status);
      if (response.data.error_message) {
        console.error('Error message:', response.data.error_message);
      }
    }
  } catch (error) {
    console.error('❌ Error testing Google Places API:', error.message);
    if (error.response?.data) {
      console.error('Response data:', error.response.data);
    }
  }
}

testGooglePlacesAPI();