import { PrismaClient } from '../generated/prisma/index.js';

// Create separate Prisma clients for dev and prod databases
const devPrisma = new PrismaClient({
  datasources: {
    db: {
      url: 'postgresql://sagar@localhost:5432/peptide_portal_dev'
    }
  }
});

const prodPrisma = new PrismaClient({
  datasources: {
    db: {
      url: 'postgresql://sagar@localhost:5432/peptide_portal_prod'
    }
  }
});

async function migrateOrders() {
  console.log('🚀 Starting Orders Migration from Dev to Prod...\n');
  
  try {
    // Step 1: Get current order counts
    const devOrderCount = await devPrisma.order.count();
    const prodOrderCount = await prodPrisma.order.count();
    
    console.log(`📊 Current order counts:`);
    console.log(`  - Dev database: ${devOrderCount} orders`);
    console.log(`  - Prod database: ${prodOrderCount} orders`);
    
    // Step 2: Get orders that exist in dev but not in prod
    const devOrders = await devPrisma.order.findMany({
      include: {
        items: true,
        history: true
      },
      orderBy: {
        placedAt: 'asc'
      }
    });
    
    const prodOrderNumbers = await prodPrisma.order.findMany({
      select: { orderNumber: true }
    });
    
    const existingOrderNumbers = new Set(prodOrderNumbers.map(o => o.orderNumber));
    const ordersToMigrate = devOrders.filter(order => !existingOrderNumbers.has(order.orderNumber));
    
    console.log(`\n🔄 Orders to migrate: ${ordersToMigrate.length}`);
    
    if (ordersToMigrate.length === 0) {
      console.log('✅ No new orders to migrate!');
      return;
    }
    
    // Step 3: Migrate orders one by one
    let migratedCount = 0;
    
    for (const order of ordersToMigrate) {
      try {
        // Create order with items and history
        await prodPrisma.order.create({
          data: {
            id: order.id,
            orderNumber: order.orderNumber,
            placedAt: order.placedAt,
            customerName: order.customerName,
            email: order.email,
            street1: order.street1,
            street2: order.street2,
            city: order.city,
            state: order.state,
            postalCode: order.postalCode,
            country: order.country,
            totalUsd: order.totalUsd,
            totalBtc: order.totalBtc,
            profitUsd: order.profitUsd,
            profitInr: order.profitInr,
            profitMargin: order.profitMargin,
            paymentMethod: order.paymentMethod,
            paymentUrl: order.paymentUrl,
            tracking17: order.tracking17,
            trackingStatus: order.trackingStatus,
            sentToSupplier: order.sentToSupplier,
            sentAt: order.sentAt,
            status: order.status,
            isEdited: order.isEdited,
            editedAt: order.editedAt,
            items: {
              create: order.items.map(item => ({
                id: item.id,
                code: item.code,
                dose: item.dose,
                qty: item.qty,
                buyingPrice: item.buyingPrice,
                sellingPrice: item.sellingPrice,
                variantId: item.variantId
              }))
            },
            history: {
              create: order.history.map(hist => ({
                id: hist.id,
                editedAt: hist.editedAt,
                changeType: hist.changeType,
                itemCode: hist.itemCode,
                itemDose: hist.itemDose,
                oldQty: hist.oldQty,
                newQty: hist.newQty,
                oldPrice: hist.oldPrice,
                newPrice: hist.newPrice
              }))
            }
          }
        });
        
        migratedCount++;
        console.log(`✅ Migrated order ${order.orderNumber} (${migratedCount}/${ordersToMigrate.length})`);
        
      } catch (error) {
        console.error(`❌ Failed to migrate order ${order.orderNumber}:`, error);
      }
    }
    
    // Step 4: Final verification
    const finalProdCount = await prodPrisma.order.count();
    const latestOrder = await prodPrisma.order.findFirst({
      orderBy: { orderNumber: 'desc' },
      select: { orderNumber: true, placedAt: true }
    });
    
    console.log(`\n🎉 Migration completed!`);
    console.log(`📊 Final statistics:`);
    console.log(`  - Orders migrated: ${migratedCount}`);
    console.log(`  - Total prod orders: ${finalProdCount}`);
    console.log(`  - Latest order: ${latestOrder?.orderNumber} (${latestOrder?.placedAt})`);
    
  } catch (error) {
    console.error('\n❌ Migration failed:', error);
  } finally {
    await devPrisma.$disconnect();
    await prodPrisma.$disconnect();
  }
}

migrateOrders();
