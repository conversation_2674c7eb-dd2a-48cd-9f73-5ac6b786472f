import * as dotenv from 'dotenv';
import { ParcelsAppService } from '../services/parcelsapp.service.js';

// Load environment variables
dotenv.config({ path: '.env' });

// Mock fastify instance
const mockFastify = {
  log: {
    info: (message: string, ...args: any[]) => console.log('INFO:', message, ...args),
    warn: (message: string, ...args: any[]) => console.warn('WARN:', message, ...args),
    error: (message: string, ...args: any[]) => console.error('ERROR:', message, ...args),
  },
  config: {
    PARCELS_APP_API_KEY: process.env.PARCELS_APP_API_KEY || ''
  }
} as any;

async function testUuidReuse() {
  console.log('🧪 Testing UUID Reuse for API Credit Optimization\n');
  
  const apiKey = process.env.PARCELS_APP_API_KEY || '';
  
  if (!apiKey) {
    console.error('❌ PARCELS_APP_API_KEY not found in .env file');
    return;
  }
  
  const service = new ParcelsAppService(mockFastify, apiKey);
  
  // Single tracking number for focused testing
  const trackingNumber = 'USP000658256';
  
  console.log('📊 Initial Stats:');
  let stats = service.getApiUsageStats();
  console.log(`Credits used: ${stats.requests}, UUIDs stored: ${stats.uuidCount}\n`);
  
  // First call - should create new tracking request (1 credit)
  console.log('1️⃣ First API call (should consume 1 credit):');
  await service.getTrackingStatus(trackingNumber);
  
  stats = service.getApiUsageStats();
  console.log(`\n📊 After first call:`);
  console.log(`Credits used: ${stats.requests}, UUIDs stored: ${stats.uuidCount}\n`);
  
  // Wait a moment
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Second call - should reuse UUID (0 credits)
  console.log('2️⃣ Second API call (should reuse UUID, 0 credits):');
  await service.getTrackingStatus(trackingNumber);
  
  stats = service.getApiUsageStats();
  console.log(`\n📊 After second call:`);
  console.log(`Credits used: ${stats.requests}, UUIDs stored: ${stats.uuidCount}\n`);
  
  console.log('✅ Test complete!');
  console.log(`\n💰 Result: ${stats.requests} credit(s) used for 2 tracking requests`);
  console.log('🎯 Expected: 1 credit (first creates UUID, second reuses it)');
  
  // Force exit
  process.exit(0);
}

// Run the test
testUuidReuse().catch(error => {
  console.error('Test failed:', error);
  process.exit(1);
});