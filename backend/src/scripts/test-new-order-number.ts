// Test script to verify new order numbering system
async function testNewOrderNumber() {
  const orderData = {
    customerName: "John Test",
    street1: "123 Test Street",
    city: "New York",
    state: "NY",
    postalCode: "10001",
    items: [
      {
        code: "RT10",
        dose: "10 mg",
        qty: 2
      }
    ],
    paymentMethod: "BTC",
    paymentUrl: "https://blockchain.info/tx/test123"
  };

  try {
    const response = await fetch('http://localhost:3000/api/orders', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(orderData),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json() as { order: { orderNumber: string; id: string; totalUsd: number } };
    console.log('Order created successfully!');
    console.log('Order Number:', result.order.orderNumber);
    console.log('Order ID:', result.order.id);
    console.log('Total USD:', result.order.totalUsd);
  } catch (error) {
    console.error('Error creating order:', error);
  }
}

testNewOrderNumber();