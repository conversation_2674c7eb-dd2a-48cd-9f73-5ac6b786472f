import { PDFService } from '../services/pdf.service';
import { ExcelService } from '../services/excel.service';
import { emailService } from '../services/email.service';
import type { Order, OrderItem } from '../generated/prisma/index.js';
import { Decimal } from '@prisma/client/runtime/library';

// Create a mock order to test the services
const mockOrder: Order & { items: OrderItem[] } = {
  id: 'test-123',
  orderNumber: 'ORD-2024-0001',
  placedAt: new Date(),
  customerName: 'Test Customer',
  email: null,
  street1: '123 Test St',
  street2: null,
  city: 'Test City',
  state: 'CA',
  postalCode: '12345',
  country: 'United States',
  totalUsd: new Decimal(500),
  totalBtc: new Decimal(0.01),
  profitUsd: new Decimal(100),
  profitInr: new Decimal(8600),
  profitMargin: new Decimal(20),
  paymentMethod: 'BTC',
  paymentUrl: 'https://example.com/payment',
  tracking17: null,
  trackingStatus: null,
  sentToSupplier: false,
  sentAt: null,
  status: 'pending',
  isEdited: false,
  editedAt: null,
  items: [
    {
      id: BigInt(1),
      orderId: 'test-123',
      code: 'BPC-157',
      dose: '5mg',
      qty: 2,
      buyingPrice: new Decimal(100), // Buying price
      sellingPrice: new Decimal(150), // Selling price
      variantId: null
    },
    {
      id: BigInt(2),
      orderId: 'test-123',
      code: 'TB-500',
      dose: '10mg',
      qty: 1,
      buyingPrice: new Decimal(200), // Buying price
      sellingPrice: new Decimal(250), // Selling price
      variantId: null
    }
  ]
};

console.log('=== Testing Buying Price Fix ===\n');

console.log('Order Items:');
mockOrder.items.forEach(item => {
  console.log(`${item.code} (${item.dose}):`);
  console.log(`  Buying Price: $${item.buyingPrice.toString()}`);
  console.log(`  Selling Price: $${item.sellingPrice.toString()}`);
  console.log(`  Quantity: ${item.qty}`);
  console.log(`  Expected in supplier docs: $${item.buyingPrice.toString()} × ${item.qty} = $${Number(item.buyingPrice) * item.qty}`);
  console.log('');
});

console.log('Expected calculations for supplier:');
const expectedSubtotal = mockOrder.items.reduce((sum, item) => sum + (Number(item.buyingPrice) * item.qty), 0);
const expectedShipping = 40;
const expectedDiscount = expectedSubtotal * 0.05;
const expectedTotal = expectedSubtotal + expectedShipping - expectedDiscount;

console.log(`  Subtotal (using buying prices): $${expectedSubtotal.toFixed(2)}`);
console.log(`  Shipping: $${expectedShipping.toFixed(2)}`);
console.log(`  Discount (5%): -$${expectedDiscount.toFixed(2)}`);
console.log(`  Total: $${expectedTotal.toFixed(2)}`);

console.log('\n✅ If the services are correctly updated:');
console.log('   - PDF should show unit prices as $100 and $200 (buying prices)');
console.log('   - Excel should show unit prices as $100 and $200 (buying prices)');
console.log('   - Email HTML should calculate totals using buying prices');
console.log('   - Total should be $426.80 (not $543.70 which would use selling prices)');

console.log('\n❌ If still using selling prices:');
console.log('   - PDF would show unit prices as $150 and $250 (selling prices)');
console.log('   - Total would be $543.70');

console.log('\nTo generate test files and verify the fix:');
console.log('1. Start the server: npm run dev');
console.log('2. Create a real order through the API or UI');
console.log('3. Export the order as PDF/Excel');
console.log('4. Check that prices match buying prices, not selling prices');