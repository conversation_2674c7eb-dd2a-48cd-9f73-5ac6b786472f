import { PrismaClient } from '../generated/prisma/index.js';

const prisma = new PrismaClient({
  datasources: {
    db: {
      url: 'postgresql://sagar@localhost:5432/peptide_portal_prod'
    }
  }
});

// Missing orders data from CSV
const missingOrdersData = [
  {
    orderNumber: 'ORD-2025-0088',
    customerName: 'Chioma Eke',
    address: '1 Addington Road, Sittingbourne,  ME10 1HT, United Kingdom',
    orderDate: '7/31/2025',
    items: [{ product: 'RT10', qty: 1, unitPrice: 105.00, lineTotal: 105.00, discount: 5.25, totalDue: 139.75 }]
  },
  {
    orderNumber: 'ORD-2025-0087',
    customerName: '<PERSON>acie <PERSON>',
    address: '9833 Bradford Summit St, Las Vegas, NV 89183-6243, United States',
    orderDate: '7/31/2025',
    items: [{ product: 'SM15', qty: 1, unitPrice: 75.00, lineTotal: 75.00, discount: 3.75, totalDue: 111.25 }]
  },
  {
    orderNumber: 'ORD-2025-0086',
    customerName: 'Antoinette Bordeaux',
    address: '4608 W North Ln, Glendale, AZ 85302-1921, United States',
    orderDate: '7/31/2025',
    items: [{ product: 'TR20', qty: 1, unitPrice: 105.00, lineTotal: 105.00, discount: 5.25, totalDue: 139.75 }]
  },
  {
    orderNumber: 'ORD-2025-0085',
    customerName: 'Crystal Zeller',
    address: '5525 Baron Dr, Joshua, TX 76058-4392, United States',
    orderDate: '7/31/2025',
    items: [{ product: 'TR30', qty: 1, unitPrice: 115.00, lineTotal: 115.00, discount: 5.75, totalDue: 149.25 }]
  },
  {
    orderNumber: 'ORD-2025-0084',
    customerName: 'kim grinnell',
    address: '3076 Twin Oaks Rd, Cameron Park, CA 95682-8517, United States',
    orderDate: '7/31/2025',
    items: [{ product: 'RT15', qty: 1, unitPrice: 140.00, lineTotal: 140.00, discount: 7.00, totalDue: 173.00 }]
  },
  {
    orderNumber: 'ORD-2025-0083',
    customerName: 'Deborah Fornari',
    address: '9156 Holly Hill Dr, Salinas, CA 93907-1440, United States',
    orderDate: '7/31/2025',
    items: [{ product: 'TR60', qty: 1, unitPrice: 215.00, lineTotal: 215.00, discount: 10.75, totalDue: 244.25 }]
  },
  {
    orderNumber: 'ORD-2025-0082',
    customerName: 'Hailey Hunt',
    address: '3312 Valley Hollow, Norman, OK 73071-3684, United States',
    orderDate: '7/31/2025',
    items: [
      { product: 'TR30', qty: 1, unitPrice: 115.00, lineTotal: 115.00, discount: 6.55, totalDue: 164.45 },
      { product: 'BA10', qty: 1, unitPrice: 16.00, lineTotal: 16.00, discount: 0, totalDue: 16.00 }
    ]
  },
  {
    orderNumber: 'ORD-2025-0081',
    customerName: 'Madilynn Calkins',
    address: '701 Canal Dr, Chesapeake, VA 23323-4300, United States',
    orderDate: '7/30/2025',
    items: [{ product: 'SM5', qty: 1, unitPrice: 48.00, lineTotal: 48.00, discount: 2.40, totalDue: 85.60 }]
  },
  {
    orderNumber: 'ORD-2025-0080',
    customerName: 'Mason Tarascou',
    address: '6350 Co Rd 189, Florence, AL 35633-0406, United States',
    orderDate: '7/30/2025',
    items: [
      { product: 'IP5', qty: 1, unitPrice: 40.00, lineTotal: 40.00, discount: 3.75, totalDue: 111.25 },
      { product: 'IG01', qty: 1, unitPrice: 35.00, lineTotal: 35.00, discount: 0, totalDue: 35.00 }
    ]
  },
  {
    orderNumber: 'ORD-2025-0079',
    customerName: 'JENNIFER PORTER',
    address: '1902 S State Hwy 121 apt 910, apt 910, Lewisville, TX 75067-6548, United States',
    orderDate: '7/30/2025',
    items: [{ product: 'SM15', qty: 1, unitPrice: 75.00, lineTotal: 75.00, discount: 3.75, totalDue: 111.25 }]
  },
  {
    orderNumber: 'ORD-2025-0078',
    customerName: 'Clara Dostal',
    address: '33851 Marie Rd, Pine, CO 80470-8630, United States',
    orderDate: '7/30/2025',
    items: [{ product: 'TR30', qty: 1, unitPrice: 115.00, lineTotal: 115.00, discount: 5.75, totalDue: 149.25 }]
  },
  {
    orderNumber: 'ORD-2025-0077',
    customerName: 'Dave McMullan',
    address: '935 Elizabeth St, Rexburg, ID 83440-5069, United States',
    orderDate: '7/29/2025',
    items: [
      { product: 'RT10', qty: 1, unitPrice: 105.00, lineTotal: 105.00, discount: 14.00, totalDue: 306.00 },
      { product: 'GLOW70', qty: 1, unitPrice: 175.00, lineTotal: 175.00, discount: 0, totalDue: 175.00 }
    ]
  },
  {
    orderNumber: 'ORD-2025-0076',
    customerName: 'Jamie Lin',
    address: '2043 North Orange Olive Road, Orange, CA 92865, United States',
    orderDate: '7/29/2025',
    items: [{ product: 'TR15', qty: 1, unitPrice: 90.00, lineTotal: 90.00, discount: 4.50, totalDue: 125.50 }]
  },
  {
    orderNumber: 'ORD-2025-0075',
    customerName: 'Christopher Kaiser',
    address: '426 Hampton Boulevard, Rochester, NY 14612, United States',
    orderDate: '7/28/2025',
    items: [{ product: 'RT10', qty: 1, unitPrice: 105.00, lineTotal: 105.00, discount: 5.25, totalDue: 139.75 }]
  },
  {
    orderNumber: 'ORD-2025-0074',
    customerName: 'Giovanni Mineo',
    address: '11148 Lalani Drive, La Mesa, CA 91941, United States',
    orderDate: '7/28/2025',
    items: [
      { product: 'CU50', qty: 1, unitPrice: 32.00, lineTotal: 32.00, discount: 15.60, totalDue: 336.40 },
      { product: 'CP10', qty: 1, unitPrice: 100.00, lineTotal: 100.00, discount: 0, totalDue: 100.00 },
      { product: 'RT20', qty: 1, unitPrice: 180.00, lineTotal: 180.00, discount: 0, totalDue: 180.00 }
    ]
  },
  {
    orderNumber: 'ORD-2025-0073',
    customerName: 'Peyton Robertson',
    address: '1352 Dale Street, San Diego, CA 92102, United States',
    orderDate: '7/28/2025',
    items: [{ product: 'CU50', qty: 1, unitPrice: 32.00, lineTotal: 32.00, discount: 1.60, totalDue: 70.40 }]
  },
  {
    orderNumber: 'ORD-2025-0072',
    customerName: 'Allicia Geagon',
    address: '4420 Clar Mar Lane Southeast, Olympia, WA 98501, United States',
    orderDate: '7/28/2025',
    items: [{ product: 'TR20', qty: 1, unitPrice: 105.00, lineTotal: 105.00, discount: 5.25, totalDue: 139.75 }]
  },
  {
    orderNumber: 'ORD-2025-0071',
    customerName: 'Peyton Robertson',
    address: '1352 Dale Street, San Diego, CA 92102, United States',
    orderDate: '7/27/2025',
    items: [{ product: 'SM10', qty: 1, unitPrice: 60.00, lineTotal: 60.00, discount: 3.00, totalDue: 97.00 }]
  },
  {
    orderNumber: 'ORD-2025-0070',
    customerName: 'Tyler Hunt',
    address: '2328 Portland Drive, Maryville, TN 37803, United States',
    orderDate: '7/27/2025',
    items: [{ product: 'RT10', qty: 1, unitPrice: 105.00, lineTotal: 105.00, discount: 5.25, totalDue: 139.75 }]
  },
  {
    orderNumber: 'ORD-2025-0069',
    customerName: 'David Olde',
    address: '334 Paradise Road, Aberdeen, MD 21001, United States',
    orderDate: '7/27/2025',
    items: [
      { product: 'BB10', qty: 1, unitPrice: 90.00, lineTotal: 90.00, discount: 6.10, totalDue: 155.90 },
      { product: 'BA10', qty: 2, unitPrice: 16.00, lineTotal: 32.00, discount: 0, totalDue: 32.00 }
    ]
  },
  {
    orderNumber: 'ORD-2025-0068',
    customerName: 'Erin K Sullivan',
    address: '5129 Lexington Street, Erie, PA 16509, United States',
    orderDate: '7/27/2025',
    items: [{ product: 'TR5', qty: 1, unitPrice: 48.00, lineTotal: 48.00, discount: 2.40, totalDue: 85.60 }]
  }
];

// Product mapping to match database format
const productMapping: Record<string, { dose: string, buyingPrice: number, sellingPrice: number }> = {
  'RT10': { dose: '10 mg', buyingPrice: 70.00, sellingPrice: 105.00 },
  'SM15': { dose: '15 mg', buyingPrice: 50.00, sellingPrice: 75.00 },
  'TR20': { dose: '20 mg', buyingPrice: 70.00, sellingPrice: 105.00 },
  'TR30': { dose: '30 mg', buyingPrice: 80.00, sellingPrice: 115.00 },
  'RT15': { dose: '15 mg', buyingPrice: 95.00, sellingPrice: 140.00 },
  'TR60': { dose: '60 mg', buyingPrice: 145.00, sellingPrice: 215.00 },
  'BA10': { dose: '10 ml × 10 vials', buyingPrice: 8.00, sellingPrice: 16.00 },
  'SM5': { dose: '5 mg', buyingPrice: 32.00, sellingPrice: 48.00 },
  'IP5': { dose: '5 mg', buyingPrice: 27.00, sellingPrice: 40.00 },
  'IG01': { dose: '1 mg', buyingPrice: 24.00, sellingPrice: 35.00 },
  'GLOW70': { dose: '70 mg', buyingPrice: 115.00, sellingPrice: 175.00 },
  'TR15': { dose: '15 mg', buyingPrice: 60.00, sellingPrice: 90.00 },
  'CU50': { dose: '50 mg', buyingPrice: 22.00, sellingPrice: 32.00 },
  'CP10': { dose: '10 mg', buyingPrice: 65.00, sellingPrice: 100.00 },
  'RT20': { dose: '20 mg', buyingPrice: 120.00, sellingPrice: 180.00 },
  'SM10': { dose: '10 mg', buyingPrice: 40.00, sellingPrice: 60.00 },
  'BB10': { dose: '10 mg', buyingPrice: 60.00, sellingPrice: 90.00 },
  'TR5': { dose: '5 mg', buyingPrice: 32.00, sellingPrice: 48.00 }
};

function parseAddress(addressString: string) {
  // Simple address parsing - you may need to adjust based on your requirements
  const parts = addressString.split(', ');
  
  let street1 = '';
  let street2 = '';
  let city = '';
  let state = '';
  let postalCode = '';
  let country = 'United States';
  
  if (addressString.includes('United Kingdom')) {
    country = 'United Kingdom';
    // UK address format
    if (parts.length >= 3) {
      street1 = parts[0];
      city = parts[1];
      const lastPart = parts[parts.length - 1];
      if (lastPart.includes('United Kingdom')) {
        const beforeCountry = parts[parts.length - 2];
        const matches = beforeCountry.match(/([A-Z]{1,2}\d{1,2}[A-Z]?\s?\d[A-Z]{2})/);
        if (matches) {
          postalCode = matches[1];
        }
      }
    }
  } else {
    // US address format
    if (parts.length >= 3) {
      street1 = parts[0];
      if (parts.length > 3) {
        city = parts[parts.length - 3];
        const stateZip = parts[parts.length - 2];
        const matches = stateZip.match(/([A-Z]{2})\s+(\d{5}(?:-\d{4})?)/);
        if (matches) {
          state = matches[1];
          postalCode = matches[2];
        }
      }
    }
  }
  
  return { street1, street2, city, state, postalCode, country };
}

function parseDate(dateString: string): Date {
  // Convert M/D/YYYY to Date object
  const [month, day, year] = dateString.split('/').map(Number);
  return new Date(year, month - 1, day);
}

async function restoreMissingOrders() {
  console.log('🚀 Starting restoration of missing orders...\n');
  
  try {
    let restoredCount = 0;
    
    for (const orderData of missingOrdersData) {
      try {
        const address = parseAddress(orderData.address);
        const placedAt = parseDate(orderData.orderDate);
        
        // Calculate order totals
        const totalUsd = orderData.items.reduce((sum, item) => sum + item.totalDue, 0);
        const totalBtc = totalUsd * 0.00001; // Approximate BTC conversion
        const profitUsd = orderData.items.reduce((sum, item) => {
          const productInfo = productMapping[item.product];
          if (productInfo) {
            return sum + (productInfo.sellingPrice - productInfo.buyingPrice) * item.qty;
          }
          return sum + (item.unitPrice * 0.3); // Fallback 30% profit margin
        }, 0);
        const profitInr = profitUsd * 86; // Approximate USD to INR conversion
        const profitMargin = (profitUsd / totalUsd) * 100;
        
        // Create order
        const order = await prisma.order.create({
          data: {
            orderNumber: orderData.orderNumber,
            placedAt: placedAt,
            customerName: orderData.customerName,
            email: null,
            street1: address.street1,
            street2: address.street2,
            city: address.city,
            state: address.state,
            postalCode: address.postalCode,
            country: address.country,
            totalUsd: totalUsd,
            totalBtc: totalBtc,
            profitUsd: profitUsd,
            profitInr: profitInr,
            profitMargin: profitMargin,
            paymentMethod: 'BTC',
            paymentUrl: null,
            tracking17: null,
            trackingStatus: null,
            sentToSupplier: false,
            sentAt: null,
            status: 'pending',
            isEdited: false,
            editedAt: null,
            items: {
              create: orderData.items.map(item => {
                const productInfo = productMapping[item.product];
                return {
                  code: item.product,
                  dose: productInfo?.dose || 'Unknown',
                  qty: item.qty,
                  buyingPrice: productInfo?.buyingPrice || item.unitPrice * 0.7,
                  sellingPrice: productInfo?.sellingPrice || item.unitPrice,
                  variantId: null
                };
              })
            }
          },
          include: {
            items: true
          }
        });
        
        restoredCount++;
        console.log(`✅ Restored order ${orderData.orderNumber} (${restoredCount}/21)`);
        
      } catch (error) {
        console.error(`❌ Failed to restore order ${orderData.orderNumber}:`, error);
      }
    }
    
    // Final verification
    const totalOrders = await prisma.order.count();
    const latestOrder = await prisma.order.findFirst({
      orderBy: { orderNumber: 'desc' },
      select: { orderNumber: true, placedAt: true }
    });
    
    console.log(`\n🎉 Restoration completed!`);
    console.log(`📊 Final statistics:`);
    console.log(`  - Orders restored: ${restoredCount}/21`);
    console.log(`  - Total orders in database: ${totalOrders}`);
    console.log(`  - Latest order: ${latestOrder?.orderNumber} (${latestOrder?.placedAt})`);
    
    if (totalOrders === 88) {
      console.log(`\n🎊 SUCCESS! Production database now has all 88 orders!`);
    }
    
  } catch (error) {
    console.error('\n❌ Restoration failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

restoreMissingOrders();
