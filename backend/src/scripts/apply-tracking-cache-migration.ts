import { PrismaClient } from '../generated/prisma/index.js';

const prisma = new PrismaClient();

async function applyMigration() {
  try {
    console.log('Applying tracking cache migration...');

    // Create tracking_cache table
    await prisma.$executeRaw`
      CREATE TABLE IF NOT EXISTS "tracking_cache" (
        "tracking_number" VARCHAR(255) NOT NULL,
        "destination_country" VARCHAR(50) NOT NULL DEFAULT 'United States',
        "status_code" VARCHAR(2) NOT NULL,
        "status_text" VARCHAR(50) NOT NULL,
        "last_update" TIMESTAMP(3),
        "last_event_description" TEXT,
        "uuid" VARCHAR(255),
        "cached_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "expires_at" TIMESTAMP(3) NOT NULL,
        "is_final_status" BOOLEAN NOT NULL DEFAULT false,
        CONSTRAINT "tracking_cache_pkey" PRIMARY KEY ("tracking_number","destination_country")
      )
    `;
    console.log('✓ Created tracking_cache table');

    // Create tracking_metrics table
    await prisma.$executeRaw`
      CREATE TABLE IF NOT EXISTS "tracking_metrics" (
        "id" TEXT NOT NULL,
        "metric_type" VARCHAR(50) NOT NULL,
        "value" DECIMAL(10,2) NOT NULL,
        "metadata" JSONB,
        "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT "tracking_metrics_pkey" PRIMARY KEY ("id")
      )
    `;
    console.log('✓ Created tracking_metrics table');

    // Create indexes on tracking_cache
    await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS "tracking_cache_expires_at_idx" ON "tracking_cache"("expires_at")`;
    await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS "tracking_cache_is_final_status_idx" ON "tracking_cache"("is_final_status")`;
    console.log('✓ Created indexes on tracking_cache');

    // Create indexes on tracking_metrics
    await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS "tracking_metrics_created_at_idx" ON "tracking_metrics"("created_at")`;
    await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS "tracking_metrics_metric_type_idx" ON "tracking_metrics"("metric_type")`;
    console.log('✓ Created indexes on tracking_metrics');

    // Add indexes to orders table
    await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS "orders_tracking_17_idx" ON "orders"("tracking_17")`;
    await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS "orders_status_idx" ON "orders"("status")`;
    await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS "orders_placed_at_gz_idx" ON "orders"("placed_at_gz")`;
    await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS "orders_tracking_status_idx" ON "orders"("tracking_status")`;
    console.log('✓ Created indexes on orders table');

    // Composite index for common query patterns
    await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS "orders_status_tracking_idx" ON "orders"("status", "tracking_17") WHERE "tracking_17" IS NOT NULL`;
    console.log('✓ Created composite index on orders');

    console.log('\n✅ Migration applied successfully!');

    // Show current cache statistics
    const cacheCount = await prisma.trackingCache.count();
    const metricsCount = await prisma.trackingMetric.count();
    console.log(`\nCurrent statistics:`);
    console.log(`- Tracking cache entries: ${cacheCount}`);
    console.log(`- Tracking metrics: ${metricsCount}`);

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

applyMigration();