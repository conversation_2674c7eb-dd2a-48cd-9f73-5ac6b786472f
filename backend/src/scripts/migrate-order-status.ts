import { PrismaClient } from '../generated/prisma/index.js';

const prisma = new PrismaClient();

async function migrateOrderStatus() {
  console.log('Starting order status migration...');

  try {
    // First, add the status column to the database
    await prisma.$executeRawUnsafe(`
      DO $$
      BEGIN
        -- Create the enum type if it doesn't exist
        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'OrderStatus') THEN
          CREATE TYPE "OrderStatus" AS ENUM ('pending', 'sent', 'shipped');
        END IF;
        
        -- Add the status column if it doesn't exist
        IF NOT EXISTS (
          SELECT 1 FROM information_schema.columns 
          WHERE table_name = 'orders' AND column_name = 'status'
        ) THEN
          ALTER TABLE orders 
          ADD COLUMN status "OrderStatus" DEFAULT 'pending'::\"OrderStatus\";
        END IF;
      END $$;
    `);
    
    console.log('Status column added successfully');

    // Now migrate existing data
    // First, get all orders
    const orders = await prisma.order.findMany({
      select: {
        id: true,
        sentToSupplier: true,
        tracking17: true
      }
    });

    console.log(`Found ${orders.length} orders to migrate`);

    // Update each order's status based on current state
    let pendingCount = 0;
    let sentCount = 0;
    let shippedCount = 0;

    for (const order of orders) {
      let status: 'pending' | 'sent' | 'shipped' = 'pending';
      
      if (order.tracking17) {
        // If tracking exists, order is shipped
        status = 'shipped';
        shippedCount++;
      } else if (order.sentToSupplier) {
        // If sent to supplier but no tracking, order is sent
        status = 'sent';
        sentCount++;
      } else {
        // Otherwise, order is pending
        pendingCount++;
      }

      // Update the order with raw SQL to ensure enum value is set correctly
      await prisma.$executeRaw`
        UPDATE orders 
        SET status = ${status}::"OrderStatus"
        WHERE id = ${order.id}
      `;
    }

    console.log('Migration completed successfully!');
    console.log(`Orders migrated: ${pendingCount} pending, ${sentCount} sent, ${shippedCount} shipped`);

  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the migration
migrateOrderStatus()
  .then(() => {
    console.log('Migration script completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Migration script failed:', error);
    process.exit(1);
  });