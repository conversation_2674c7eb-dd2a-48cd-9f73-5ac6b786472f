import { PrismaClient } from '../generated/prisma/index.js';

const prisma = new PrismaClient();

async function countOrders() {
  try {
    const count = await prisma.order.count();
    console.log(`Current orders in database: ${count}`);
    
    const orders = await prisma.order.findMany({
      select: { orderNumber: true },
      orderBy: { orderNumber: 'asc' }
    });
    
    console.log('Order numbers:');
    orders.forEach(order => {
      console.log(order.orderNumber);
    });
    
  } catch (error) {
    console.error('Error counting orders:', error);
  } finally {
    await prisma.$disconnect();
  }
}

countOrders();