import axios from 'axios';

const API_BASE = 'http://localhost:3000/api';

async function createBulkTestOrders() {
  const orders = [
    {
      customerName: '<PERSON> - Bulk Test',
      street1: '123 Main Street',
      street2: 'Apt 4B',
      city: 'New York',
      state: 'NY',
      postalCode: '10001',
      country: 'USA',
      paymentMethod: 'BTC',
      paymentUrl: 'https://blockchain.info/tx/bulk-test-1',
      items: [
        { code: 'TR15', dose: '15 mg', qty: 2 },
        { code: 'SM20', dose: '20 mg', qty: 1 },
        { code: 'BA10', dose: '10 ml × 10 vials', qty: 2 }
      ]
    },
    {
      customerName: 'Sarah <PERSON>k Test',
      street1: '456 Oak Avenue',
      street2: null,
      city: 'Chicago',
      state: 'IL',
      postalCode: '60601',
      country: 'USA',
      paymentMethod: 'USDT',
      paymentUrl: 'https://blockchain.info/tx/bulk-test-2',
      items: [
        { code: 'RT20', dose: '20 mg', qty: 3 },
        { code: 'TR30', dose: '30 mg', qty: 1 }
      ]
    },
    {
      customerName: '<PERSON>k Test',
      street1: '789 Pine Road',
      street2: 'Suite 100',
      city: 'Seattle',
      state: 'WA',
      postalCode: '98101',
      country: 'USA',
      paymentMethod: 'USDC',
      paymentUrl: 'https://blockchain.info/tx/bulk-test-3',
      items: [
        { code: 'SM10', dose: '10 mg', qty: 4 },
        { code: 'RT15', dose: '15 mg', qty: 2 },
        { code: 'BA10', dose: '10 ml × 10 vials', qty: 1 }
      ]
    }
  ];

  const createdOrderIds = [];

  try {
    for (const orderData of orders) {
      const response = await axios.post(`${API_BASE}/orders`, orderData);
      createdOrderIds.push(response.data.order.id);
      console.log(`✅ Created order for ${orderData.customerName}: ${response.data.order.id}`);
    }

    console.log('\n📦 All orders created successfully!');
    console.log('Order IDs:', createdOrderIds);

    // Now send bulk export email
    console.log('\n📧 Sending bulk export email...');
    const bulkResponse = await axios.post(`${API_BASE}/exports/bulk/send`, {
      orderIds: createdOrderIds
    });

    console.log('✅ Bulk email sent successfully!');
    console.log(`Sent ${bulkResponse.data.orders.length} orders to supplier`);

  } catch (error: any) {
    console.error('❌ Error:', error.response?.data || error.message);
  }
}

createBulkTestOrders();