import { PrismaClient } from '../generated/prisma/index.js';
import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { config } from '../config.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const prisma = new PrismaClient();

interface PricingData {
  [product: string]: Array<{
    code: string;
    dose: string;
    buying_price: number;
    selling_price: number;
  }>;
}

async function main() {
  console.log(`Starting seed in ${config.env.toUpperCase()} environment...`);
  console.log(`Database: ${config.database.url.split('@')[1]}`);
  
  if (config.isProduction) {
    console.warn('⚠️  WARNING: You are about to seed the PRODUCTION database!');
    console.warn('This will DELETE all existing pricing data.');
    const readline = await import('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
    
    await new Promise<void>((resolve) => {
      rl.question('Type "PRODUCTION" to confirm: ', (answer) => {
        if (answer !== 'PRODUCTION') {
          console.log('Seed cancelled.');
          process.exit(0);
        }
        rl.close();
        resolve();
      });
    });
  }

  // Read pricing data
  const pricingDataPath = join(__dirname, 'pricing.json');
  const pricingData: PricingData = JSON.parse(readFileSync(pricingDataPath, 'utf-8'));

  // Clear existing pricing data
  await prisma.pricing.deleteMany();
  console.log('Cleared existing pricing data');

  // Insert pricing data
  let totalRecords = 0;
  for (const [product, items] of Object.entries(pricingData)) {
    for (const item of items) {
      await prisma.pricing.create({
        data: {
          code: item.code,
          product: product,
          dose: item.dose,
          buyingPrice: item.buying_price,
          sellingPrice: item.selling_price,
        },
      });
      totalRecords++;
    }
  }

  console.log(`Seeded ${totalRecords} pricing records`);

  // Seed initial exchange rates (placeholder)
  await prisma.exchangeRate.upsert({
    where: { pair: 'BTC-USD' },
    update: { rate: 100000 }, // Placeholder rate
    create: {
      pair: 'BTC-USD',
      rate: 100000,
    },
  });

  await prisma.exchangeRate.upsert({
    where: { pair: 'BTC-INR' },
    update: { rate: 8600000 }, // Placeholder rate
    create: {
      pair: 'BTC-INR',
      rate: 8600000,
    },
  });

  console.log('Seeded exchange rates');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });