import { PrismaClient } from '../generated/prisma/index.js';
import { config } from '../config.js';

const prisma = new PrismaClient();

// SAFETY: This script should NEVER run in production
if (config.isProduction) {
  console.error('❌ CRITICAL ERROR: Cannot delete orders in production!');
  console.error('This script is for development use only.');
  process.exit(1);
}

async function deleteAllOrders() {
  console.log('Starting to delete all orders from the database...');
  
  try {
    // Start a transaction to ensure data consistency
    const result = await prisma.$transaction(async (tx) => {
      // First, delete all order items (due to foreign key constraints)
      const deletedOrderItems = await tx.orderItem.deleteMany();
      console.log(`Deleted ${deletedOrderItems.count} order items`);
      
      // Then, delete all orders
      const deletedOrders = await tx.order.deleteMany();
      console.log(`Deleted ${deletedOrders.count} orders`);
      
      return {
        orderItems: deletedOrderItems.count,
        orders: deletedOrders.count
      };
    });
    
    console.log('\n✅ Successfully deleted all orders!');
    console.log(`Summary:`);
    console.log(`- Order items deleted: ${result.orderItems}`);
    console.log(`- Orders deleted: ${result.orders}`);
    
  } catch (error) {
    console.error('❌ Error deleting orders:', error);
    throw error;
  }
}

async function main() {
  console.log(`Connecting to database in ${config.env.toUpperCase()} environment...`);
  console.log(`Database: ${config.database.url.split('@')[1]}\n`);
  
  try {
    await deleteAllOrders();
  } catch (error) {
    console.error('Failed to delete orders:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
    console.log('\nDisconnected from database');
  }
}

// Run the script
main();