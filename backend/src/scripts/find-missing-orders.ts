import { PrismaClient } from '../generated/prisma/index.js';

const prisma = new PrismaClient();

async function findMissingOrders() {
  try {
    // Get all current order numbers
    const currentOrders = await prisma.order.findMany({
      select: { orderNumber: true },
      orderBy: { orderNumber: 'asc' }
    });
    
    const currentOrderNumbers = currentOrders.map(order => order.orderNumber);
    
    // Expected orders from backup (ORD-2025-0001 to ORD-2025-0088)
    const expectedOrders = [];
    for (let i = 1; i <= 88; i++) {
      expectedOrders.push(`ORD-2025-${i.toString().padStart(4, '0')}`);
    }
    
    console.log('Current orders count:', currentOrderNumbers.length);
    console.log('Expected orders count:', expectedOrders.length);
    
    // Find missing orders
    const missingOrders = expectedOrders.filter(orderNum => 
      !currentOrderNumbers.includes(orderNum)
    );
    
    console.log('\nMissing orders:');
    missingOrders.forEach(orderNum => {
      console.log(orderNum);
    });
    
    console.log(`\nTotal missing orders: ${missingOrders.length}`);
    
    // Find extra orders (those beyond ORD-2025-0088)
    const extraOrders = currentOrderNumbers.filter(orderNum => 
      !expectedOrders.includes(orderNum)
    );
    
    console.log('\nExtra orders (beyond expected range):');
    extraOrders.forEach(orderNum => {
      console.log(orderNum);
    });
    
  } catch (error) {
    console.error('Error finding missing orders:', error);
  } finally {
    await prisma.$disconnect();
  }
}

findMissingOrders();