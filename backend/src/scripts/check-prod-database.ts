import { PrismaClient } from '../generated/prisma/index.js';

const prisma = new PrismaClient({
  datasources: {
    db: {
      url: 'postgresql://sagar@localhost:5432/peptide_portal_prod'
    }
  }
});

async function checkProdDatabase() {
  try {
    const count = await prisma.order.count();
    console.log(`Production database orders count: ${count}`);
    
    if (count > 0) {
      const orders = await prisma.order.findMany({
        select: { orderNumber: true },
        orderBy: { orderNumber: 'asc' }
      });
      
      console.log('Production database order numbers:');
      orders.forEach(order => {
        console.log(order.orderNumber);
      });
      
      const highestOrder = orders[orders.length - 1];
      console.log(`\nHighest order in production: ${highestOrder.orderNumber}`);
    }
    
  } catch (error) {
    console.error('Error checking production database:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkProdDatabase();