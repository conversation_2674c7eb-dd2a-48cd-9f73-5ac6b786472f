import { PrismaClient } from '../generated/prisma/index.js';

const prisma = new PrismaClient();

async function checkOrderSequence() {
  try {
    const orders = await prisma.order.findMany({
      select: { orderNumber: true },
      orderBy: { orderNumber: 'asc' }
    });
    
    console.log('Current order sequence:');
    orders.forEach((order, index) => {
      const expectedNum = index + 1;
      const actualNum = parseInt(order.orderNumber.split('-')[2]);
      const isSequential = expectedNum === actualNum;
      
      console.log(`${order.orderNumber} - ${isSequential ? 'OK' : 'GAP'}`);
    });
    
    console.log(`\nTotal orders: ${orders.length}`);
    
    // Check for gaps
    let hasGaps = false;
    for (let i = 0; i < orders.length - 1; i++) {
      const currentNum = parseInt(orders[i].orderNumber.split('-')[2]);
      const nextNum = parseInt(orders[i + 1].orderNumber.split('-')[2]);
      
      if (nextNum !== currentNum + 1) {
        console.log(`GAP found: ${orders[i].orderNumber} -> ${orders[i + 1].orderNumber}`);
        hasGaps = true;
      }
    }
    
    if (!hasGaps) {
      console.log('No gaps found in sequence!');
    }
    
  } catch (error) {
    console.error('Error checking order sequence:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkOrderSequence();