import * as dotenv from 'dotenv';
import { SeventeenTrackService } from '../services/seventeentrack.service.js';

// Load environment variables
dotenv.config({ path: '.env' });

// Mock fastify instance
const mockFastify = {
  log: {
    info: (message: string, ...args: any[]) => console.log('INFO:', message, ...args),
    warn: (message: string, ...args: any[]) => console.warn('WARN:', message, ...args),
    error: (message: string, ...args: any[]) => console.error('ERROR:', message, ...args),
  },
  config: {
    SEVENTEEN_TRACK_API_KEY: process.env.SEVENTEEN_TRACK_API_KEY || ''
  }
} as any;

async function testTrackingAPI() {
  console.log('🚀 Testing 17track API Integration\n');
  
  const apiKey = process.env.SEVENTEEN_TRACK_API_KEY || '';
  
  if (!apiKey) {
    console.error('❌ SEVENTEEN_TRACK_API_KEY not found in .env file');
    return;
  }
  
  console.log('✅ API Key found:', apiKey.substring(0, 8) + '...');
  
  const service = new SeventeenTrackService(mockFastify, apiKey);
  
  // Test tracking numbers
  const testTrackingNumbers = [
    '390434798633',  // Real tracking number that shows as delivered
    'RR123456789CN',  // Example China Post tracking
    '1Z999AA1012345678',  // Example UPS tracking
    '9405511899223197428490',  // Example USPS tracking
  ];
  
  console.log('\n📦 Testing Tracking Number Registration:');
  for (const trackingNumber of testTrackingNumbers) {
    console.log(`\nRegistering: ${trackingNumber}`);
    try {
      await service.registerTrackingNumber(trackingNumber);
      console.log('✅ Registration completed');
    } catch (error) {
      console.error('❌ Registration failed:', error);
    }
  }
  
  console.log('\n🔍 Testing Tracking Status Retrieval:');
  for (const trackingNumber of testTrackingNumbers) {
    console.log(`\nChecking status for: ${trackingNumber}`);
    try {
      const status = await service.getTrackingStatus(trackingNumber);
      if (status) {
        console.log('✅ Status retrieved:');
        console.log('  - Status:', status.statusText);
        console.log('  - Code:', status.status);
        console.log('  - Last Update:', status.lastUpdate);
        console.log('  - Tracking URL:', service.generateTrackingUrl(trackingNumber));
      } else {
        console.log('❌ No status returned');
      }
    } catch (error) {
      console.error('❌ Status check failed:', error);
    }
  }
  
  console.log('\n✨ Testing complete!');
}

// Run the test
testTrackingAPI().catch(console.error);