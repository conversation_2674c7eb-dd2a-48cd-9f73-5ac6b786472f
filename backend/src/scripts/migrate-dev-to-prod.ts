import { PrismaClient } from '../generated/prisma/index.js';
import { config } from '../config.js';

// Create separate Prisma clients for dev and prod databases
const devPrisma = new PrismaClient({
  datasources: {
    db: {
      url: 'postgresql://sagar@localhost:5432/peptide_portal_dev'
    }
  }
});

const prodPrisma = new PrismaClient({
  datasources: {
    db: {
      url: 'postgresql://sagar@localhost:5432/peptide_portal_prod'
    }
  }
});

async function runMigration() {
  console.log('🚀 Starting Dev to Prod Database Migration...\n');
  
  try {
    // Step 1: Create tracking_cache table
    console.log('1️⃣ Creating tracking_cache table...');
    await prodPrisma.$executeRaw`
      CREATE TABLE IF NOT EXISTS "tracking_cache" (
        "tracking_number" VARCHAR(255) NOT NULL,
        "destination_country" VARCHAR(50) NOT NULL DEFAULT 'United States',
        "status_code" VARCHAR(2) NOT NULL,
        "status_text" VARCHAR(50) NOT NULL,
        "last_update" TIMESTAMP(3),
        "last_event_description" TEXT,
        "uuid" VARCHAR(255),
        "cached_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "expires_at" TIMESTAMP(3) NOT NULL,
        "is_final_status" BOOLEAN NOT NULL DEFAULT false,
        CONSTRAINT "tracking_cache_pkey" PRIMARY KEY ("tracking_number","destination_country")
      )
    `;
    console.log('✅ tracking_cache table created');

    // Step 2: Create tracking_metrics table
    console.log('\n2️⃣ Creating tracking_metrics table...');
    await prodPrisma.$executeRaw`
      CREATE TABLE IF NOT EXISTS "tracking_metrics" (
        "id" TEXT NOT NULL,
        "metric_type" VARCHAR(50) NOT NULL,
        "value" DECIMAL(10,2) NOT NULL,
        "metadata" JSONB,
        "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT "tracking_metrics_pkey" PRIMARY KEY ("id")
      )
    `;
    console.log('✅ tracking_metrics table created');

    // Step 3: Create indexes on tracking tables
    console.log('\n3️⃣ Creating indexes on tracking tables...');
    
    await prodPrisma.$executeRaw`CREATE INDEX IF NOT EXISTS "tracking_cache_expires_at_idx" ON "tracking_cache"("expires_at")`;
    await prodPrisma.$executeRaw`CREATE INDEX IF NOT EXISTS "tracking_cache_is_final_status_idx" ON "tracking_cache"("is_final_status")`;
    console.log('✅ tracking_cache indexes created');

    await prodPrisma.$executeRaw`CREATE INDEX IF NOT EXISTS "tracking_metrics_created_at_idx" ON "tracking_metrics"("created_at")`;
    await prodPrisma.$executeRaw`CREATE INDEX IF NOT EXISTS "tracking_metrics_metric_type_idx" ON "tracking_metrics"("metric_type")`;
    console.log('✅ tracking_metrics indexes created');

    // Step 4: Add missing indexes to orders table
    console.log('\n4️⃣ Adding missing indexes to orders table...');
    
    await prodPrisma.$executeRaw`CREATE INDEX IF NOT EXISTS "orders_tracking_17_idx" ON "orders"("tracking_17")`;
    await prodPrisma.$executeRaw`CREATE INDEX IF NOT EXISTS "orders_status_idx" ON "orders"("status")`;
    await prodPrisma.$executeRaw`CREATE INDEX IF NOT EXISTS "orders_placed_at_gz_idx" ON "orders"("placed_at_gz")`;
    await prodPrisma.$executeRaw`CREATE INDEX IF NOT EXISTS "orders_tracking_status_idx" ON "orders"("tracking_status")`;
    console.log('✅ Basic orders indexes created');

    await prodPrisma.$executeRaw`CREATE INDEX IF NOT EXISTS "orders_status_tracking_idx" ON "orders"("status", "tracking_17") WHERE "tracking_17" IS NOT NULL`;
    console.log('✅ Composite orders index created');

    // Step 5: Migrate tracking_cache data
    console.log('\n5️⃣ Migrating tracking_cache data...');
    
    const trackingCacheData = await devPrisma.$queryRaw<any[]>`
      SELECT * FROM tracking_cache
    `;
    
    console.log(`Found ${trackingCacheData.length} tracking_cache entries to migrate`);
    
    if (trackingCacheData.length > 0) {
      for (const entry of trackingCacheData) {
        await prodPrisma.$executeRaw`
          INSERT INTO tracking_cache (
            tracking_number, destination_country, status_code, status_text,
            last_update, last_event_description, uuid, cached_at, expires_at, is_final_status
          ) VALUES (
            ${entry.tracking_number}, ${entry.destination_country}, ${entry.status_code}, ${entry.status_text},
            ${entry.last_update}, ${entry.last_event_description}, ${entry.uuid}, 
            ${entry.cached_at}, ${entry.expires_at}, ${entry.is_final_status}
          ) ON CONFLICT (tracking_number, destination_country) DO UPDATE SET
            status_code = EXCLUDED.status_code,
            status_text = EXCLUDED.status_text,
            last_update = EXCLUDED.last_update,
            last_event_description = EXCLUDED.last_event_description,
            uuid = EXCLUDED.uuid,
            cached_at = EXCLUDED.cached_at,
            expires_at = EXCLUDED.expires_at,
            is_final_status = EXCLUDED.is_final_status
        `;
      }
      console.log(`✅ ${trackingCacheData.length} tracking_cache entries migrated`);
    }

    // Step 6: Migrate tracking_metrics data
    console.log('\n6️⃣ Migrating tracking_metrics data...');
    
    const trackingMetricsData = await devPrisma.$queryRaw<any[]>`
      SELECT * FROM tracking_metrics
    `;
    
    console.log(`Found ${trackingMetricsData.length} tracking_metrics entries to migrate`);
    
    if (trackingMetricsData.length > 0) {
      for (const entry of trackingMetricsData) {
        await prodPrisma.$executeRaw`
          INSERT INTO tracking_metrics (
            id, metric_type, value, metadata, created_at
          ) VALUES (
            ${entry.id}, ${entry.metric_type}, ${entry.value}, 
            ${entry.metadata}::jsonb, ${entry.created_at}
          ) ON CONFLICT (id) DO UPDATE SET
            metric_type = EXCLUDED.metric_type,
            value = EXCLUDED.value,
            metadata = EXCLUDED.metadata,
            created_at = EXCLUDED.created_at
        `;
      }
      console.log(`✅ ${trackingMetricsData.length} tracking_metrics entries migrated`);
    }

    // Step 7: Verification
    console.log('\n7️⃣ Verifying migration...');
    
    const prodTrackingCacheCount = await prodPrisma.$queryRaw<[{count: bigint}]>`SELECT COUNT(*) as count FROM tracking_cache`;
    const prodTrackingMetricsCount = await prodPrisma.$queryRaw<[{count: bigint}]>`SELECT COUNT(*) as count FROM tracking_metrics`;
    
    console.log(`Production tracking_cache entries: ${prodTrackingCacheCount[0].count}`);
    console.log(`Production tracking_metrics entries: ${prodTrackingMetricsCount[0].count}`);
    
    // Verify table structures match
    const prodTables = await prodPrisma.$queryRaw<any[]>`
      SELECT table_name FROM information_schema.tables 
      WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
      ORDER BY table_name
    `;
    
    console.log('\nProduction database tables:');
    prodTables.forEach(table => console.log(`  - ${table.table_name}`));
    
    console.log('\n🎉 Migration completed successfully!');
    console.log('\n📊 Final Statistics:');
    console.log(`  - Total tables in production: ${prodTables.length}`);
    console.log(`  - Tracking cache entries: ${prodTrackingCacheCount[0].count}`);
    console.log(`  - Tracking metrics entries: ${prodTrackingMetricsCount[0].count}`);

  } catch (error) {
    console.error('\n❌ Migration failed:', error);
    console.error('\n⚠️  The migration was interrupted. Production database state may be partially updated.');
    console.error('Please review the error and run the migration again if needed.');
    process.exit(1);
  } finally {
    await devPrisma.$disconnect();
    await prodPrisma.$disconnect();
  }
}

async function main() {
  console.log('🔒 Database Migration Safety Check');
  console.log('This will migrate dev database changes to production.');
  console.log('Fresh backups should be available before proceeding.\n');
  
  const readline = await import('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  await new Promise<void>((resolve) => {
    rl.question('Type "MIGRATE" to confirm you want to proceed: ', (answer) => {
      if (answer !== 'MIGRATE') {
        console.log('Migration cancelled.');
        process.exit(0);
      }
      rl.close();
      resolve();
    });
  });

  await runMigration();
}

main();