import axios from 'axios';

const API_URL = 'http://localhost:3000/api';

// Test data
const testProduct = {
  product: 'Test Product',
  dose: '10 mg',
  code: 'TEST10',
  buyingPrice: 50,
  sellingPrice: 100
};

const testProductUpdate = {
  dose: '20 mg',
  sellingPrice: 120
};

const bulkProducts = [
  {
    product: 'Bulk Product 1',
    dose: '5 mg',
    code: 'BULK1',
    buyingPrice: 30,
    sellingPrice: 60
  },
  {
    product: 'Bulk Product 2',
    dose: '10 mg',
    code: 'BULK2',
    buyingPrice: 40,
    sellingPrice: 80
  }
];

async function testPricingCRUD() {
  console.log('🧪 Testing Pricing CRUD Operations...\n');

  try {
    // 1. Test GET all products
    console.log('1️⃣ Testing GET /api/pricing');
    const getResponse = await axios.get(`${API_URL}/pricing`);
    console.log(`✅ Found ${getResponse.data.pricing.length} products\n`);

    // 2. Test CREATE product
    console.log('2️⃣ Testing POST /api/pricing');
    try {
      const createResponse = await axios.post(`${API_URL}/pricing`, testProduct);
      console.log('✅ Product created:', createResponse.data.product);
    } catch (error: any) {
      if (error.response?.status === 409) {
        console.log('⚠️ Product already exists, continuing with tests...');
      } else {
        throw error;
      }
    }
    console.log();

    // 3. Test UPDATE product
    console.log('3️⃣ Testing PUT /api/pricing/:code');
    const updateResponse = await axios.put(`${API_URL}/pricing/${testProduct.code}`, testProductUpdate);
    console.log('✅ Product updated:', updateResponse.data.product);
    console.log();

    // 4. Test validation errors
    console.log('4️⃣ Testing validation errors');
    try {
      await axios.post(`${API_URL}/pricing`, {
        product: '',
        dose: '10 mg',
        code: 'INVALID',
        buyingPrice: 100,
        sellingPrice: 50 // selling < buying
      });
    } catch (error: any) {
      if (error.response?.status === 400) {
        console.log('✅ Validation errors caught correctly:', error.response.data.validationErrors);
      }
    }
    console.log();

    // 5. Test BULK import
    console.log('5️⃣ Testing POST /api/pricing/bulk (append mode)');
    // First, delete bulk products if they exist
    for (const product of bulkProducts) {
      try {
        await axios.delete(`${API_URL}/pricing/${product.code}`);
      } catch (error) {
        // Ignore if doesn't exist
      }
    }
    
    const bulkResponse = await axios.post(`${API_URL}/pricing/bulk`, {
      mode: 'append',
      products: bulkProducts
    });
    console.log('✅ Bulk import result:', bulkResponse.data);
    console.log();

    // 6. Test EXPORT
    console.log('6️⃣ Testing GET /api/pricing/export');
    
    // Test JSON export
    const jsonExport = await axios.get(`${API_URL}/pricing/export?format=json`);
    console.log(`✅ JSON export: ${jsonExport.data.pricing.length} products`);
    
    // Test CSV export
    const csvExport = await axios.get(`${API_URL}/pricing/export?format=csv`);
    console.log('✅ CSV export (first 3 lines):');
    console.log(csvExport.data.split('\n').slice(0, 3).join('\n'));
    console.log();

    // 7. Test DELETE product
    console.log('7️⃣ Testing DELETE /api/pricing/:code');
    
    // First, ensure the product is not used in any order
    const deleteResponse = await axios.delete(`${API_URL}/pricing/${testProduct.code}`);
    console.log('✅ Product deleted successfully');
    console.log();

    // 8. Test deletion prevention for products in use
    console.log('8️⃣ Testing deletion prevention for products in orders');
    console.log('⚠️ This test requires a product that is used in an existing order');
    console.log('Skipping this test in automated script...\n');

    // 9. Clean up - delete bulk products
    console.log('9️⃣ Cleaning up test data...');
    for (const product of bulkProducts) {
      try {
        await axios.delete(`${API_URL}/pricing/${product.code}`);
        console.log(`✅ Deleted ${product.code}`);
      } catch (error) {
        console.log(`⚠️ Could not delete ${product.code}`);
      }
    }

    console.log('\n✅ All tests completed successfully!');

  } catch (error: any) {
    console.error('\n❌ Test failed:', error.response?.data || error.message);
    process.exit(1);
  }
}

// Run tests
testPricingCRUD().catch(console.error);