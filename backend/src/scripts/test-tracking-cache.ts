import { PrismaClient } from '../generated/prisma/index.js';
import { FastifyInstance } from 'fastify';
import { EnhancedTrackingService } from '../services/enhancedTracking.service.js';

const prisma = new PrismaClient();

// Mock Fastify instance
const mockFastify = {
  log: {
    info: (...args: any[]) => console.log('INFO:', ...args),
    error: (...args: any[]) => console.log('ERROR:', ...args),
    warn: (...args: any[]) => console.log('WARN:', ...args),
  },
  config: {
    PARCELS_APP_API_KEY: process.env.PARCELS_APP_API_KEY || ''
  }
} as unknown as FastifyInstance;

async function testTrackingCache() {
  console.log('🚀 Testing Enhanced Tracking Service...\n');

  const service = new EnhancedTrackingService(mockFastify, mockFastify.config.PARCELS_APP_API_KEY, prisma);

  try {
    // Test 1: First API call (cache miss)
    console.log('📝 Test 1: First API call for tracking number...');
    const trackingNumber = '1Z999AA10123456784';  // Example UPS tracking
    const result1 = await service.getTrackingStatus(trackingNumber, 'United States');
    console.log('Result:', result1);
    console.log('✅ First call completed (should be cache miss)\n');

    // Wait a bit
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Test 2: Second call (cache hit)
    console.log('📝 Test 2: Second call for same tracking number...');
    const result2 = await service.getTrackingStatus(trackingNumber, 'United States');
    console.log('Result:', result2);
    console.log('✅ Second call completed (should be cache hit)\n');

    // Test 3: Get cache statistics
    console.log('📊 Cache Statistics:');
    const stats = await service.getCacheStats();
    console.log('- Total Hits:', stats.metrics.hits);
    console.log('- Total Misses:', stats.metrics.misses);
    console.log('- Hit Rate:', stats.metrics.hitRate.toFixed(2) + '%');
    console.log('- Cache Size:', stats.metrics.cacheSize);
    console.log('- API Calls Made:', stats.metrics.apiCalls);
    console.log('\n📈 Cache by Status:');
    stats.cacheByStatus.forEach((status: any) => {
      console.log(`  - ${status.statusText}: ${status._count} entries`);
    });

    // Test 4: Bulk tracking
    console.log('\n📝 Test 4: Bulk tracking status...');
    const bulkTrackingNumbers = [
      { trackingNumber: '1Z999AA10123456784', destinationCountry: 'United States' },
      { trackingNumber: '9400111899562167892493', destinationCountry: 'United States' },
      { trackingNumber: '420941159505500127317160688571', destinationCountry: 'United States' }
    ];
    
    const bulkResults = await service.bulkGetTrackingStatuses(bulkTrackingNumbers);
    console.log('Bulk results:', bulkResults.size, 'tracking numbers processed');
    bulkResults.forEach((status, trackingNumber) => {
      console.log(`  - ${trackingNumber}: ${status?.statusText || 'Not Found'}`);
    });

    // Test 5: Check DB cache persistence
    console.log('\n📝 Test 5: Checking database cache persistence...');
    const dbCacheEntries = await prisma.trackingCache.findMany({
      take: 10
    });
    console.log(`Found ${dbCacheEntries.length} entries in DB cache:`);
    dbCacheEntries.forEach(entry => {
      console.log(`  - ${entry.trackingNumber}: ${entry.statusText} (expires: ${entry.expiresAt.toISOString()})`);
    });

    // Stop background jobs
    service.stopBackgroundJobs();
    console.log('\n✅ All tests completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testTrackingCache();