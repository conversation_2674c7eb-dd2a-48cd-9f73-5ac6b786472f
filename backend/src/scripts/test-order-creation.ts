import { PrismaClient } from '../generated/prisma/index.js';

const prisma = new PrismaClient();

async function testOrderCreation() {
  try {
    // First, let's check if we have pricing data
    const pricingCount = await prisma.pricing.count();
    console.log(`Found ${pricingCount} pricing items in database`);
    
    if (pricingCount === 0) {
      console.error('No pricing data found! Run npm run db:seed first');
      return;
    }
    
    // Get a sample pricing item
    const samplePricing = await prisma.pricing.findFirst({
      where: {
        product: 'Tirzepatide',
        dose: '10 mg'
      }
    });
    
    if (!samplePricing) {
      console.error('Could not find Tirzepatide 10mg pricing');
      return;
    }
    
    console.log('Sample pricing:', samplePricing);
    
    // Test order data
    const testOrderData = {
      customerName: 'Test Customer John',
      street1: '456 Test Avenue',
      street2: '',
      city: 'San Francisco',
      state: 'CA',
      postalCode: '94102',
      country: 'United States',
      paymentMethod: 'BTC' as const,
      paymentUrl: '',
      items: [
        {
          code: 'Tirzepatide',
          dose: '10 mg',
          qty: 2
        }
      ]
    };
    
    console.log('Attempting to create order with data:', JSON.stringify(testOrderData, null, 2));
    
    // Try to create an order directly
    const order = await prisma.order.create({
      data: {
        orderNumber: 'ORD-2024-9999', // Test order number
        customerName: testOrderData.customerName,
        street1: testOrderData.street1,
        street2: testOrderData.street2,
        city: testOrderData.city,
        state: testOrderData.state,
        postalCode: testOrderData.postalCode,
        country: testOrderData.country,
        paymentMethod: testOrderData.paymentMethod,
        paymentUrl: testOrderData.paymentUrl || null,
        totalUsd: 269.66, // Pre-calculated for testing
        totalBtc: 0.00269660,
        profitUsd: 58.00,
        profitInr: 4988.00,
        profitMargin: 24.37,
        sentToSupplier: false,
        placedAt: new Date(),
        items: {
          create: [
            {
              code: 'TR10',
              dose: '10 mg',
              qty: 2,
              buyingPrice: 70.00,
              sellingPrice: 119.00
            }
          ]
        }
      },
      include: {
        items: true
      }
    });
    
    console.log('Order created successfully!');
    console.log('Order ID:', order.id);
    console.log('Order details:', JSON.stringify(order, null, 2));
    
  } catch (error) {
    console.error('Failed to create order:', error);
    if (error instanceof Error) {
      console.error('Error message:', error.message);
      console.error('Error stack:', error.stack);
    }
  } finally {
    await prisma.$disconnect();
  }
}

testOrderCreation();