import { PrismaClient } from '../generated/prisma/index.js';

const prisma = new PrismaClient();

async function checkTrackingMetrics() {
  console.log('📊 Tracking Cache Analysis\n');

  try {
    // 1. Check cache entries
    const cacheEntries = await prisma.trackingCache.findMany({
      orderBy: { cachedAt: 'desc' }
    });

    console.log(`📦 Cache Entries (${cacheEntries.length} total):`);
    cacheEntries.forEach(entry => {
      const remainingTTL = Math.round((entry.expiresAt.getTime() - Date.now()) / 1000 / 60);
      console.log(`- ${entry.trackingNumber}: ${entry.statusText}`);
      console.log(`  • Cached: ${entry.cachedAt.toISOString()}`);
      console.log(`  • Expires: ${entry.expiresAt.toISOString()} (${remainingTTL} min remaining)`);
      console.log(`  • Final Status: ${entry.isFinalStatus}`);
      console.log(`  • UUID: ${entry.uuid || 'None'}`);
      console.log('');
    });

    // 2. Check recent metrics
    const recentMetrics = await prisma.trackingMetric.groupBy({
      by: ['metricType'],
      _count: true,
      _sum: {
        value: true
      }
    });

    console.log('📈 Metrics Summary:');
    recentMetrics.forEach(metric => {
      console.log(`- ${metric.metricType}: ${metric._count} events, total value: ${metric._sum.value}`);
    });

    // 3. Calculate cache hit rate
    const hits = recentMetrics.find(m => m.metricType === 'cache_hit')?._count || 0;
    const misses = recentMetrics.find(m => m.metricType === 'cache_miss')?._count || 0;
    const total = hits + misses;
    const hitRate = total > 0 ? (hits / total * 100).toFixed(2) : '0.00';

    console.log(`\n🎯 Cache Performance:`);
    console.log(`- Hit Rate: ${hitRate}%`);
    console.log(`- Total Requests: ${total}`);
    console.log(`- Cache Hits: ${hits}`);
    console.log(`- Cache Misses: ${misses}`);

    // 4. Check orders with tracking
    const ordersWithTracking = await prisma.order.count({
      where: {
        tracking17: { not: null }
      }
    });

    const deliveredOrders = await prisma.order.count({
      where: {
        trackingStatus: 'Delivered'
      }
    });

    console.log(`\n📦 Order Statistics:`);
    console.log(`- Orders with tracking: ${ordersWithTracking}`);
    console.log(`- Delivered orders: ${deliveredOrders}`);

    // 5. Check for potential cache warming candidates
    const recentOrdersNoCache = await prisma.$queryRaw`
      SELECT o.id, o.order_number, o.tracking_17, o.tracking_status
      FROM orders o
      LEFT JOIN tracking_cache tc 
        ON o.tracking_17 = tc.tracking_number 
        AND o.country = tc.destination_country
      WHERE o.tracking_17 IS NOT NULL
        AND o.status != 'delivered'
        AND tc.tracking_number IS NULL
      LIMIT 10
    ` as any[];

    console.log(`\n🔥 Cache Warming Candidates (${recentOrdersNoCache.length}):`);
    recentOrdersNoCache.forEach(order => {
      console.log(`- ${order.order_number}: ${order.tracking_17} (${order.tracking_status || 'No status'})`);
    });

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkTrackingMetrics();