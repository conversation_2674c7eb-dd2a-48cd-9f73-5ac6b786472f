import { PrismaClient } from '../generated/prisma/index.js';

const prisma = new PrismaClient();

async function checkRecentOrders() {
  try {
    const recentOrders = await prisma.order.findMany({
      select: {
        orderNumber: true,
        placedAt: true,
        customerName: true,
        totalUsd: true,
        status: true
      },
      orderBy: { placedAt: 'desc' },
      take: 10
    });
    
    console.log('Most recent orders:');
    recentOrders.forEach(order => {
      console.log(`${order.orderNumber} - ${order.placedAt} - ${order.customerName} - $${order.totalUsd} - ${order.status}`);
    });
    
    // Check orders from the backup range
    const backupRangeOrders = await prisma.order.findMany({
      select: {
        orderNumber: true,
        placedAt: true,
        customerName: true
      },
      where: {
        orderNumber: {
          in: ['ORD-2025-0086', 'ORD-2025-0087', 'ORD-2025-0088', 'ORD-2025-0089']
        }
      },
      orderBy: { orderNumber: 'asc' }
    });
    
    console.log('\nOrders in the backup range and beyond:');
    backupRangeOrders.forEach(order => {
      console.log(`${order.orderNumber} - ${order.placedAt} - ${order.customerName} - $${order.totalUsd} - ${order.status}`);
    });
    
  } catch (error) {
    console.error('Error checking recent orders:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkRecentOrders();