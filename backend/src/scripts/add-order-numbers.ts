import { PrismaClient } from '../generated/prisma/index.js';

const prisma = new PrismaClient();

async function addOrderNumbers() {
  try {
    // First, fetch all existing orders
    const orders = await prisma.order.findMany({
      orderBy: { placedAt: 'asc' }
    });

    console.log(`Found ${orders.length} existing orders`);

    // Update each order with a sequential order number
    let orderCounter = 1;
    for (const order of orders) {
      const year = new Date(order.placedAt).getFullYear();
      const orderNumber = `ORD-${year}-${String(orderCounter).padStart(4, '0')}`;
      
      console.log(`Updating order ${order.id} with order number: ${orderNumber}`);
      
      // Update the order with the new order number
      await prisma.order.update({
        where: { id: order.id },
        data: { orderNumber }
      });
      
      orderCounter++;
    }

    console.log(`\nSuccessfully updated ${orders.length} orders with new order numbers.`);

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

addOrderNumbers();