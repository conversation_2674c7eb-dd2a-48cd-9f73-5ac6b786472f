// Test order calculations without database
import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load pricing data
const pricingData = JSON.parse(readFileSync(join(__dirname, 'pricing.json'), 'utf-8'));

// Mock BTC rate
const BTC_RATE = 100000; // $100,000 per BTC

interface OrderItem {
  code: string;
  dose: string;
  qty: number;
}

interface PricingItem {
  code: string;
  dose: string;
  buying_price: number;
  selling_price: number;
}

function findPricing(code: string, dose: string): PricingItem | undefined {
  for (const [product, items] of Object.entries(pricingData)) {
    const item = (items as PricingItem[]).find(i => i.code === code && i.dose === dose);
    if (item) return item;
  }
  return undefined;
}

function calculateOrder(items: OrderItem[]) {
  console.log('\n📊 Order Calculation Test\n');
  console.log('Items:');
  
  let subtotal = 0;
  let totalBuyingPrice = 0;
  
  items.forEach(item => {
    const pricing = findPricing(item.code, item.dose);
    if (!pricing) {
      console.log(`❌ Pricing not found for ${item.code} ${item.dose}`);
      return;
    }
    
    const itemTotal = pricing.selling_price * item.qty;
    const itemBuyingTotal = pricing.buying_price * item.qty;
    
    subtotal += itemTotal;
    totalBuyingPrice += itemBuyingTotal;
    
    console.log(`  ${item.code} ${item.dose} x${item.qty}:`);
    console.log(`    Selling: $${pricing.selling_price} × ${item.qty} = $${itemTotal}`);
    console.log(`    Buying: $${pricing.buying_price} × ${item.qty} = $${itemBuyingTotal}`);
  });
  
  const shipping = 40;
  const discount = subtotal * 0.05;
  const totalUsd = subtotal + shipping - discount;
  const totalBtc = totalUsd / BTC_RATE;
  const profitUsd = subtotal - totalBuyingPrice;
  const profitInr = profitUsd * 86;
  const profitMargin = (profitUsd / subtotal) * 100;
  
  console.log('\nCalculations:');
  console.log(`  Subtotal: $${subtotal.toFixed(2)}`);
  console.log(`  Shipping: $${shipping.toFixed(2)}`);
  console.log(`  Discount (5%): -$${discount.toFixed(2)}`);
  console.log(`  Total USD: $${totalUsd.toFixed(2)}`);
  console.log(`  Total BTC: ${totalBtc.toFixed(8)} BTC`);
  console.log(`  Profit USD: $${profitUsd.toFixed(2)}`);
  console.log(`  Profit INR: ₹${profitInr.toFixed(2)}`);
  console.log(`  Profit Margin: ${profitMargin.toFixed(2)}%`);
  
  return {
    subtotal,
    shipping,
    discount,
    totalUsd,
    totalBtc,
    profitUsd,
    profitInr,
    profitMargin
  };
}

// Test cases
console.log('🧪 Testing Order Calculations\n');

// Test 1: Single item order
console.log('Test 1: Single Item Order');
calculateOrder([
  { code: 'TR10', dose: '10 mg', qty: 1 }
]);

// Test 2: Multiple items
console.log('\nTest 2: Multiple Items Order');
calculateOrder([
  { code: 'TR15', dose: '15 mg', qty: 2 },
  { code: 'SM5', dose: '5 mg', qty: 1 },
  { code: 'RT20', dose: '20 mg', qty: 3 }
]);

// Test 3: High quantity order
console.log('\nTest 3: High Quantity Order');
calculateOrder([
  { code: 'SM10', dose: '10 mg', qty: 10 }
]);

// Test 4: Mixed products
console.log('\nTest 4: Mixed Products Order');
calculateOrder([
  { code: 'TR50', dose: '50 mg', qty: 2 },
  { code: 'RT10', dose: '10 mg', qty: 1 },
  { code: 'BA10', dose: '10 ml × 10 vials', qty: 5 }
]);

console.log('\n✅ All calculation tests completed!');
console.log('\n📋 Available Products in pricing.json:');
Object.entries(pricingData).forEach(([product, items]) => {
  console.log(`\n${product}:`);
  (items as PricingItem[]).forEach(item => {
    console.log(`  - ${item.code} ${item.dose}: Buy $${item.buying_price}, Sell $${item.selling_price}`);
  });
});