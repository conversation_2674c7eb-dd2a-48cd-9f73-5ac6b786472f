import axios from 'axios';
import { fileURLToPath } from 'url';

const API_BASE = 'http://localhost:3000/api';

// Test data
const testOrder = {
  customerName: '<PERSON>',
  street1: '123 Main St',
  street2: 'Apt 4B',
  city: 'New York',
  state: 'NY',
  postalCode: '10001',
  country: 'United States',
  items: [
    { code: 'PT141', dose: '10mg', qty: 2 },
    { code: 'BPC157', dose: '5mg', qty: 1 }
  ],
  paymentMethod: 'BTC' as const,
  paymentUrl: 'https://blockchain.info/tx/abc123'
};

async function runTests() {
  console.log('🧪 Starting API Tests...\n');

  try {
    // Test 1: Health Check
    console.log('1️⃣ Testing Health Endpoint...');
    const healthResponse = await axios.get('http://localhost:3000/health');
    console.log('✅ Health Check:', healthResponse.data);
    console.log('');

    // Test 2: Get Pricing
    console.log('2️⃣ Testing GET /api/pricing...');
    const pricingResponse = await axios.get(`${API_BASE}/pricing`);
    console.log('✅ Pricing Data Sample:');
    console.log(`   Total Products: ${pricingResponse.data.pricing.length}`);
    if (pricingResponse.data.pricing.length > 0) {
      console.log('   Sample Item:', pricingResponse.data.pricing[0]);
    }
    console.log('');

    // Test 3: Create Order
    console.log('3️⃣ Testing POST /api/orders...');
    console.log('   Order Data:', JSON.stringify(testOrder, null, 2));
    const createOrderResponse = await axios.post(`${API_BASE}/orders`, testOrder);
    const orderId = createOrderResponse.data.order.id;
    console.log('✅ Order Created:');
    console.log(`   Order ID: ${orderId}`);
    console.log(`   Total USD: $${createOrderResponse.data.order.totalUsd}`);
    console.log(`   Total BTC: ${createOrderResponse.data.order.totalBtc}`);
    console.log(`   Profit USD: $${createOrderResponse.data.order.profitUsd}`);
    console.log(`   Profit Margin: ${createOrderResponse.data.order.profitMargin}%`);
    console.log('');

    // Test 4: List Orders
    console.log('4️⃣ Testing GET /api/orders...');
    const listOrdersResponse = await axios.get(`${API_BASE}/orders`);
    console.log('✅ Orders List:');
    console.log(`   Total Orders: ${listOrdersResponse.data.orders.length}`);
    console.log('');

    // Test 5: Get Single Order
    console.log('5️⃣ Testing GET /api/orders/:id...');
    const getOrderResponse = await axios.get(`${API_BASE}/orders/${orderId}`);
    console.log('✅ Order Details:');
    console.log(`   Customer: ${getOrderResponse.data.order.customerName}`);
    console.log(`   Items: ${getOrderResponse.data.order.items.length}`);
    console.log('');

    // Test 6: Update Tracking
    console.log('6️⃣ Testing PATCH /api/orders/:id/tracking...');
    const trackingResponse = await axios.patch(`${API_BASE}/orders/${orderId}/tracking`, {
      tracking: 'ABC123456789'
    });
    console.log('✅ Tracking Updated:');
    console.log(`   Tracking Number: ${trackingResponse.data.order.tracking17}`);
    console.log('');

    // Test 7: Generate PDF
    console.log('7️⃣ Testing PDF Generation...');
    const pdfResponse = await axios.get(`${API_BASE}/exports/${orderId}/pdf`, {
      responseType: 'arraybuffer'
    });
    console.log('✅ PDF Generated:');
    console.log(`   Size: ${pdfResponse.data.byteLength} bytes`);
    console.log('');

    // Test 8: Generate Excel
    console.log('8️⃣ Testing Excel Generation...');
    const excelResponse = await axios.get(`${API_BASE}/exports/${orderId}/excel`, {
      responseType: 'arraybuffer'
    });
    console.log('✅ Excel Generated:');
    console.log(`   Size: ${excelResponse.data.byteLength} bytes`);
    console.log('');

    console.log('✨ All tests completed successfully!');

  } catch (error) {
    if (axios.isAxiosError(error)) {
      console.error('❌ Test failed:', error.message);
      if (error.response) {
        console.error('   Status:', error.response.status);
        console.error('   Data:', error.response.data);
      }
    } else {
      console.error('❌ Unexpected error:', error);
    }
  }
}

// Run tests if server is running
console.log('⚠️  Note: This test requires the server to be running with a database connection.');
console.log('⚠️  Since PostgreSQL is not installed, let me create a mock test instead...\n');

// Create a mock test to show the API structure
function mockApiDemo() {
  console.log('📋 API Endpoints Overview:\n');
  
  console.log('1️⃣ GET /health');
  console.log('   Returns: { status: "ok", timestamp: "2024-01-01T00:00:00.000Z" }\n');
  
  console.log('2️⃣ GET /api/pricing');
  console.log('   Returns: { pricing: [{ code, product, dose, buyingPrice, sellingPrice }] }\n');
  
  console.log('3️⃣ POST /api/orders');
  console.log('   Body: { customerName, street1, city, state, postalCode, items, paymentMethod }');
  console.log('   Returns: { order: { id, totalUsd, totalBtc, profitUsd, profitMargin, ... } }\n');
  
  console.log('4️⃣ GET /api/orders');
  console.log('   Returns: { orders: [...] }\n');
  
  console.log('5️⃣ GET /api/orders/:id');
  console.log('   Returns: { order: { id, customerName, items, ... } }\n');
  
  console.log('6️⃣ PATCH /api/orders/:id/tracking');
  console.log('   Body: { tracking: "ABC123" }');
  console.log('   Returns: { order: { ...updated order } }\n');
  
  console.log('7️⃣ GET /api/exports/:orderId/pdf');
  console.log('   Returns: PDF file download\n');
  
  console.log('8️⃣ GET /api/exports/:orderId/excel');
  console.log('   Returns: Excel file download\n');
  
  console.log('💡 Order Calculations:');
  console.log('   - Subtotal = Σ(selling_price × quantity)');
  console.log('   - Shipping = $40 (fixed)');
  console.log('   - Discount = 5% × Subtotal');
  console.log('   - Total USD = Subtotal + Shipping - Discount');
  console.log('   - Total BTC = Total USD ÷ current_btc_rate');
  console.log('   - Profit USD = Σ(selling_price - buying_price)');
  console.log('   - Profit INR = Profit USD × 86');
  console.log('   - Profit Margin = (Profit USD ÷ Subtotal) × 100\n');
}

// Check if running directly
if (process.argv[1] === fileURLToPath(import.meta.url)) {
  mockApiDemo();
  console.log('To run actual tests:');
  console.log('1. Install PostgreSQL: brew install postgresql@16');
  console.log('2. Start PostgreSQL: brew services start postgresql@16');
  console.log('3. Create database: createdb peptide_portal_dev');
  console.log('4. Run migrations: npm run db:push');
  console.log('5. Seed data: npm run db:seed');
  console.log('6. Start server: npm run dev');
  console.log('7. Run tests: npm run test:api');
}

export { runTests };