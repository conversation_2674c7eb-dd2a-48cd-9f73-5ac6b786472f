import { PrismaClient } from '../generated/prisma';
import { PDFService } from '../services/pdf.service';
import { ExcelService } from '../services/excel.service';
import path from 'path';

const prisma = new PrismaClient();
const pdfService = new PDFService();
const excelService = new ExcelService();

async function testSupplierPricing() {
  try {
    // Get the most recent order with items
    const order = await prisma.order.findFirst({
      where: {
        items: {
          some: {}
        }
      },
      orderBy: {
        placedAt: 'desc'
      },
      include: {
        items: true
      }
    });

    if (!order) {
      console.log('No orders found with items');
      return;
    }

    console.log('\n=== Testing Supplier Document Pricing ===');
    console.log(`Order ID: ${order.id}`);
    console.log(`Customer: ${order.customerName}`);
    console.log('\nOrder Items:');
    
    for (const item of order.items) {
      console.log(`\n${item.code} (${item.dose}):`);
      console.log(`  Quantity: ${item.qty}`);
      console.log(`  Buying Price: $${item.buyingPrice}`);
      console.log(`  Selling Price: $${item.sellingPrice}`);
      console.log(`  Should show in supplier docs: $${item.buyingPrice}`);
    }

    // Generate test files
    const timestamp = Date.now();
    const pdfPath = path.join(process.cwd(), 'exports', `test-supplier-${timestamp}.pdf`);
    const excelPath = path.join(process.cwd(), 'exports', `test-supplier-${timestamp}.xlsx`);

    console.log('\n=== Generating Test Files ===');
    
    await pdfService.generateOrderPDF(order, pdfPath);
    console.log(`✓ PDF generated: ${pdfPath}`);
    
    await excelService.generateOrderExcel(order, excelPath);
    console.log(`✓ Excel generated: ${excelPath}`);

    console.log('\n=== IMPORTANT ===');
    console.log('Please check the generated files to verify:');
    console.log('1. Unit prices should match BUYING prices (not selling prices)');
    console.log('2. Totals should be calculated using buying prices');
    console.log('3. No profit information should be visible');
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testSupplierPricing();