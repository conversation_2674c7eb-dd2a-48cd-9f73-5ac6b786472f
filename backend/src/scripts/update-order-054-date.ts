import { PrismaClient } from '../generated/prisma/index.js';

const prisma = new PrismaClient();

async function main() {
  const orderNumber = 'ORD-2025-0054';
  const newDate = new Date('2025-07-08T00:00:00Z'); // July 8th, 2025

  try {
    console.log(`Updating ${orderNumber} to ${newDate.toISOString()}...`);

    // First check if the order exists
    const existingOrder = await prisma.order.findUnique({
      where: { orderNumber },
      select: { id: true, orderNumber: true, placedAt: true }
    });

    if (!existingOrder) {
      console.error(`Order ${orderNumber} not found`);
      return;
    }

    console.log(`Found order: ${existingOrder.orderNumber}`);
    console.log(`Current date: ${existingOrder.placedAt.toISOString()}`);

    // Update the order date
    const updatedOrder = await prisma.order.update({
      where: { orderNumber },
      data: { placedAt: newDate }
    });

    console.log(`✅ Successfully updated ${orderNumber} to July 8th, 2025`);

  } catch (error) {
    console.error('Error updating order date:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main().catch(console.error);