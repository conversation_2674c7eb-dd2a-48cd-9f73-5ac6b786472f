import { PrismaClient } from '../generated/prisma/index.js';

const prisma = new PrismaClient();

async function findNewOrders() {
  try {
    const newOrders = await prisma.order.findMany({
      where: {
        orderNumber: {
          gt: 'ORD-2025-0089'
        }
      },
      select: {
        orderNumber: true,
        placedAt: true,
        customerName: true,
        totalUsd: true,
        status: true
      },
      orderBy: { orderNumber: 'asc' }
    });
    
    console.log(`Found ${newOrders.length} new orders (>= ORD-2025-0090):`);
    newOrders.forEach(o => {
      console.log(`${o.orderNumber} - ${o.placedAt} - ${o.customerName} - $${o.totalUsd} - ${o.status}`);
    });
  } catch (err) {
    console.error('Error fetching new orders:', err);
  } finally {
    await prisma.$disconnect();
  }
}

findNewOrders();