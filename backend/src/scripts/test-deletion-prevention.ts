import { PrismaClient } from '../generated/prisma/index.js';
import axios from 'axios';

const prisma = new PrismaClient();
const API_URL = 'http://localhost:3000/api';

async function testDeletionPrevention() {
  console.log('🧪 Testing product deletion prevention...\n');

  try {
    // Find an order with items
    const orders = await prisma.order.findMany({
      include: { items: true },
      take: 1
    });

    if (orders.length === 0) {
      console.log('❌ No orders found in database. Cannot test deletion prevention.');
      console.log('Please create an order first to test this feature.');
      return;
    }

    const order = orders[0];
    console.log(`Found order: ${order.orderNumber}`);
    console.log('Items in order:');
    order.items.forEach(item => {
      console.log(`  - Code: ${item.code}, Dose: ${item.dose}, Qty: ${item.qty}`);
    });

    if (order.items.length === 0) {
      console.log('❌ Order has no items. Cannot test deletion prevention.');
      return;
    }

    // Try to delete a product that's used in this order
    const productCode = order.items[0].code;
    console.log(`\n🔄 Attempting to delete product with code: ${productCode}`);

    try {
      await axios.delete(`${API_URL}/pricing/${productCode}`);
      console.log('❌ Product was deleted! This should not happen.');
    } catch (error: any) {
      if (error.response?.status === 409) {
        console.log('✅ Deletion prevented successfully!');
        console.log('Response:', error.response.data);
      } else {
        console.log('❌ Unexpected error:', error.response?.data || error.message);
      }
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run test
testDeletionPrevention().catch(console.error);