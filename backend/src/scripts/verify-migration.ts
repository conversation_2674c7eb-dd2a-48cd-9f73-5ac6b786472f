import { PrismaClient } from '../generated/prisma/index.js';

const prisma = new PrismaClient();

async function verifyMigration() {
  console.log('Verifying migration...\n');

  // Check products
  const products = await prisma.product.findMany({
    include: {
      variants: {
        orderBy: { sortOrder: 'asc' }
      }
    }
  });

  console.log(`Total products: ${products.length}`);
  for (const product of products) {
    console.log(`\n${product.name}: ${product.variants.length} variants`);
    for (const variant of product.variants) {
      console.log(`  - ${variant.dose} (${variant.code}): $${variant.buyingPrice} → $${variant.sellingPrice}`);
    }
  }

  // Check order items
  const orderItemsWithVariants = await prisma.orderItem.count({
    where: { variantId: { not: null } }
  });
  const totalOrderItems = await prisma.orderItem.count();
  
  console.log(`\nOrder items: ${orderItemsWithVariants}/${totalOrderItems} linked to variants`);

  await prisma.$disconnect();
}

verifyMigration().catch(console.error);