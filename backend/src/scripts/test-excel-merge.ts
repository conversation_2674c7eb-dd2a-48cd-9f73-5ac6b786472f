import { ExcelService } from '../services/excel.service.js';
import { PrismaClient } from '../generated/prisma/index.js';
import ExcelJS from 'exceljs';
import path from 'path';

const prisma = new PrismaClient();
const excelService = new ExcelService();

async function testExcelMerge() {
  try {
    // Get the 3 bulk test orders we just created
    const orders = await prisma.order.findMany({
      where: {
        customerName: {
          contains: 'Bulk Test'
        }
      },
      include: {
        items: true
      },
      orderBy: {
        placedAt: 'desc'
      },
      take: 3
    });

    console.log(`Found ${orders.length} bulk test orders`);
    
    // Generate Excel
    const outputPath = path.join(process.cwd(), 'test-merge-verification.xlsx');
    await excelService.generateBulkOrdersExcel(orders, outputPath);
    
    console.log('✅ Excel generated at:', outputPath);
    
    // Now let's verify the merge worked by reading the file back
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.readFile(outputPath);
    
    const detailsSheet = workbook.getWorksheet('Order Details');
    
    if (!detailsSheet) {
      throw new Error('Order Details sheet not found!');
    }
    
    console.log('\n📊 Verifying merged cells...\n');
    
    // Check merged cells
    let currentRow = 2; // Start after header
    
    orders.forEach((order, orderIndex) => {
      console.log(`\nOrder ${orderIndex + 1}: ${order.customerName} (${order.items.length} items)`);
      
      if (order.items.length > 1) {
        const startRow = currentRow;
        const endRow = currentRow + order.items.length - 1;
        
        // Check if cells are merged
        const mergedRanges = detailsSheet.model.merges;
        
        // Look for merges in our range
        const orderIdMerge = mergedRanges.find(range => 
          range.startsWith(`A${startRow}:A${endRow}`)
        );
        const nameMerge = mergedRanges.find(range => 
          range.startsWith(`B${startRow}:B${endRow}`)
        );
        
        console.log(`  - Expected merge range: Row ${startRow} to ${endRow}`);
        console.log(`  - Order ID merge found: ${orderIdMerge ? '✅' : '❌'}`);
        console.log(`  - Name merge found: ${nameMerge ? '✅' : '❌'}`);
        
        // Check cell values
        for (let row = startRow; row <= endRow; row++) {
          const orderId = detailsSheet.getCell(row, 1).value;
          const name = detailsSheet.getCell(row, 2).value;
          const code = detailsSheet.getCell(row, 4).value;
          
          console.log(`  - Row ${row}: OrderID="${orderId || 'null'}", Name="${name || 'null'}", Code="${code}"`);
        }
        
        currentRow = endRow + 1;
      } else {
        currentRow++;
      }
    });
    
    console.log('\n✨ Verification complete!');
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testExcelMerge();