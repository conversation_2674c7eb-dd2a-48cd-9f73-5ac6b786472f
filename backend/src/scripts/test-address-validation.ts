import axios from 'axios';
import * as dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, resolve } from 'path';

// Get __dirname equivalent for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
dotenv.config({ path: resolve(__dirname, '../../.env') });

const API_URL = 'http://localhost:3000/api';

// Test addresses - some valid, some invalid
const TEST_ADDRESSES = [
  {
    name: 'Valid US Address (White House)',
    address: {
      street1: '1600 Pennsylvania Avenue NW',
      city: 'Washington',
      state: 'DC',
      postalCode: '20500',
      country: 'United States'
    },
    expectedValid: true
  },
  {
    name: 'Valid US Address with Suite',
    address: {
      street1: '1 Microsoft Way',
      street2: 'Building 92',
      city: 'Redmond',
      state: 'WA',
      postalCode: '98052',
      country: 'United States'
    },
    expectedValid: true
  },
  {
    name: 'Invalid US Address',
    address: {
      street1: '123 Fake Street',
      city: 'Nowhere',
      state: 'XX',
      postalCode: '00000',
      country: 'United States'
    },
    expectedValid: false
  },
  {
    name: 'Valid Canadian Address',
    address: {
      street1: '1 Sussex Drive',
      city: 'Ottawa',
      state: 'ON',
      postalCode: 'K1A 0A1',
      country: 'Canada'
    },
    expectedValid: true
  },
  {
    name: 'Valid UK Address (no state)',
    address: {
      street1: '10 Downing Street',
      city: 'London',
      postalCode: 'SW1A 2AB',
      country: 'United Kingdom'
    },
    expectedValid: true
  },
  {
    name: 'Incomplete Address',
    address: {
      street1: 'Main Street',
      city: 'Somewhere',
      state: 'CA',
      postalCode: '90210',
      country: 'United States'
    },
    expectedValid: false // Missing street number
  },
  // New countries added for Phase 5
  {
    name: 'Valid Brazil Address (Copacabana)',
    address: {
      street1: 'Avenida Atlântica 1702',
      city: 'Rio de Janeiro',
      state: 'RJ',
      postalCode: '22021-001',
      country: 'Brazil'
    },
    expectedValid: true
  },
  {
    name: 'Valid China Address (Beijing)',
    address: {
      street1: '1 Dongchang\'an Avenue',
      city: 'Beijing',
      state: 'BJ',
      postalCode: '100006',
      country: 'China'
    },
    expectedValid: true
  },
  {
    name: 'Valid South Korea Address (Seoul)',
    address: {
      street1: '1 Sejong-daero',
      city: 'Seoul',
      state: 'Seoul',
      postalCode: '04524',
      country: 'South Korea'
    },
    expectedValid: true
  },
  {
    name: 'Valid Netherlands Address (Amsterdam)',
    address: {
      street1: 'Dam 1',
      city: 'Amsterdam',
      state: 'NH',
      postalCode: '1012 JS',
      country: 'Netherlands'
    },
    expectedValid: true
  }
];

async function testAddressValidation() {
  console.log('Testing Google Address Validation API...\n');

  for (const testCase of TEST_ADDRESSES) {
    console.log(`\n========================================`);
    console.log(`Test: ${testCase.name}`);
    console.log(`Expected Valid: ${testCase.expectedValid}`);
    console.log(`========================================`);
    
    try {
      console.log('\nSending validation request...');
      const startTime = Date.now();
      
      const response = await axios.post(`${API_URL}/address/validate`, testCase.address);
      const duration = Date.now() - startTime;
      
      const result = response.data;
      console.log(`\nResponse received in ${duration}ms:`);
      console.log(`- Valid: ${result.isValid}`);
      console.log(`- Confidence: ${result.confidence}`);
      
      if (result.issues && result.issues.length > 0) {
        console.log(`- Issues: ${result.issues.join(', ')}`);
      }
      
      if (result.metadata) {
        console.log(`- Metadata:`);
        if (result.metadata.residential !== undefined) {
          console.log(`  - Residential: ${result.metadata.residential}`);
        }
        if (result.metadata.business !== undefined) {
          console.log(`  - Business: ${result.metadata.business}`);
        }
        if (result.metadata.poBox !== undefined) {
          console.log(`  - PO Box: ${result.metadata.poBox}`);
        }
      }
      
      if (result.standardizedAddress) {
        console.log(`\n📍 Standardized Address:`);
        console.log(`   ${result.standardizedAddress.street1}`);
        if (result.standardizedAddress.street2) {
          console.log(`   ${result.standardizedAddress.street2}`);
        }
        console.log(`   ${result.standardizedAddress.city}, ${result.standardizedAddress.state || ''} ${result.standardizedAddress.postalCode}`);
        console.log(`   ${result.standardizedAddress.country}`);
      }
      
      // Verify result matches expectation
      if (result.isValid === testCase.expectedValid) {
        console.log(`\n✅ Test PASSED`);
      } else {
        console.log(`\n❌ Test FAILED - Expected valid=${testCase.expectedValid}, got valid=${result.isValid}`);
      }
      
    } catch (error: any) {
      console.log(`\n❌ Error: ${error.message}`);
      if (error.response?.data) {
        console.log(`   Details: ${JSON.stringify(error.response.data)}`);
      }
    }
  }
  
  // Test rate limiting
  console.log('\n\n========================================');
  console.log('Testing Rate Limiting (10 requests/minute)');
  console.log('========================================');
  
  try {
    console.log('\nSending 12 rapid requests...');
    const promises = [];
    
    for (let i = 0; i < 12; i++) {
      promises.push(
        axios.post(`${API_URL}/address/validate`, TEST_ADDRESSES[0].address)
          .then(() => ({ success: true, index: i }))
          .catch(err => ({ success: false, index: i, status: err.response?.status }))
      );
    }
    
    const results = await Promise.all(promises);
    const successful = results.filter(r => r.success).length;
    const rateLimited = results.filter(r => !r.success && r.status === 429).length;
    
    console.log(`\nResults:`);
    console.log(`- Successful: ${successful}`);
    console.log(`- Rate Limited (429): ${rateLimited}`);
    
    if (successful <= 10 && rateLimited > 0) {
      console.log('\n✅ Rate limiting is working correctly');
    } else {
      console.log('\n❌ Rate limiting test failed');
    }
    
  } catch (error: any) {
    console.log(`\n❌ Rate limiting test error: ${error.message}`);
  }
  
  // Test caching
  console.log('\n\n========================================');
  console.log('Testing Caching');
  console.log('========================================');
  
  try {
    const testAddress = TEST_ADDRESSES[0].address;
    
    console.log('\nFirst request (should hit API)...');
    const firstStart = Date.now();
    await axios.post(`${API_URL}/address/validate`, testAddress);
    const firstDuration = Date.now() - firstStart;
    console.log(`Duration: ${firstDuration}ms`);
    
    console.log('\nSecond request (should hit cache)...');
    const secondStart = Date.now();
    await axios.post(`${API_URL}/address/validate`, testAddress);
    const secondDuration = Date.now() - secondStart;
    console.log(`Duration: ${secondDuration}ms`);
    
    if (secondDuration < firstDuration / 2) {
      console.log('\n✅ Caching appears to be working (second request was faster)');
    } else {
      console.log('\n⚠️  Cache performance unclear (check server logs for cache hits)');
    }
    
  } catch (error: any) {
    console.log(`\n❌ Caching test error: ${error.message}`);
  }
  
  console.log('\n\nAll tests completed!');
}

// Run the tests
testAddressValidation().catch(console.error);