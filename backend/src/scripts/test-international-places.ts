import axios from 'axios';
import * as dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, resolve } from 'path';

// Get __dirname equivalent for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
dotenv.config({ path: resolve(__dirname, '../../.env') });

const API_URL = 'http://localhost:3000/api';

// Test addresses for different countries
const TEST_ADDRESSES = [
  // United States
  { query: '1600 Pennsylvania Avenue NW, Washington, DC', country: 'United States' },
  { query: '1 Infinite Loop, Cupertino, CA', country: 'United States' },
  
  // Canada
  { query: '1 Sussex Drive, Ottawa, ON', country: 'Canada' },
  { query: '1455 De Maisonneuve Blvd W, Montreal, QC', country: 'Canada' },
  
  // United Kingdom
  { query: '10 Downing Street, London', country: 'United Kingdom' },
  { query: 'Buckingham Palace, London SW1A 1AA', country: 'United Kingdom' },
  
  // France
  { query: '55 Rue du Faubourg Saint-Honoré, 75008 Paris', country: 'France' },
  { query: '5 Avenue Anatole France, 75007 Paris', country: 'France' },
  
  // Germany
  { query: 'Platz der Republik 1, 11011 Berlin', country: 'Germany' },
  { query: 'Marienplatz 8, 80331 München', country: 'Germany' },
  
  // Japan
  { query: '1-1 Nagatacho, Chiyoda City, Tokyo 100-8914', country: 'Japan' },
  { query: '2-1-1 Shibuya, Shibuya City, Tokyo', country: 'Japan' },
  
  // Australia
  { query: '1 Macquarie Street, Sydney NSW 2000', country: 'Australia' },
  { query: '1 Treasury Place, Melbourne VIC 3002', country: 'Australia' },
  
  // India
  { query: 'Rajpath, New Delhi 110001', country: 'India' },
  { query: 'Chhatrapati Shivaji Terminus, Mumbai, Maharashtra', country: 'India' },
  
  // Singapore
  { query: '1 Raffles Place, Singapore 048616', country: 'Singapore' },
  { query: '80 Bras Basah Road, Singapore 189560', country: 'Singapore' },
  
  // Ireland
  { query: 'Dublin Castle, Dame Street, Dublin 2', country: 'Ireland' },
  { query: 'St Stephen\'s Green, Dublin 2', country: 'Ireland' },
];

async function testPlacesAPI() {
  console.log('Testing Google Places API with international addresses...\n');

  for (const testCase of TEST_ADDRESSES) {
    console.log(`\n========================================`);
    console.log(`Testing: ${testCase.query}`);
    console.log(`Expected Country: ${testCase.country}`);
    console.log(`========================================`);
    
    try {
      // Step 1: Get autocomplete suggestions
      console.log('\n1. Getting autocomplete suggestions...');
      const autocompleteResponse = await axios.get(`${API_URL}/places/autocomplete`, {
        params: { input: testCase.query }
      });
      
      const predictions = autocompleteResponse.data.predictions;
      if (!predictions || predictions.length === 0) {
        console.log('❌ No predictions found');
        continue;
      }
      
      console.log(`✅ Found ${predictions.length} predictions`);
      console.log(`   First suggestion: ${predictions[0].description}`);
      
      // Step 2: Get place details for the first prediction
      console.log('\n2. Getting place details...');
      const placeId = predictions[0].placeId;
      const detailsResponse = await axios.get(`${API_URL}/places/details`, {
        params: { placeId }
      });
      
      const details = detailsResponse.data;
      console.log('\n📍 Parsed Address:');
      console.log(`   Street: ${details.street1 || '(empty)'}`);
      console.log(`   City: ${details.city || '(empty)'}`);
      console.log(`   State/Region: ${details.state || '(empty)'}`);
      console.log(`   Postal Code: ${details.postalCode || '(empty)'}`);
      console.log(`   Country: ${details.country || '(empty)'}`);
      
      // Verify country matches
      if (details.country === testCase.country) {
        console.log(`\n✅ Country matches!`);
      } else {
        console.log(`\n❌ Country mismatch: expected "${testCase.country}", got "${details.country}"`);
      }
      
      // Check for state handling in stateless countries
      const statelessCountries = ['United Kingdom', 'France', 'Singapore', 'Ireland'];
      if (statelessCountries.includes(details.country) && details.state) {
        console.log(`\n⚠️  Warning: ${details.country} should not have a state, but got "${details.state}"`);
      }
      
    } catch (error: any) {
      console.log(`\n❌ Error: ${error.message}`);
      if (error.response?.data) {
        console.log(`   Details: ${JSON.stringify(error.response.data)}`);
      }
    }
  }
  
  console.log('\n\nTest completed!');
}

// Run the test
testPlacesAPI().catch(console.error);