import { PrismaClient } from '../generated/prisma/index.js';

const prisma = new PrismaClient();

interface MigrationResult {
  productsCreated: number;
  variantsCreated: number;
  errors: string[];
}

async function migrateToVariants(): Promise<MigrationResult> {
  console.log('Starting migration to variant-based product structure...');
  
  const result: MigrationResult = {
    productsCreated: 0,
    variantsCreated: 0,
    errors: []
  };

  try {
    // Get all existing pricing data
    const pricingData = await prisma.pricing.findMany({
      orderBy: [
        { product: 'asc' },
        { dose: 'asc' }
      ]
    });

    console.log(`Found ${pricingData.length} pricing entries to migrate`);

    // Group by product name
    const productGroups = pricingData.reduce((acc, item) => {
      if (!acc[item.product]) {
        acc[item.product] = [];
      }
      acc[item.product].push(item);
      return acc;
    }, {} as Record<string, typeof pricingData>);

    console.log(`Grouped into ${Object.keys(productGroups).length} products`);

    // Create products and variants
    for (const [productName, variants] of Object.entries(productGroups)) {
      try {
        console.log(`\nMigrating product: ${productName}`);
        
        // Check if product already exists
        let product = await prisma.product.findUnique({
          where: { name: productName }
        });

        if (!product) {
          // Create the product
          product = await prisma.product.create({
            data: { name: productName }
          });
          result.productsCreated++;
          console.log(`  ✓ Created product: ${productName}`);
        } else {
          console.log(`  - Product already exists: ${productName}`);
        }

        // Create variants for this product
        for (let i = 0; i < variants.length; i++) {
          const variant = variants[i];
          
          // Check if variant already exists
          const existingVariant = await prisma.productVariant.findUnique({
            where: { code: variant.code }
          });

          if (!existingVariant) {
            await prisma.productVariant.create({
              data: {
                productId: product.id,
                dose: variant.dose,
                code: variant.code,
                buyingPrice: variant.buyingPrice,
                sellingPrice: variant.sellingPrice,
                sortOrder: i * 10 // Leave gaps for future reordering
              }
            });
            result.variantsCreated++;
            console.log(`    ✓ Created variant: ${variant.dose} (${variant.code})`);
          } else {
            console.log(`    - Variant already exists: ${variant.dose} (${variant.code})`);
          }
        }
      } catch (error) {
        const errorMsg = `Error migrating product ${productName}: ${error}`;
        console.error(errorMsg);
        result.errors.push(errorMsg);
      }
    }

    console.log('\nMigration completed!');
    console.log(`Products created: ${result.productsCreated}`);
    console.log(`Variants created: ${result.variantsCreated}`);
    if (result.errors.length > 0) {
      console.log(`Errors: ${result.errors.length}`);
      result.errors.forEach(err => console.error(`  - ${err}`));
    }

    // Update existing order items to reference the new variants
    console.log('\nUpdating order items to reference new variants...');
    const orderItems = await prisma.orderItem.findMany({
      where: { variantId: null }
    });

    let updatedItems = 0;
    for (const item of orderItems) {
      try {
        const variant = await prisma.productVariant.findUnique({
          where: { code: item.code }
        });
        
        if (variant) {
          await prisma.orderItem.update({
            where: { id: item.id },
            data: { variantId: variant.id }
          });
          updatedItems++;
        }
      } catch (error) {
        console.error(`Error updating order item ${item.id}: ${error}`);
      }
    }
    
    console.log(`Updated ${updatedItems} order items with variant references`);

    return result;
  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run migration
migrateToVariants()
  .then(result => {
    console.log('\nMigration summary:', result);
    process.exit(0);
  })
  .catch(error => {
    console.error('Migration failed:', error);
    process.exit(1);
  });