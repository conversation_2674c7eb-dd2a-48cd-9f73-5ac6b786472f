import { PrismaClient } from '../generated/prisma/index.js';
import axios from 'axios';

const prisma = new PrismaClient();
const API_BASE_URL = 'http://localhost:3000/api';

async function testOrderStatus() {
  console.log('Testing order status functionality...\n');

  try {
    // Step 1: Create a new order
    console.log('1. Creating a new order...');
    const createResponse = await axios.post(`${API_BASE_URL}/orders`, {
      customerName: 'Status Test Customer',
      street1: '123 Test Street',
      city: 'Test City',
      state: 'CA',
      postalCode: '12345',
      country: 'United States',
      paymentMethod: 'BTC',
      paymentUrl: 'https://blockchain.info/test-payment',
      items: [
        { code: 'TR5', dose: '5 mg', qty: 2 }
      ]
    }, {
      headers: { 'Content-Type': 'application/json' }
    });

    const order = createResponse.data.order;
    console.log(`   ✓ Order created with ID: ${order.id}`);
    console.log(`   ✓ Order number: ${order.orderNumber}`);
    console.log(`   ✓ Initial status: ${order.status}`);
    
    if (order.status !== 'pending') {
      throw new Error(`Expected status 'pending', got '${order.status}'`);
    }

    // Step 2: Send order to supplier
    console.log('\n2. Sending order to supplier...');
    const sendResponse = await axios.post(`${API_BASE_URL}/orders/${order.id}/send`, {});
    const sentOrder = sendResponse.data.order;
    console.log(`   ✓ Order sent to supplier`);
    console.log(`   ✓ Updated status: ${sentOrder.status}`);
    
    if (sentOrder.status !== 'sent') {
      throw new Error(`Expected status 'sent', got '${sentOrder.status}'`);
    }

    // Step 3: Add tracking number
    console.log('\n3. Adding tracking number...');
    const trackingResponse = await axios.patch(`${API_BASE_URL}/orders/${order.id}/tracking`, {
      tracking: 'TEST-TRACKING-123'
    }, {
      headers: { 'Content-Type': 'application/json' }
    });
    const shippedOrder = trackingResponse.data.order;
    console.log(`   ✓ Tracking number added: ${shippedOrder.tracking17}`);
    console.log(`   ✓ Updated status: ${shippedOrder.status}`);
    
    if (shippedOrder.status !== 'shipped') {
      throw new Error(`Expected status 'shipped', got '${shippedOrder.status}'`);
    }

    // Step 4: Verify order in list
    console.log('\n4. Verifying order in list...');
    const listResponse = await axios.get(`${API_BASE_URL}/orders`);
    const orderInList = listResponse.data.orders.find((o: any) => o.id === order.id);
    
    if (!orderInList) {
      throw new Error('Order not found in list');
    }
    
    console.log(`   ✓ Order found in list with status: ${orderInList.status}`);

    // Step 5: Get single order
    console.log('\n5. Getting single order...');
    const getResponse = await axios.get(`${API_BASE_URL}/orders/${order.id}`);
    const singleOrder = getResponse.data.order;
    console.log(`   ✓ Single order retrieved with status: ${singleOrder.status}`);

    // Step 6: Clean up - delete test order
    console.log('\n6. Cleaning up test order...');
    await axios.delete(`${API_BASE_URL}/orders/${order.id}`);
    console.log('   ✓ Test order deleted');

    console.log('\n✅ All tests passed! Order status tracking is working correctly.');

  } catch (error: any) {
    console.error('\n❌ Test failed:', error.response?.data || error.message);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Verify server is running
async function checkServerRunning() {
  try {
    await axios.get(`${API_BASE_URL}/pricing`);
    return true;
  } catch (error) {
    return false;
  }
}

// Run the test
(async () => {
  const serverRunning = await checkServerRunning();
  
  if (!serverRunning) {
    console.error('❌ Server is not running on port 3000. Please start the server first.');
    process.exit(1);
  }

  await testOrderStatus();
  process.exit(0);
})();