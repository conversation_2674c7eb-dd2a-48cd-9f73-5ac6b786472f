import { PrismaClient } from '../generated/prisma/index.js';

const prisma = new PrismaClient();

export interface DashboardMetrics {
  profit: {
    totalUSD: number;
    totalINR: number;
    byProduct: {
      Tirzepatide: number;
      Retatrutide: number;
      Semaglutide: number;
      'BAC Water': number;
    };
  };
  revenue: {
    totalUSD: number;
    totalINR: number;
  };
  profitMargin: {
    percentage: number;
  };
  orders: {
    total: number;
    byStatus: {
      pending: number;
      sent: number;
      shipped: number;
    };
  };
  products: {
    total: number;
  };
}

export interface DailyRevenueProfitData {
  date: string;
  revenue: number;
  profit: number;
}

export interface DailyOrderCountData {
  date: string;
  count: number;
}

export interface ShippingStatusDistribution {
  status: string;
  count: number;
  percentage: number;
}

export class DashboardService {
  constructor() {}

  async getDashboardMetrics(
    dateRange?: 'today' | 'yesterday' | 'last7Days' | 'last30Days' | 'custom' | 'range' | 'lifetime',
    startDate?: string,
    endDate?: string
  ): Promise<DashboardMetrics> {
    try {
      // Calculate date filter based on range
      let dateFilter = {};
      const now = new Date();
      
      if (dateRange === 'today') {
        const startOfDay = new Date(now);
        startOfDay.setHours(0, 0, 0, 0);
        dateFilter = {
          placedAt: {
            gte: startOfDay
          }
        };
      } else if (dateRange === 'yesterday') {
        const yesterday = new Date(now);
        yesterday.setDate(yesterday.getDate() - 1);
        yesterday.setHours(0, 0, 0, 0);
        const endOfYesterday = new Date(yesterday);
        endOfYesterday.setHours(23, 59, 59, 999);
        dateFilter = {
          placedAt: {
            gte: yesterday,
            lte: endOfYesterday
          }
        };
      } else if (dateRange === 'last7Days') {
        const sevenDaysAgo = new Date(now);
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
        sevenDaysAgo.setHours(0, 0, 0, 0);
        dateFilter = {
          placedAt: {
            gte: sevenDaysAgo
          }
        };
      } else if (dateRange === 'last30Days') {
        const thirtyDaysAgo = new Date(now);
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        thirtyDaysAgo.setHours(0, 0, 0, 0);
        dateFilter = {
          placedAt: {
            gte: thirtyDaysAgo
          }
        };
      } else if (dateRange === 'custom' && startDate) {
        const start = new Date(startDate);
        start.setHours(0, 0, 0, 0);
        const end = new Date(startDate);
        end.setHours(23, 59, 59, 999);
        dateFilter = {
          placedAt: {
            gte: start,
            lte: end
          }
        };
      } else if (dateRange === 'range' && startDate && endDate) {
        const start = new Date(startDate);
        start.setHours(0, 0, 0, 0);
        const end = new Date(endDate);
        end.setHours(23, 59, 59, 999);
        dateFilter = {
          placedAt: {
            gte: start,
            lte: end
          }
        };
      } else if (dateRange === 'lifetime') {
        // No date filter - get all records
        dateFilter = {};
      }

      // Fetch all orders with their items
      const orders = await prisma.order.findMany({
        where: dateFilter,
        include: {
          items: true
        },
        orderBy: {
          placedAt: 'desc'
        }
      });

      // Initialize metrics
      let totalProfitUSD = 0;
      let totalRevenueUSD = 0;
      const profitByProduct = {
        Tirzepatide: 0,
        Retatrutide: 0,
        Semaglutide: 0,
        'BAC Water': 0
      };

      const ordersByStatus = {
        pending: 0,
        sent: 0,
        shipped: 0
      };

      // Process each order
      orders.forEach(order => {
        // Add to total profit and revenue
        totalProfitUSD += Number(order.profitUsd);
        totalRevenueUSD += Number(order.totalUsd);

        // Count by status
        if (order.status === 'pending' || order.status === 'sent' || order.status === 'shipped') {
          ordersByStatus[order.status]++;
        }

        // Calculate profit by product
        order.items.forEach(item => {
          const itemProfit = (Number(item.sellingPrice) - Number(item.buyingPrice)) * item.qty;
          
          // Determine product type from code
          let productType: keyof typeof profitByProduct;
          if (item.code.startsWith('TR')) {
            productType = 'Tirzepatide';
          } else if (item.code.startsWith('RT')) {
            productType = 'Retatrutide';
          } else if (item.code.startsWith('SM')) {
            productType = 'Semaglutide';
          } else if (item.code.startsWith('BA')) {
            productType = 'BAC Water';
          } else {
            // Skip unknown product codes
            return;
          }

          profitByProduct[productType] += itemProfit;
        });
      });

      // Convert INR (fixed rate of 86)
      const totalProfitINR = totalProfitUSD * 86;
      const totalRevenueINR = totalRevenueUSD * 86;

      // Calculate profit margin percentage
      const profitMarginPercentage = totalRevenueUSD > 0 ? (totalProfitUSD / totalRevenueUSD) * 100 : 0;

      // Get product count
      const productCount = await prisma.pricing.count();

      return {
        profit: {
          totalUSD: totalProfitUSD,
          totalINR: totalProfitINR,
          byProduct: profitByProduct
        },
        revenue: {
          totalUSD: totalRevenueUSD,
          totalINR: totalRevenueINR
        },
        profitMargin: {
          percentage: profitMarginPercentage
        },
        orders: {
          total: orders.length,
          byStatus: ordersByStatus
        },
        products: {
          total: productCount
        }
      };
    } catch (error) {
      console.error('Failed to fetch dashboard metrics:', error);
      throw error;
    }
  }

  async getDailyRevenueProfit(
    days?: number,
    dateRange?: 'today' | 'yesterday' | 'last7Days' | 'last30Days' | 'custom' | 'range' | 'lifetime',
    startDateParam?: string,
    endDateParam?: string
  ): Promise<DailyRevenueProfitData[]> {
    try {
      let startDate: Date;
      let endDate: Date;
      const now = new Date();

      // Calculate date range
      if (dateRange) {
        if (dateRange === 'today') {
          startDate = new Date(now);
          startDate.setHours(0, 0, 0, 0);
          endDate = new Date(now);
          endDate.setHours(23, 59, 59, 999);
        } else if (dateRange === 'yesterday') {
          startDate = new Date(now);
          startDate.setDate(startDate.getDate() - 1);
          startDate.setHours(0, 0, 0, 0);
          endDate = new Date(startDate);
          endDate.setHours(23, 59, 59, 999);
        } else if (dateRange === 'last7Days') {
          startDate = new Date(now);
          startDate.setDate(startDate.getDate() - 7);
          startDate.setHours(0, 0, 0, 0);
          endDate = new Date(now);
          endDate.setHours(23, 59, 59, 999);
        } else if (dateRange === 'last30Days') {
          startDate = new Date(now);
          startDate.setDate(startDate.getDate() - 30);
          startDate.setHours(0, 0, 0, 0);
          endDate = new Date(now);
          endDate.setHours(23, 59, 59, 999);
        } else if (dateRange === 'custom' && startDateParam) {
          startDate = new Date(startDateParam);
          startDate.setHours(0, 0, 0, 0);
          endDate = new Date(startDateParam);
          endDate.setHours(23, 59, 59, 999);
        } else if (dateRange === 'range' && startDateParam && endDateParam) {
          startDate = new Date(startDateParam);
          startDate.setHours(0, 0, 0, 0);
          endDate = new Date(endDateParam);
          endDate.setHours(23, 59, 59, 999);
        } else if (dateRange === 'lifetime') {
          // Get the earliest order date
          const earliestOrder = await prisma.order.findFirst({
            orderBy: {
              placedAt: 'asc'
            },
            select: {
              placedAt: true
            }
          });
          
          if (earliestOrder?.placedAt) {
            startDate = new Date(earliestOrder.placedAt);
            startDate.setHours(0, 0, 0, 0);
          } else {
            // No orders found, use today as start date
            startDate = new Date(now);
            startDate.setHours(0, 0, 0, 0);
          }
          endDate = new Date(now);
          endDate.setHours(23, 59, 59, 999);
        } else {
          // Default to 7 days
          startDate = new Date(now);
          startDate.setDate(startDate.getDate() - 7);
          startDate.setHours(0, 0, 0, 0);
          endDate = new Date(now);
          endDate.setHours(23, 59, 59, 999);
        }
      } else {
        // Use days parameter or default to 7
        const daysToUse = days || 7;
        startDate = new Date(now);
        startDate.setDate(startDate.getDate() - daysToUse);
        startDate.setHours(0, 0, 0, 0);
        endDate = new Date(now);
        endDate.setHours(23, 59, 59, 999);
      }

      const orders = await prisma.order.findMany({
        where: {
          placedAt: {
            gte: startDate,
            lte: endDate
          }
        },
        orderBy: {
          placedAt: 'asc'
        }
      });

      // Create a map to store daily data
      const dailyDataMap = new Map<string, { revenue: number; profit: number }>();

      // Initialize all days with zero values
      const currentDate = new Date(startDate);
      while (currentDate <= endDate) {
        const dateStr = currentDate.toISOString().split('T')[0];
        dailyDataMap.set(dateStr, { revenue: 0, profit: 0 });
        currentDate.setDate(currentDate.getDate() + 1);
      }

      // Aggregate revenue and profit by day
      orders.forEach(order => {
        const dateStr = order.placedAt!.toISOString().split('T')[0];
        const currentData = dailyDataMap.get(dateStr) || { revenue: 0, profit: 0 };
        
        currentData.revenue += Number(order.totalUsd);
        currentData.profit += Number(order.profitUsd);
        
        dailyDataMap.set(dateStr, currentData);
      });

      // Convert map to array
      const result: DailyRevenueProfitData[] = [];
      dailyDataMap.forEach((data, date) => {
        result.push({
          date,
          revenue: data.revenue,
          profit: data.profit
        });
      });

      return result;
    } catch (error) {
      console.error('Failed to fetch daily revenue and profit:', error);
      throw error;
    }
  }

  async getDailyOrderCount(
    days?: number,
    dateRange?: 'today' | 'yesterday' | 'last7Days' | 'last30Days' | 'custom' | 'range' | 'lifetime',
    startDateParam?: string,
    endDateParam?: string
  ): Promise<DailyOrderCountData[]> {
    try {
      let startDate: Date;
      let endDate: Date;
      const now = new Date();

      // Calculate date range
      if (dateRange) {
        if (dateRange === 'today') {
          startDate = new Date(now);
          startDate.setHours(0, 0, 0, 0);
          endDate = new Date(now);
          endDate.setHours(23, 59, 59, 999);
        } else if (dateRange === 'yesterday') {
          startDate = new Date(now);
          startDate.setDate(startDate.getDate() - 1);
          startDate.setHours(0, 0, 0, 0);
          endDate = new Date(startDate);
          endDate.setHours(23, 59, 59, 999);
        } else if (dateRange === 'last7Days') {
          startDate = new Date(now);
          startDate.setDate(startDate.getDate() - 7);
          startDate.setHours(0, 0, 0, 0);
          endDate = new Date(now);
          endDate.setHours(23, 59, 59, 999);
        } else if (dateRange === 'last30Days') {
          startDate = new Date(now);
          startDate.setDate(startDate.getDate() - 30);
          startDate.setHours(0, 0, 0, 0);
          endDate = new Date(now);
          endDate.setHours(23, 59, 59, 999);
        } else if (dateRange === 'custom' && startDateParam) {
          startDate = new Date(startDateParam);
          startDate.setHours(0, 0, 0, 0);
          endDate = new Date(startDateParam);
          endDate.setHours(23, 59, 59, 999);
        } else if (dateRange === 'range' && startDateParam && endDateParam) {
          startDate = new Date(startDateParam);
          startDate.setHours(0, 0, 0, 0);
          endDate = new Date(endDateParam);
          endDate.setHours(23, 59, 59, 999);
        } else if (dateRange === 'lifetime') {
          // Get the earliest order date
          const earliestOrder = await prisma.order.findFirst({
            orderBy: {
              placedAt: 'asc'
            },
            select: {
              placedAt: true
            }
          });
          
          if (earliestOrder?.placedAt) {
            startDate = new Date(earliestOrder.placedAt);
            startDate.setHours(0, 0, 0, 0);
          } else {
            // No orders found, use today as start date
            startDate = new Date(now);
            startDate.setHours(0, 0, 0, 0);
          }
          endDate = new Date(now);
          endDate.setHours(23, 59, 59, 999);
        } else {
          // Default to 30 days
          startDate = new Date(now);
          startDate.setDate(startDate.getDate() - 30);
          startDate.setHours(0, 0, 0, 0);
          endDate = new Date(now);
          endDate.setHours(23, 59, 59, 999);
        }
      } else {
        // Use days parameter or default to 30
        const daysToUse = days || 30;
        startDate = new Date(now);
        startDate.setDate(startDate.getDate() - daysToUse);
        startDate.setHours(0, 0, 0, 0);
        endDate = new Date(now);
        endDate.setHours(23, 59, 59, 999);
      }

      const orders = await prisma.order.findMany({
        where: {
          placedAt: {
            gte: startDate,
            lte: endDate
          }
        },
        orderBy: {
          placedAt: 'asc'
        },
        select: {
          placedAt: true
        }
      });

      // Create a map to store daily order counts
      const dailyOrderMap = new Map<string, number>();

      // Initialize all days with zero values
      const currentDate = new Date(startDate);
      while (currentDate <= endDate) {
        const dateStr = currentDate.toISOString().split('T')[0];
        dailyOrderMap.set(dateStr, 0);
        currentDate.setDate(currentDate.getDate() + 1);
      }

      // Count orders by day
      orders.forEach(order => {
        const dateStr = order.placedAt!.toISOString().split('T')[0];
        const currentCount = dailyOrderMap.get(dateStr) || 0;
        dailyOrderMap.set(dateStr, currentCount + 1);
      });

      // Convert map to array
      const result: DailyOrderCountData[] = [];
      dailyOrderMap.forEach((count, date) => {
        result.push({
          date,
          count
        });
      });

      return result;
    } catch (error) {
      console.error('Failed to fetch daily order count:', error);
      throw error;
    }
  }

  async getShippingStatusDistribution(): Promise<ShippingStatusDistribution[]> {
    try {
      // Get all orders with tracking status and sentToSupplier status
      const orders = await prisma.order.findMany({
        select: {
          trackingStatus: true,
          sentToSupplier: true
        }
      });

      // Count orders by status
      const statusCounts = new Map<string, number>();
      let totalOrders = 0;

      orders.forEach(order => {
        totalOrders++;
        
        // Check if order is not sent to supplier
        if (!order.sentToSupplier) {
          const currentCount = statusCounts.get('Not Sent') || 0;
          statusCounts.set('Not Sent', currentCount + 1);
          return;
        }
        
        // Normalize the tracking status display for sent orders
        let displayStatus: string;
        const status = order.trackingStatus?.toLowerCase() || '';
        
        if (!order.trackingStatus || status === 'info received') {
          displayStatus = 'Info Received';
        } else if (status === 'delivered') {
          displayStatus = 'Delivered';
        } else if (['in transit', 'pick up', 'out for delivery', 'alert', 'undelivered'].includes(status)) {
          displayStatus = 'In Transit';
        } else {
          displayStatus = 'Info Received'; // Default for unknown statuses
        }

        const currentCount = statusCounts.get(displayStatus) || 0;
        statusCounts.set(displayStatus, currentCount + 1);
      });

      // Convert to array with percentages
      const result: ShippingStatusDistribution[] = [];
      
      // Ensure all four statuses are included even if count is 0
      const allStatuses = ['Not Sent', 'Info Received', 'In Transit', 'Delivered'];
      
      allStatuses.forEach(status => {
        const count = statusCounts.get(status) || 0;
        const percentage = totalOrders > 0 ? Math.round((count / totalOrders) * 100) : 0;
        
        result.push({
          status,
          count,
          percentage
        });
      });

      // Sort by count descending
      result.sort((a, b) => b.count - a.count);

      return result;
    } catch (error) {
      console.error('Failed to fetch shipping status distribution:', error);
      throw error;
    }
  }
}