import axios from 'axios';
import { PrismaClient } from '../generated/prisma/index.js';

const prisma = new PrismaClient();

interface CoinGeckoResponse {
  bitcoin: {
    usd: number;
  };
}

export class CoinGeckoService {
  private baseUrl: string;
  private cacheExpiryMinutes = 5; // Cache for 5 minutes

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl;
  }

  async getBtcRate(): Promise<number> {
    try {
      // Check cache first
      const cachedRate = await prisma.exchangeRate.findUnique({
        where: { pair: 'BTC-USD' }
      });

      // If cache exists and is less than 5 minutes old, use it
      if (cachedRate) {
        const cacheAge = Date.now() - cachedRate.fetchedAt.getTime();
        if (cacheAge < this.cacheExpiryMinutes * 60 * 1000) {
          return Number(cachedRate.rate);
        }
      }

      // Fetch fresh rate from CoinGecko
      const response = await axios.get<CoinGeckoResponse>(
        `${this.baseUrl}/simple/price`,
        {
          params: {
            ids: 'bitcoin',
            vs_currencies: 'usd'
          },
          timeout: 5000
        }
      );

      const rate = response.data.bitcoin.usd;

      // Update cache
      await prisma.exchangeRate.upsert({
        where: { pair: 'BTC-USD' },
        update: { 
          rate: rate,
          fetchedAt: new Date()
        },
        create: {
          pair: 'BTC-USD',
          rate: rate
        }
      });

      // Also update BTC-INR rate (USD * 86)
      await prisma.exchangeRate.upsert({
        where: { pair: 'BTC-INR' },
        update: { 
          rate: rate * 86,
          fetchedAt: new Date()
        },
        create: {
          pair: 'BTC-INR',
          rate: rate * 86
        }
      });

      return rate;
    } catch (error) {
      console.error('Failed to fetch BTC rate from CoinGecko:', error);
      
      // Fall back to cached rate if available
      const cachedRate = await prisma.exchangeRate.findUnique({
        where: { pair: 'BTC-USD' }
      });
      
      if (cachedRate) {
        console.log('Using stale cached rate due to API failure');
        return Number(cachedRate.rate);
      }
      
      // Last resort: use a default rate
      console.log('Using default BTC rate of $100,000');
      return 100000;
    }
  }

  async getInrRate(): Promise<number> {
    const btcUsd = await this.getBtcRate();
    return btcUsd * 86;
  }
}