import { PDFDocument, StandardFonts, rgb } from 'pdf-lib';
import type { Order, OrderItem } from '../generated/prisma/index.js';
import { promises as fs } from 'fs';
import path from 'path';

export class PDFService {
  private getDiscountRate(orderNumber: string | null): number {
    if (!orderNumber) return 0.05;
    const match = orderNumber.match(/^ORD-\d{4}-(\d{4})$/);
    if (!match) return 0.05;
    const orderNum = parseInt(match[1]);
    return orderNum <= 35 ? 0.03 : 0.05;
  }

  private getDiscountPercentage(orderNumber: string | null): string {
    const rate = this.getDiscountRate(orderNumber);
    return `${(rate * 100).toFixed(0)}%`;
  }

  async generateOrderPDF(
    order: Order & { items: OrderItem[] },
    outputPath: string
  ): Promise<string> {
    // Create a new PDF document
    const pdfDoc = await PDFDocument.create();
    const page = pdfDoc.addPage([612, 792]); // Letter size
    
    // Load fonts
    const helveticaBold = await pdfDoc.embedFont(StandardFonts.HelveticaBold);
    const helvetica = await pdfDoc.embedFont(StandardFonts.Helvetica);
    
    const { width, height } = page.getSize();
    let yPosition = height - 50;

    // Header
    page.drawText('PEPTIDE ORDER', {
      x: 50,
      y: yPosition,
      size: 20,
      font: helveticaBold,
      color: rgb(0, 0, 0),
    });

    yPosition -= 30;

    // Order ID and Date
    page.drawText(`Order: ${order.orderNumber || order.id}`, {
      x: 50,
      y: yPosition,
      size: 12,
      font: helvetica,
    });

    yPosition -= 20;

    const orderDate = new Date(order.placedAt).toLocaleString('en-US', {
      timeZone: 'Asia/Shanghai',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
    page.drawText(`Date: ${orderDate} (Asia/Shanghai)`, {
      x: 50,
      y: yPosition,
      size: 12,
      font: helvetica,
    });

    yPosition -= 40;

    // Customer Information
    page.drawText('CUSTOMER INFORMATION', {
      x: 50,
      y: yPosition,
      size: 14,
      font: helveticaBold,
    });

    yPosition -= 20;

    const customerInfo = [
      order.customerName,
      order.street1,
      order.street2 ? order.street2 : null,
      `${order.city}, ${order.state} ${order.postalCode}`,
      order.country,
    ].filter(Boolean);

    for (const line of customerInfo) {
      page.drawText(line, {
        x: 50,
        y: yPosition,
        size: 11,
        font: helvetica,
      });
      yPosition -= 15;
    }

    yPosition -= 20;

    // Order Items Header
    page.drawText('ORDER ITEMS', {
      x: 50,
      y: yPosition,
      size: 14,
      font: helveticaBold,
    });

    yPosition -= 25;

    // Table headers
    const headers = ['Item', 'Dose', 'Qty', 'Price', 'Total'];
    const columnWidths = [200, 100, 50, 80, 80];
    let xPosition = 50;

    for (let i = 0; i < headers.length; i++) {
      page.drawText(headers[i], {
        x: xPosition,
        y: yPosition,
        size: 11,
        font: helveticaBold,
      });
      xPosition += columnWidths[i];
    }

    yPosition -= 5;
    
    // Draw line under headers
    page.drawLine({
      start: { x: 50, y: yPosition },
      end: { x: 560, y: yPosition },
      thickness: 1,
      color: rgb(0, 0, 0),
    });

    yPosition -= 20;

    // Order items
    let subtotal = 0;
    for (const item of order.items) {
      xPosition = 50;
      const itemTotal = Number(item.buyingPrice) * item.qty;
      subtotal += itemTotal;

      const itemData = [
        item.code,
        item.dose,
        item.qty.toString(),
        `$${Number(item.buyingPrice).toFixed(2)}`,
        `$${itemTotal.toFixed(2)}`,
      ];

      for (let i = 0; i < itemData.length; i++) {
        page.drawText(itemData[i], {
          x: xPosition,
          y: yPosition,
          size: 11,
          font: helvetica,
        });
        xPosition += columnWidths[i];
      }

      yPosition -= 20;
    }

    yPosition -= 10;

    // Draw line above totals
    page.drawLine({
      start: { x: 50, y: yPosition },
      end: { x: 560, y: yPosition },
      thickness: 1,
      color: rgb(0, 0, 0),
    });

    yPosition -= 25;

    // Totals (excluding profit information)
    const shipping = 40;
    const discountRate = this.getDiscountRate(order.orderNumber);
    const discount = subtotal * discountRate;
    const grandTotal = subtotal + shipping - discount;

    const totals = [
      ['Subtotal:', `$${subtotal.toFixed(2)}`],
      ['Shipping:', `$${shipping.toFixed(2)}`],
      [`Discount (${this.getDiscountPercentage(order.orderNumber)}):`, `-$${discount.toFixed(2)}`],
      ['', ''],
      ['TOTAL:', `$${grandTotal.toFixed(2)}`],
    ];

    for (const [label, value] of totals) {
      if (label) {
        page.drawText(label, {
          x: 400,
          y: yPosition,
          size: 11,
          font: label === 'TOTAL:' ? helveticaBold : helvetica,
        });
        page.drawText(value, {
          x: 480,
          y: yPosition,
          size: 11,
          font: label === 'TOTAL:' ? helveticaBold : helvetica,
        });
      }
      yPosition -= 20;
    }


    // Save the PDF
    const pdfBytes = await pdfDoc.save();
    
    // Ensure directory exists
    const dir = path.dirname(outputPath);
    await fs.mkdir(dir, { recursive: true });
    
    // Write to file
    await fs.writeFile(outputPath, pdfBytes);
    
    return outputPath;
  }

  async generateBulkOrdersPDF(
    orders: (Order & { items: OrderItem[] })[],
    outputPath: string
  ): Promise<string> {
    // Create a new PDF document
    const pdfDoc = await PDFDocument.create();
    
    // Load fonts
    const helveticaBold = await pdfDoc.embedFont(StandardFonts.HelveticaBold);
    const helvetica = await pdfDoc.embedFont(StandardFonts.Helvetica);

    // Summary Page
    const summaryPage = pdfDoc.addPage([612, 792]);
    const { width, height } = summaryPage.getSize();
    let yPosition = height - 50;

    // Summary Header
    summaryPage.drawText('BULK ORDERS SUMMARY', {
      x: 50,
      y: yPosition,
      size: 24,
      font: helveticaBold,
      color: rgb(0, 0, 0),
    });

    yPosition -= 40;

    // Date range
    const orderDates = orders.map(o => new Date(o.placedAt).getTime());
    const minDate = new Date(Math.min(...orderDates));
    const maxDate = new Date(Math.max(...orderDates));
    
    const formatDate = (date: Date) => date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });

    summaryPage.drawText(`Date Range: ${formatDate(minDate)} - ${formatDate(maxDate)}`, {
      x: 50,
      y: yPosition,
      size: 12,
      font: helvetica,
    });

    yPosition -= 30;

    // Summary statistics
    summaryPage.drawText(`Total Orders: ${orders.length}`, {
      x: 50,
      y: yPosition,
      size: 12,
      font: helvetica,
    });

    yPosition -= 20;

    // Calculate totals
    let totalValue = 0;
    let totalItems = 0;

    for (const order of orders) {
      // Calculate using buying prices for supplier, not selling prices
      let orderTotal = 0;
      const subtotal = order.items.reduce((sum, item) => sum + Number(item.buyingPrice) * item.qty, 0);
      const shipping = 40;
      const discountRate = this.getDiscountRate(order.orderNumber);
    const discount = subtotal * discountRate;
      orderTotal = subtotal + shipping - discount;
      totalValue += orderTotal;
      totalItems += order.items.reduce((sum, item) => sum + item.qty, 0);
    }

    summaryPage.drawText(`Total Items: ${totalItems}`, {
      x: 50,
      y: yPosition,
      size: 12,
      font: helvetica,
    });

    yPosition -= 20;

    summaryPage.drawText(`Total Amount to Pay Supplier: $${totalValue.toFixed(2)}`, {
      x: 50,
      y: yPosition,
      size: 12,
      font: helveticaBold,
    });

    yPosition -= 40;


    // Orders list
    summaryPage.drawText('Orders Included:', {
      x: 50,
      y: yPosition,
      size: 14,
      font: helveticaBold,
    });

    yPosition -= 20;

    // Table headers
    const headers = ['Order #', 'Customer', 'Date', 'Total'];
    const columnWidths = [150, 200, 120, 80];
    let xPosition = 50;

    for (let i = 0; i < headers.length; i++) {
      summaryPage.drawText(headers[i], {
        x: xPosition,
        y: yPosition,
        size: 10,
        font: helveticaBold,
      });
      xPosition += columnWidths[i];
    }

    yPosition -= 5;

    // Draw line under headers
    summaryPage.drawLine({
      start: { x: 50, y: yPosition },
      end: { x: 600, y: yPosition },
      thickness: 1,
      color: rgb(0, 0, 0),
    });

    yPosition -= 15;

    // List orders (with pagination if needed)
    let orderCount = 0;
    for (const order of orders) {
      if (yPosition < 100) {
        // Add continuation note
        summaryPage.drawText('...continued on individual order pages', {
          x: 50,
          y: yPosition,
          size: 10,
          font: helvetica,
          color: rgb(0.5, 0.5, 0.5),
        });
        break;
      }

      xPosition = 50;
      // Calculate using buying prices for supplier, not selling prices
      const orderSubtotal = order.items.reduce((sum, item) => sum + Number(item.buyingPrice) * item.qty, 0);
      const orderShipping = 40;
      const orderDiscountRate = this.getDiscountRate(order.orderNumber);
      const orderDiscount = orderSubtotal * orderDiscountRate;
      const orderTotal = orderSubtotal + orderShipping - orderDiscount;
      
      const orderData = [
        order.orderNumber || order.id.substring(0, 8) + '...',
        order.customerName.length > 25 ? order.customerName.substring(0, 22) + '...' : order.customerName,
        new Date(order.placedAt).toLocaleDateString(),
        `$${orderTotal.toFixed(2)}`,
      ];

      for (let i = 0; i < orderData.length; i++) {
        summaryPage.drawText(orderData[i], {
          x: xPosition,
          y: yPosition,
          size: 9,
          font: helvetica,
        });
        xPosition += columnWidths[i];
      }

      yPosition -= 15;
      orderCount++;
    }

    // Generate individual order pages
    for (const order of orders) {
      const page = pdfDoc.addPage([612, 792]);
      await this.drawOrderOnPage(page, order, helveticaBold, helvetica);
    }

    // Save the PDF
    const pdfBytes = await pdfDoc.save();
    
    // Ensure directory exists
    const dir = path.dirname(outputPath);
    await fs.mkdir(dir, { recursive: true });
    
    // Write to file
    await fs.writeFile(outputPath, pdfBytes);
    
    return outputPath;
  }

  private async drawOrderOnPage(
    page: any,
    order: Order & { items: OrderItem[] },
    helveticaBold: any,
    helvetica: any
  ): Promise<void> {
    const { width, height } = page.getSize();
    let yPosition = height - 50;

    // Header
    page.drawText('PEPTIDE ORDER', {
      x: 50,
      y: yPosition,
      size: 20,
      font: helveticaBold,
      color: rgb(0, 0, 0),
    });

    yPosition -= 30;

    // Order ID and Date
    page.drawText(`Order: ${order.orderNumber || order.id}`, {
      x: 50,
      y: yPosition,
      size: 12,
      font: helvetica,
    });

    yPosition -= 20;

    const orderDate = new Date(order.placedAt).toLocaleString('en-US', {
      timeZone: 'Asia/Shanghai',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
    page.drawText(`Date: ${orderDate} (Asia/Shanghai)`, {
      x: 50,
      y: yPosition,
      size: 12,
      font: helvetica,
    });

    yPosition -= 40;

    // Customer Information
    page.drawText('CUSTOMER INFORMATION', {
      x: 50,
      y: yPosition,
      size: 14,
      font: helveticaBold,
    });

    yPosition -= 20;

    const customerInfo = [
      order.customerName,
      order.street1,
      order.street2 ? order.street2 : null,
      `${order.city}, ${order.state} ${order.postalCode}`,
      order.country,
    ].filter(Boolean);

    for (const line of customerInfo) {
      page.drawText(line, {
        x: 50,
        y: yPosition,
        size: 11,
        font: helvetica,
      });
      yPosition -= 15;
    }

    yPosition -= 20;

    // Order Items Header
    page.drawText('ORDER ITEMS', {
      x: 50,
      y: yPosition,
      size: 14,
      font: helveticaBold,
    });

    yPosition -= 25;

    // Table headers
    const headers = ['Item', 'Dose', 'Qty', 'Price', 'Total'];
    const columnWidths = [200, 100, 50, 80, 80];
    let xPosition = 50;

    for (let i = 0; i < headers.length; i++) {
      page.drawText(headers[i], {
        x: xPosition,
        y: yPosition,
        size: 11,
        font: helveticaBold,
      });
      xPosition += columnWidths[i];
    }

    yPosition -= 5;
    
    // Draw line under headers
    page.drawLine({
      start: { x: 50, y: yPosition },
      end: { x: 560, y: yPosition },
      thickness: 1,
      color: rgb(0, 0, 0),
    });

    yPosition -= 20;

    // Order items
    let subtotal = 0;
    for (const item of order.items) {
      xPosition = 50;
      const itemTotal = Number(item.buyingPrice) * item.qty;
      subtotal += itemTotal;

      const itemData = [
        item.code,
        item.dose,
        item.qty.toString(),
        `$${Number(item.buyingPrice).toFixed(2)}`,
        `$${itemTotal.toFixed(2)}`,
      ];

      for (let i = 0; i < itemData.length; i++) {
        page.drawText(itemData[i], {
          x: xPosition,
          y: yPosition,
          size: 11,
          font: helvetica,
        });
        xPosition += columnWidths[i];
      }

      yPosition -= 20;
    }

    yPosition -= 10;

    // Draw line above totals
    page.drawLine({
      start: { x: 50, y: yPosition },
      end: { x: 560, y: yPosition },
      thickness: 1,
      color: rgb(0, 0, 0),
    });

    yPosition -= 25;

    // Totals (excluding profit information)
    const shipping = 40;
    const discountRate = this.getDiscountRate(order.orderNumber);
    const discount = subtotal * discountRate;
    const grandTotal = subtotal + shipping - discount;

    const totals = [
      ['Subtotal:', `$${subtotal.toFixed(2)}`],
      ['Shipping:', `$${shipping.toFixed(2)}`],
      [`Discount (${this.getDiscountPercentage(order.orderNumber)}):`, `-$${discount.toFixed(2)}`],
      ['', ''],
      ['TOTAL:', `$${grandTotal.toFixed(2)}`],
    ];

    for (const [label, value] of totals) {
      if (label) {
        page.drawText(label, {
          x: 400,
          y: yPosition,
          size: 11,
          font: label === 'TOTAL:' ? helveticaBold : helvetica,
        });
        page.drawText(value, {
          x: 480,
          y: yPosition,
          size: 11,
          font: label === 'TOTAL:' ? helveticaBold : helvetica,
        });
      }
      yPosition -= 20;
    }

  }
}