import ExcelJS from 'exceljs';
import type { Order, OrderItem } from '../generated/prisma/index.js';
import { promises as fs } from 'fs';
import path from 'path';

export class ExcelService {
  private getDiscountRate(orderNumber: string | null): number {
    if (!orderNumber) return 0.05;
    const match = orderNumber.match(/^ORD-\d{4}-(\d{4})$/);
    if (!match) return 0.05;
    const orderNum = parseInt(match[1]);
    return orderNum <= 35 ? 0.03 : 0.05;
  }

  private getDiscountPercentage(orderNumber: string | null): string {
    const rate = this.getDiscountRate(orderNumber);
    return `${(rate * 100).toFixed(0)}%`;
  }

  async generateOrderExcel(
    order: Order & { items: OrderItem[] },
    outputPath: string
  ): Promise<string> {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Order');

    // Set column widths
    worksheet.columns = [
      { header: 'Field', key: 'field', width: 25 },
      { header: 'Value', key: 'value', width: 40 },
    ];

    // Title
    worksheet.mergeCells('A1:B1');
    const titleCell = worksheet.getCell('A1');
    titleCell.value = 'PEPTIDE ORDER';
    titleCell.font = { size: 16, bold: true };
    titleCell.alignment = { horizontal: 'center' };

    // Add empty row
    worksheet.addRow([]);

    // Order Information
    const orderDate = new Date(order.placedAt).toLocaleString('en-US', {
      timeZone: 'Asia/Shanghai',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });

    worksheet.addRow({ field: 'Order #', value: order.orderNumber || order.id });
    worksheet.addRow({ field: 'Date', value: `${orderDate} (Asia/Shanghai)` });

    // Add empty row
    worksheet.addRow([]);

    // Customer Information
    worksheet.addRow({ field: 'CUSTOMER INFORMATION', value: '' }).font = { bold: true };
    worksheet.addRow({ field: 'Name', value: order.customerName });
    worksheet.addRow({ field: 'Street 1', value: order.street1 });
    if (order.street2) {
      worksheet.addRow({ field: 'Street 2', value: order.street2 });
    }
    worksheet.addRow({ field: 'City', value: order.city });
    worksheet.addRow({ field: 'State', value: order.state });
    worksheet.addRow({ field: 'Postal Code', value: order.postalCode });
    worksheet.addRow({ field: 'Country', value: order.country });

    // Add empty row
    worksheet.addRow([]);

    // Create items sheet
    const itemsSheet = workbook.addWorksheet('Order Items');
    
    // Set columns for items sheet
    itemsSheet.columns = [
      { header: 'Item Code', key: 'code', width: 15 },
      { header: 'Dose', key: 'dose', width: 20 },
      { header: 'Quantity', key: 'qty', width: 10 },
      { header: 'Unit Price', key: 'price', width: 15 },
      { header: 'Total', key: 'total', width: 15 },
    ];

    // Style the header row
    itemsSheet.getRow(1).font = { bold: true };
    itemsSheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' },
    };

    // Add items
    let subtotal = 0;
    order.items.forEach(item => {
      const itemTotal = Number(item.buyingPrice) * item.qty;
      subtotal += itemTotal;

      itemsSheet.addRow({
        code: item.code,
        dose: item.dose,
        qty: item.qty,
        price: `$${Number(item.buyingPrice).toFixed(2)}`,
        total: `$${itemTotal.toFixed(2)}`,
      });
    });

    // Add totals
    itemsSheet.addRow([]);
    
    const shipping = 40;
    const discountRate = this.getDiscountRate(order.orderNumber);
    const discount = subtotal * discountRate;
    const grandTotal = subtotal + shipping - discount;

    const totalsStartRow = itemsSheet.rowCount + 1;
    
    itemsSheet.addRow({ dose: 'Subtotal:', total: `$${subtotal.toFixed(2)}` });
    itemsSheet.addRow({ dose: 'Shipping:', total: `$${shipping.toFixed(2)}` });
    itemsSheet.addRow({ dose: `Discount (${this.getDiscountPercentage(order.orderNumber)}):`, total: `-$${discount.toFixed(2)}` });
    itemsSheet.addRow([]);
    const totalRow = itemsSheet.addRow({ dose: 'TOTAL:', total: `$${grandTotal.toFixed(2)}` });
    totalRow.font = { bold: true };

    // Format totals section
    for (let i = totalsStartRow; i <= itemsSheet.rowCount; i++) {
      const row = itemsSheet.getRow(i);
      row.getCell('dose').alignment = { horizontal: 'right' };
      row.getCell('total').font = { ...row.getCell('total').font, bold: true };
    }

    // Auto-fit columns
    itemsSheet.columns.forEach(column => {
      if (column.values) {
        let maxLength = 0;
        column.values.forEach(value => {
          const length = value ? value.toString().length : 0;
          if (length > maxLength) {
            maxLength = length;
          }
        });
        column.width = Math.min(maxLength + 2, 50);
      }
    });

    // Ensure directory exists
    const dir = path.dirname(outputPath);
    await fs.mkdir(dir, { recursive: true });

    // Save the workbook
    await workbook.xlsx.writeFile(outputPath);
    
    return outputPath;
  }

  async generateBulkOrdersExcel(
    orders: (Order & { items: OrderItem[] })[],
    outputPath: string
  ): Promise<string> {
    const workbook = new ExcelJS.Workbook();
    
    // Summary Sheet
    const summarySheet = workbook.addWorksheet('Summary');
    
    // Title
    summarySheet.mergeCells('A1:D1');
    const titleCell = summarySheet.getCell('A1');
    titleCell.value = 'BULK ORDERS SUMMARY';
    titleCell.font = { size: 18, bold: true };
    titleCell.alignment = { horizontal: 'center' };
    
    summarySheet.addRow([]);
    
    // Date range
    const orderDates = orders.map(o => new Date(o.placedAt).getTime());
    const minDate = new Date(Math.min(...orderDates));
    const maxDate = new Date(Math.max(...orderDates));
    
    const formatDate = (date: Date) => date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
    
    summarySheet.addRow(['Date Range:', `${formatDate(minDate)} - ${formatDate(maxDate)}`]);
    summarySheet.addRow(['Total Orders:', orders.length]);
    
    // Calculate totals
    let totalValue = 0;
    let totalItems = 0;
    
    for (const order of orders) {
      // Calculate using buying prices for supplier, not selling prices
      const subtotal = order.items.reduce((sum, item) => sum + Number(item.buyingPrice) * item.qty, 0);
      const shipping = 40;
      const discountRate = this.getDiscountRate(order.orderNumber);
    const discount = subtotal * discountRate;
      const orderTotal = subtotal + shipping - discount;
      totalValue += orderTotal;
      totalItems += order.items.reduce((sum, item) => sum + item.qty, 0);
    }
    
    summarySheet.addRow(['Total Items:', totalItems]);
    summarySheet.addRow(['Total Amount to Pay Supplier:', `$${totalValue.toFixed(2)}`]);
    
    // Format summary sheet
    summarySheet.columns = [
      { width: 25 },
      { width: 30 },
      { width: 15 },
      { width: 15 },
    ];
    
    // Bold labels
    for (let i = 3; i <= summarySheet.rowCount; i++) {
      const row = summarySheet.getRow(i);
      if (row.getCell(1).value) {
        row.getCell(1).font = { bold: true };
      }
    }
    
    // Order Details Sheet (with merged cells for clean presentation)
    const detailsSheet = workbook.addWorksheet('Order Details');
    
    // Hide gridlines for cleaner look
    detailsSheet.views = [{ showGridLines: false }];
    
    detailsSheet.columns = [
      { header: 'Order #', key: 'orderId', width: 12 },
      { header: 'Customer Name', key: 'name', width: 25 },
      { header: 'Shipping Address', key: 'address', width: 40 },
      { header: 'Product', key: 'code', width: 12 },
      { header: 'Order Date', key: 'date', width: 15 },
      { header: 'Qty', key: 'qty', width: 8 },
      { header: 'Unit Price', key: 'unitPrice', width: 12 },
      { header: 'Line Total', key: 'totalPrice', width: 12 },
      { header: 'Discount', key: 'discount', width: 12 },
      { header: 'Total Due', key: 'amountToPay', width: 15 },
    ];
    
    // Style the header row with professional dark blue
    const headerRow = detailsSheet.getRow(1);
    headerRow.font = { bold: true, color: { argb: 'FFFFFFFF' }, size: 12 };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FF2B4C8C' }, // Professional dark blue
    };
    headerRow.height = 25;
    headerRow.alignment = { vertical: 'middle', horizontal: 'center' };
    
    let currentRow = 2; // Start after header
    let orderIndex = 0;
    
    orders.forEach((order, idx) => {
      // Add empty row between orders (except for the first one)
      if (idx > 0) {
        detailsSheet.addRow({});
        currentRow++;
      }
      
      const address = `${order.street1}${order.street2 ? ', ' + order.street2 : ''}, ${order.city}, ${order.state} ${order.postalCode}, ${order.country}`;
      const orderDate = new Date(order.placedAt).toLocaleDateString('en-US');
      const firstItem = order.items[0];
      
      // Determine background color for this order (alternating light colors)
      const orderBgColor = orderIndex % 2 === 0 ? 'FFF0F5FF' : 'FFF5FFF0'; // Light blue vs light yellow
      orderIndex++;
      
      if (order.items.length === 1) {
        // Single item order
        const itemTotal = Number(firstItem.buyingPrice) * firstItem.qty;
        const shipping = 40;
        const discountRate = this.getDiscountRate(order.orderNumber);
        const discount = itemTotal * discountRate;
        const amountToPay = itemTotal + shipping - discount;
        
        const row = detailsSheet.addRow({
          orderId: order.orderNumber || order.id.substring(0, 8),
          name: order.customerName,
          address: address,
          code: firstItem.code,
          date: orderDate,
          qty: firstItem.qty,
          unitPrice: Number(firstItem.buyingPrice).toFixed(2),
          totalPrice: itemTotal.toFixed(2),
          discount: discount.toFixed(2),
          amountToPay: amountToPay.toFixed(2),
        });
        
        // Style the row
        row.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: orderBgColor }
        };
        
        // Apply alignment
        row.getCell('orderId').alignment = { horizontal: 'center' };
        row.getCell('name').alignment = { horizontal: 'left' };
        row.getCell('address').alignment = { horizontal: 'left' };
        row.getCell('code').alignment = { horizontal: 'center' };
        row.getCell('date').alignment = { horizontal: 'center' };
        row.getCell('qty').alignment = { horizontal: 'center' };
        row.getCell('unitPrice').alignment = { horizontal: 'right' };
        row.getCell('totalPrice').alignment = { horizontal: 'right' };
        row.getCell('discount').alignment = { horizontal: 'right' };
        row.getCell('amountToPay').alignment = { horizontal: 'right' };
        
        // Add subtle borders
        for (let col = 1; col <= 10; col++) {
          row.getCell(col).border = {
            top: { style: 'thin', color: { argb: 'FFD0D0D0' } },
            bottom: { style: 'thin', color: { argb: 'FFD0D0D0' } },
            left: { style: 'thin', color: { argb: 'FFD0D0D0' } },
            right: { style: 'thin', color: { argb: 'FFD0D0D0' } }
          };
        }
        
        currentRow++;
      } else {
        // Multiple items order - merge cells for order-level data
        const startRow = currentRow;
        const endRow = currentRow + order.items.length - 1;
        
        // Calculate order totals
        const subtotal = order.items.reduce((sum, item) => sum + Number(item.buyingPrice) * item.qty, 0);
        const shipping = 40;
        const discountRate = this.getDiscountRate(order.orderNumber);
    const discount = subtotal * discountRate;
        const amountToPay = subtotal + shipping - discount;
        
        // Add all items with consistent styling
        for (let i = 0; i < order.items.length; i++) {
          const item = order.items[i];
          const row = detailsSheet.addRow({
            orderId: i === 0 ? (order.orderNumber || order.id.substring(0, 8)) : undefined,
            name: i === 0 ? order.customerName : undefined,
            address: i === 0 ? address : undefined,
            code: item.code,
            date: i === 0 ? orderDate : undefined,
            qty: item.qty,
            unitPrice: Number(item.buyingPrice).toFixed(2),
            totalPrice: (Number(item.buyingPrice) * item.qty).toFixed(2),
            discount: i === 0 ? discount.toFixed(2) : undefined,
            amountToPay: i === 0 ? amountToPay.toFixed(2) : undefined,
          });
          
          // Apply consistent styling
          row.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: orderBgColor }
          };
          
          // Apply alignment
          row.getCell('code').alignment = { horizontal: 'center' };
          row.getCell('qty').alignment = { horizontal: 'center' };
          row.getCell('unitPrice').alignment = { horizontal: 'right' };
          row.getCell('totalPrice').alignment = { horizontal: 'right' };
          
          // Add borders to all cells
          for (let col = 1; col <= 10; col++) {
            row.getCell(col).border = {
              top: { style: 'thin', color: { argb: 'FFD0D0D0' } },
              bottom: { style: 'thin', color: { argb: 'FFD0D0D0' } },
              left: { style: 'thin', color: { argb: 'FFD0D0D0' } },
              right: { style: 'thin', color: { argb: 'FFD0D0D0' } }
            };
          }
          
          currentRow++;
        }
        
        // Merge cells for order-level data
        detailsSheet.mergeCells(startRow, 1, endRow, 1); // Order ID
        detailsSheet.mergeCells(startRow, 2, endRow, 2); // Name
        detailsSheet.mergeCells(startRow, 3, endRow, 3); // Address
        detailsSheet.mergeCells(startRow, 5, endRow, 5); // Date
        detailsSheet.mergeCells(startRow, 9, endRow, 9); // Discount
        detailsSheet.mergeCells(startRow, 10, endRow, 10); // Amount to Pay
        
        // Style merged cells with proper alignment
        const mergedCellAlignment = {
          1: 'center',  // Order ID
          2: 'left',    // Name
          3: 'left',    // Address
          5: 'center',  // Date
          9: 'right',   // Discount
          10: 'right'   // Amount to Pay
        };
        
        for (let col of [1, 2, 3, 5, 9, 10]) {
          const cell = detailsSheet.getCell(startRow, col);
          cell.alignment = { 
            vertical: 'middle', 
            horizontal: mergedCellAlignment[col] as any,
            wrapText: col === 3 // Wrap text for address only
          };
          
          // Bold the totals
          if (col === 9 || col === 10) {
            cell.font = { bold: true };
          }
        }
        
        currentRow = endRow + 1;
      }
    });
    
    // Order Line Items Sheet (flat format for analysis)
    const lineItemsSheet = workbook.addWorksheet('Order Line Items');
    
    // Hide gridlines
    lineItemsSheet.views = [{ showGridLines: false }];
    
    lineItemsSheet.columns = [
      { header: 'Order #', key: 'orderId', width: 12 },
      { header: 'Customer Name', key: 'name', width: 25 },
      { header: 'Shipping Address', key: 'address', width: 40 },
      { header: 'Product', key: 'code', width: 12 },
      { header: 'Order Date', key: 'date', width: 15 },
      { header: 'Qty', key: 'qty', width: 8 },
      { header: 'Unit Price', key: 'unitPrice', width: 12 },
      { header: 'Line Total', key: 'totalPrice', width: 12 },
      { header: 'Discount', key: 'discount', width: 12 },
      { header: 'Total Due', key: 'amountToPay', width: 15 },
    ];
    
    // Style the header row
    const lineItemsHeaderRow = lineItemsSheet.getRow(1);
    lineItemsHeaderRow.font = { bold: true, color: { argb: 'FFFFFFFF' }, size: 12 };
    lineItemsHeaderRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FF2B4C8C' },
    };
    lineItemsHeaderRow.height = 25;
    lineItemsHeaderRow.alignment = { vertical: 'middle', horizontal: 'center' };
    
    // Add all items in flat format
    orders.forEach(order => {
      const address = `${order.street1}${order.street2 ? ', ' + order.street2 : ''}, ${order.city}, ${order.state} ${order.postalCode}, ${order.country}`;
      const orderDate = new Date(order.placedAt).toLocaleDateString('en-US');
      
      // Calculate order totals once
      const subtotal = order.items.reduce((sum, item) => sum + Number(item.buyingPrice) * item.qty, 0);
      const shipping = 40;
      const discountRate = this.getDiscountRate(order.orderNumber);
    const discount = subtotal * discountRate;
      const amountToPay = subtotal + shipping - discount;
      
      order.items.forEach((item, index) => {
        lineItemsSheet.addRow({
          orderId: order.orderNumber || order.id.substring(0, 8),
          name: order.customerName,
          address: address,
          code: item.code,
          date: orderDate,
          qty: item.qty,
          unitPrice: Number(item.buyingPrice).toFixed(2),
          totalPrice: (Number(item.buyingPrice) * item.qty).toFixed(2),
          discount: index === 0 ? discount.toFixed(2) : '', // Only show on first item
          amountToPay: index === 0 ? amountToPay.toFixed(2) : '', // Only show on first item
        });
      });
    });
    
    // Format currency columns and add dollar signs
    const currencyColumns = [7, 8, 9, 10]; // Unit Price, Total Price, Discount, Amount to Pay
    detailsSheet.eachRow((row, rowNumber) => {
      if (rowNumber > 1) { // Skip header
        currencyColumns.forEach(col => {
          const cell = row.getCell(col);
          if (cell.value && cell.value !== undefined) {
            // Prepend dollar sign if not already there
            const value = cell.value.toString();
            if (!value.startsWith('$') && !value.startsWith('-$')) {
              if (value.startsWith('-')) {
                cell.value = '-$' + value.substring(1);
              } else {
                cell.value = '$' + value;
              }
            }
          }
        });
      }
    });
    
    // Auto-fit all sheets
    [summarySheet, detailsSheet, lineItemsSheet].forEach(sheet => {
      sheet.columns.forEach(column => {
        if (column.values && column.width) {
          let maxLength = column.width;
          column.values.forEach(value => {
            const length = value ? value.toString().length : 0;
            if (length > maxLength) {
              maxLength = length;
            }
          });
          column.width = Math.min(maxLength + 2, 50);
        }
      });
    });
    
    // Ensure directory exists
    const dir = path.dirname(outputPath);
    await fs.mkdir(dir, { recursive: true });
    
    // Save the workbook
    await workbook.xlsx.writeFile(outputPath);
    
    return outputPath;
  }
}