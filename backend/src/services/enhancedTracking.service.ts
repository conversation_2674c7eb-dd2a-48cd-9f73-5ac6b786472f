import type { FastifyInstance } from 'fastify';
import type { PrismaClient } from '../generated/prisma';
import { ParcelsAppService, TrackingStatus } from './parcelsapp.service';
import { CronJob } from 'cron';

interface TrackingCacheEntry {
  trackingNumber: string;
  destinationCountry: string;
  statusCode: string;
  statusText: string;
  lastUpdate?: Date;
  lastEventDescription?: string;
  uuid?: string;
  cachedAt: Date;
  expiresAt: Date;
  isFinalStatus: boolean;
}

interface CacheMetrics {
  hits: number;
  misses: number;
  apiCalls: number;
  cacheSize: number;
  hitRate: number;
}

export class EnhancedTrackingService extends ParcelsAppService {
  private prisma: PrismaClient;
  private metrics: CacheMetrics = {
    hits: 0,
    misses: 0,
    apiCalls: 0,
    cacheSize: 0,
    hitRate: 0
  };
  private backgroundRefreshJob?: CronJob;
  private cacheWarmingJob?: CronJob;

  // Different TTLs based on status (in milliseconds)
  private readonly TTL_FINAL_STATUS = 7 * 24 * 60 * 60 * 1000; // 7 days for delivered/failed
  private readonly TTL_IN_TRANSIT = 2 * 60 * 60 * 1000; // 2 hours for in-transit
  private readonly TTL_PENDING = 6 * 60 * 60 * 1000; // 6 hours for pending/info received
  private readonly TTL_DEFAULT = 4 * 60 * 60 * 1000; // 4 hours default

  constructor(fastify: FastifyInstance, apiKey: string, prisma: PrismaClient) {
    super(fastify, apiKey);
    this.prisma = prisma;
    
    // Initialize background jobs
    this.initializeBackgroundJobs();
    
    // Log metrics every 30 minutes
    setInterval(() => this.logMetrics(), 30 * 60 * 1000);
  }

  /**
   * Initialize background jobs for cache refresh and warming
   */
  private initializeBackgroundJobs(): void {
    // Background refresh for in-transit packages (every 30 minutes)
    this.backgroundRefreshJob = new CronJob('*/30 * * * *', async () => {
      await this.refreshInTransitPackages();
    });
    this.backgroundRefreshJob.start();

    // Cache warming for recent orders (every hour)
    this.cacheWarmingJob = new CronJob('0 * * * *', async () => {
      await this.warmCache();
    });
    this.cacheWarmingJob.start();

    // Clean expired cache entries (every hour)
    const cleanupJob = new CronJob('0 * * * *', async () => {
      await this.cleanupExpiredCache();
    });
    cleanupJob.start();

    this.fastify.log.info('Enhanced tracking background jobs initialized');
  }

  /**
   * Get tracking status with database-backed cache
   */
  async getTrackingStatus(trackingNumber: string, destinationCountry: string = 'United States'): Promise<TrackingStatus | null> {
    try {
      // Check database cache first
      const cached = await this.getFromCache(trackingNumber, destinationCountry);
      
      if (cached) {
        this.metrics.hits++;
        this.fastify.log.info(`Cache hit for ${trackingNumber} (DB cache)`);
        
        // Record cache hit metric
        await this.recordMetric('cache_hit', 1, { trackingNumber });
        
        return {
          trackingNumber: cached.trackingNumber,
          status: cached.statusCode as TrackingStatus['status'],
          statusText: cached.statusText as TrackingStatus['statusText'],
          lastUpdate: cached.lastUpdate?.toISOString(),
          lastEventDescription: cached.lastEventDescription || undefined
        };
      }

      this.metrics.misses++;
      this.metrics.apiCalls++;

      // Record cache miss metric
      await this.recordMetric('cache_miss', 1, { trackingNumber });

      // Fall back to parent implementation (which calls the API)
      const result = await super.getTrackingStatus(trackingNumber, destinationCountry);
      
      if (result) {
        // Save to database cache
        await this.saveToCache(trackingNumber, destinationCountry, result);
        
        // Record API call metric
        await this.recordMetric('api_call', 1, { trackingNumber, status: result.statusText });
      }

      return result;
    } catch (error) {
      this.fastify.log.error(`Error in enhanced getTrackingStatus: ${error}`);
      // Fall back to parent implementation
      return super.getTrackingStatus(trackingNumber, destinationCountry);
    }
  }

  /**
   * Get tracking status from database cache
   */
  private async getFromCache(trackingNumber: string, destinationCountry: string): Promise<TrackingCacheEntry | null> {
    try {
      const cached = await this.prisma.trackingCache.findUnique({
        where: {
          trackingNumber_destinationCountry: {
            trackingNumber,
            destinationCountry
          }
        }
      });

      if (!cached) return null;

      // Check if cache is expired
      if (new Date() > cached.expiresAt) {
        // Delete expired entry
        await this.prisma.trackingCache.delete({
          where: {
            trackingNumber_destinationCountry: {
              trackingNumber,
              destinationCountry
            }
          }
        });
        return null;
      }

      return cached;
    } catch (error) {
      this.fastify.log.error(`Error getting from cache: ${error}`);
      return null;
    }
  }

  /**
   * Save tracking status to database cache
   */
  private async saveToCache(trackingNumber: string, destinationCountry: string, status: TrackingStatus): Promise<void> {
    try {
      const isFinalStatus = this.isFinalStatus(status.statusText);
      const ttl = this.getTTLForStatus(status.statusText);
      const expiresAt = new Date(Date.now() + ttl);

      // Get UUID from in-memory map if available
      const uuid = this.getUUIDForTracking(trackingNumber);

      await this.prisma.trackingCache.upsert({
        where: {
          trackingNumber_destinationCountry: {
            trackingNumber,
            destinationCountry
          }
        },
        update: {
          statusCode: status.status,
          statusText: status.statusText,
          lastUpdate: status.lastUpdate ? new Date(status.lastUpdate) : null,
          lastEventDescription: status.lastEventDescription,
          uuid: uuid || undefined,
          cachedAt: new Date(),
          expiresAt,
          isFinalStatus
        },
        create: {
          trackingNumber,
          destinationCountry,
          statusCode: status.status,
          statusText: status.statusText,
          lastUpdate: status.lastUpdate ? new Date(status.lastUpdate) : null,
          lastEventDescription: status.lastEventDescription,
          uuid: uuid || undefined,
          expiresAt,
          isFinalStatus
        }
      });

      this.fastify.log.info(`Saved tracking status to DB cache: ${trackingNumber} - ${status.statusText}`);
    } catch (error) {
      this.fastify.log.error(`Error saving to cache: ${error}`);
    }
  }

  /**
   * Get UUID for tracking number from in-memory map
   */
  private getUUIDForTracking(trackingNumber: string): string | null {
    // Access the private uuidMap through reflection (not ideal but works)
    const uuidMap = (this as any).uuidMap as Map<string, string>;
    return uuidMap.get(trackingNumber) || null;
  }

  /**
   * Determine if a status is final (no further updates expected)
   */
  private isFinalStatus(statusText: string): boolean {
    const finalStatuses = ['Delivered', 'Undelivered', 'Expired'];
    return finalStatuses.includes(statusText);
  }

  /**
   * Get TTL based on tracking status
   */
  private getTTLForStatus(statusText: string): number {
    if (this.isFinalStatus(statusText)) {
      return this.TTL_FINAL_STATUS;
    }
    
    switch (statusText) {
      case 'In Transit':
      case 'Out for Delivery':
        return this.TTL_IN_TRANSIT;
      case 'Info Received':
      case 'Pick Up':
        return this.TTL_PENDING;
      default:
        return this.TTL_DEFAULT;
    }
  }

  /**
   * Refresh tracking for in-transit packages
   */
  private async refreshInTransitPackages(): Promise<void> {
    try {
      this.fastify.log.info('Starting background refresh for in-transit packages');

      // Get all in-transit packages from cache
      const inTransitPackages = await this.prisma.trackingCache.findMany({
        where: {
          statusText: {
            in: ['In Transit', 'Out for Delivery']
          },
          expiresAt: {
            gt: new Date()
          }
        },
        take: 20 // Limit to prevent overwhelming the API
      });

      this.fastify.log.info(`Found ${inTransitPackages.length} in-transit packages to refresh`);

      // Refresh each package
      for (const pkg of inTransitPackages) {
        try {
          // Restore UUID to memory map if available
          if (pkg.uuid) {
            const uuidMap = (this as any).uuidMap as Map<string, string>;
            uuidMap.set(pkg.trackingNumber, pkg.uuid);
          }

          const newStatus = await super.getTrackingStatus(pkg.trackingNumber, pkg.destinationCountry);
          
          if (newStatus && newStatus.statusText !== pkg.statusText) {
            await this.saveToCache(pkg.trackingNumber, pkg.destinationCountry, newStatus);
            this.fastify.log.info(`Updated status for ${pkg.trackingNumber}: ${pkg.statusText} -> ${newStatus.statusText}`);
          }
        } catch (error) {
          this.fastify.log.error(`Error refreshing tracking for ${pkg.trackingNumber}: ${error}`);
        }
      }

      await this.recordMetric('background_refresh_completed', inTransitPackages.length);
    } catch (error) {
      this.fastify.log.error(`Error in background refresh: ${error}`);
    }
  }

  /**
   * Warm cache with recent orders
   */
  private async warmCache(): Promise<void> {
    try {
      this.fastify.log.info('Starting cache warming for recent orders');

      // Get recent orders with tracking numbers that aren't delivered
      const recentOrders = await this.prisma.order.findMany({
        where: {
          tracking17: { not: null },
          status: {
            notIn: ['delivered']
          },
          placedAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // Last 7 days
          }
        },
        select: {
          tracking17: true,
          country: true
        },
        take: 30
      });

      this.fastify.log.info(`Found ${recentOrders.length} recent orders to warm cache`);

      let warmed = 0;
      for (const order of recentOrders) {
        if (!order.tracking17) continue;

        // Check if already in cache
        const cached = await this.getFromCache(order.tracking17, order.country || 'United States');
        if (!cached) {
          try {
            await this.getTrackingStatus(order.tracking17, order.country || 'United States');
            warmed++;
            // Add delay to avoid rate limiting
            await new Promise(resolve => setTimeout(resolve, 1000));
          } catch (error) {
            this.fastify.log.error(`Error warming cache for ${order.tracking17}: ${error}`);
          }
        }
      }

      await this.recordMetric('cache_warming_completed', warmed);
      this.fastify.log.info(`Cache warming completed: ${warmed} entries added`);
    } catch (error) {
      this.fastify.log.error(`Error in cache warming: ${error}`);
    }
  }

  /**
   * Clean up expired cache entries
   */
  private async cleanupExpiredCache(): Promise<void> {
    try {
      const result = await this.prisma.trackingCache.deleteMany({
        where: {
          expiresAt: {
            lt: new Date()
          }
        }
      });

      if (result.count > 0) {
        this.fastify.log.info(`Cleaned up ${result.count} expired cache entries`);
        await this.recordMetric('cache_cleanup', result.count);
      }
    } catch (error) {
      this.fastify.log.error(`Error cleaning up cache: ${error}`);
    }
  }

  /**
   * Record tracking metric
   */
  private async recordMetric(metricType: string, value: number, metadata?: any): Promise<void> {
    try {
      await this.prisma.trackingMetric.create({
        data: {
          metricType,
          value,
          metadata: metadata || undefined
        }
      });
    } catch (error) {
      this.fastify.log.error(`Error recording metric: ${error}`);
    }
  }

  /**
   * Log cache metrics
   */
  private async logMetrics(): Promise<void> {
    try {
      // Calculate hit rate
      const total = this.metrics.hits + this.metrics.misses;
      this.metrics.hitRate = total > 0 ? (this.metrics.hits / total) * 100 : 0;

      // Get cache size
      const cacheSize = await this.prisma.trackingCache.count();
      this.metrics.cacheSize = cacheSize;

      this.fastify.log.info({
        metrics: this.metrics,
        apiReduction: `${this.metrics.hitRate.toFixed(2)}%`
      }, 'Tracking cache metrics');

      // Record aggregate metrics
      await this.recordMetric('hit_rate', this.metrics.hitRate);
      await this.recordMetric('cache_size', cacheSize);
      await this.recordMetric('api_calls_saved', this.metrics.hits);
    } catch (error) {
      this.fastify.log.error(`Error logging metrics: ${error}`);
    }
  }

  /**
   * Get cache statistics
   */
  async getCacheStats(): Promise<{
    metrics: CacheMetrics;
    cacheByStatus: any;
    recentMetrics: any[];
  }> {
    // Get cache breakdown by status
    const cacheByStatus = await this.prisma.trackingCache.groupBy({
      by: ['statusText'],
      _count: true
    });

    // Get recent metrics
    const recentMetrics = await this.prisma.trackingMetric.findMany({
      where: {
        createdAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 100
    });

    return {
      metrics: this.metrics,
      cacheByStatus,
      recentMetrics
    };
  }

  /**
   * Bulk fetch tracking statuses with optimized caching
   */
  async bulkGetTrackingStatuses(trackingNumbers: Array<{ trackingNumber: string; destinationCountry?: string }>): Promise<Map<string, TrackingStatus | null>> {
    const results = new Map<string, TrackingStatus | null>();
    const toFetch: Array<{ trackingNumber: string; destinationCountry: string }> = [];

    // First, check cache for all tracking numbers
    for (const { trackingNumber, destinationCountry = 'United States' } of trackingNumbers) {
      const cached = await this.getFromCache(trackingNumber, destinationCountry);
      
      if (cached) {
        this.metrics.hits++;
        results.set(trackingNumber, {
          trackingNumber: cached.trackingNumber,
          status: cached.statusCode as TrackingStatus['status'],
          statusText: cached.statusText as TrackingStatus['statusText'],
          lastUpdate: cached.lastUpdate?.toISOString(),
          lastEventDescription: cached.lastEventDescription || undefined
        });
      } else {
        this.metrics.misses++;
        toFetch.push({ trackingNumber, destinationCountry });
      }
    }

    // Fetch missing tracking statuses in batches
    const batchSize = 5;
    for (let i = 0; i < toFetch.length; i += batchSize) {
      const batch = toFetch.slice(i, i + batchSize);
      
      await Promise.all(
        batch.map(async ({ trackingNumber, destinationCountry }) => {
          try {
            const status = await this.getTrackingStatus(trackingNumber, destinationCountry);
            results.set(trackingNumber, status);
          } catch (error) {
            this.fastify.log.error(`Error fetching tracking for ${trackingNumber}: ${error}`);
            results.set(trackingNumber, null);
          }
        })
      );

      // Small delay between batches
      if (i + batchSize < toFetch.length) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }

    return results;
  }

  /**
   * Stop background jobs
   */
  stopBackgroundJobs(): void {
    if (this.backgroundRefreshJob) {
      this.backgroundRefreshJob.stop();
    }
    if (this.cacheWarmingJob) {
      this.cacheWarmingJob.stop();
    }
    this.fastify.log.info('Enhanced tracking background jobs stopped');
  }
}