import type { FastifyInstance } from 'fastify';

export interface TrackingStatus {
  trackingNumber: string;
  status: '40' | '10' | '20' | '30' | '35' | '36' | '50' | '0' | '60'; // Status codes
  statusText: 'Not Found' | 'Info Received' | 'In Transit' | 'Pick Up' | 'Out for Delivery' | 'Undelivered' | 'Delivered' | 'Alert' | 'Expired';
  lastUpdate?: string;
}

// Map 17track status codes to text
const STATUS_MAP: Record<string, TrackingStatus['statusText']> = {
  '0': 'Not Found',
  '10': 'Info Received',
  '20': 'In Transit',
  '30': 'Pick Up',
  '35': 'Out for Delivery',
  '36': 'Undelivered',
  '40': 'Delivered',
  '50': 'Alert',
  '60': 'Expired'
};

// 17track API response interfaces
interface TrackInfo {
  number: string;
  carrier: number;
  track_info?: {
    latest_status?: {
      status: string;
      sub_status?: string;
      sub_status_descr?: string;
    };
    latest_event?: {
      time_iso: string;
      time_utc: string;
      description: string;
      location: string;
      stage?: string;
    };
  };
}

interface RegisterResponse {
  code: number;
  data?: {
    accepted?: Array<{ number: string; carrier?: number }>;
    rejected?: Array<{ number: string; error?: { code: number; message: string } }>;
  };
}

interface TrackInfoResponse {
  code: number;
  data?: {
    accepted?: TrackInfo[];
    rejected?: Array<{ number: string; error?: { code: number; message: string } }>;
    errors?: Array<{ code: number; message: string }>;
  };
}

export class SeventeenTrackService {
  private baseUrl = 'https://api.17track.net/track/v2';

  constructor(private fastify: FastifyInstance, private apiKey: string) {
  }

  /**
   * Register a tracking number with 17track API
   */
  async registerTrackingNumber(trackingNumber: string): Promise<void> {
    if (!this.apiKey) {
      this.fastify.log.warn('17track API key not configured, skipping registration');
      return;
    }

    try {
      const response = await fetch(`${this.baseUrl}/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          '17token': this.apiKey
        },
        body: JSON.stringify([
          { number: trackingNumber }
        ])
      });

      const responseData = await response.json() as RegisterResponse;

      if (!response.ok) {
        this.fastify.log.error(`Failed to register tracking number: ${response.status} ${response.statusText}`, responseData);
        // Don't throw - we'll allow tracking numbers even if registration fails
        return;
      }

      // Check if registration was successful
      if (responseData.data?.accepted && responseData.data.accepted.length > 0) {
        this.fastify.log.info(`Successfully registered tracking number ${trackingNumber}`);
      } else if (responseData.data?.rejected && responseData.data.rejected.length > 0) {
        this.fastify.log.warn(`Tracking number ${trackingNumber} was rejected: ${JSON.stringify(responseData.data.rejected)}`);
      }
    } catch (error) {
      this.fastify.log.error(`Error registering tracking number: ${error}`);
      // Don't throw - we'll allow tracking numbers even if registration fails
    }
  }

  /**
   * Get tracking status for a tracking number
   * Uses 17track's gettrackinfo endpoint to query tracking data
   * 
   * IMPORTANT: Tracking numbers must be registered first and may take time to process.
   * The API works asynchronously - after registration, 17track needs time to fetch
   * tracking data from carriers. This can take anywhere from minutes to hours.
   * 
   * For immediate results, consider using webhook integration instead of polling.
   */
  async getTrackingStatus(trackingNumber: string): Promise<TrackingStatus | null> {
    if (!this.apiKey) {
      this.fastify.log.warn('17track API key not configured, returning default status');
      return {
        trackingNumber,
        status: '10',
        statusText: 'Info Received',
        lastUpdate: new Date().toISOString()
      };
    }

    try {
      // Query tracking info using the gettrackinfo endpoint
      const response = await fetch(`${this.baseUrl}/gettrackinfo`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          '17token': this.apiKey
        },
        body: JSON.stringify([
          { number: trackingNumber }
        ])
      });

      if (!response.ok) {
        this.fastify.log.error(`Failed to get tracking info: ${response.status} ${response.statusText}`);
        return null;
      }

      const responseData = await response.json() as TrackInfoResponse;
      
      // Check for errors in the response
      if (responseData.data?.errors && responseData.data.errors.length > 0) {
        this.fastify.log.error(`API error for tracking ${trackingNumber}:`, responseData.data.errors);
        
        // For MVP, return default status when API errors occur
        return {
          trackingNumber,
          status: '10',
          statusText: 'Info Received',
          lastUpdate: new Date().toISOString()
        };
      }
      
      // Parse the response to extract tracking status
      if (responseData.data?.accepted && responseData.data.accepted.length > 0) {
        const trackInfo = responseData.data.accepted[0];
        
        // Extract status from the nested structure
        const apiStatus = trackInfo.track_info?.latest_status?.status || 'NotFound';
        const lastUpdate = trackInfo.track_info?.latest_event?.time_utc || new Date().toISOString();
        
        // Map API status strings to our numeric codes
        const statusMap: Record<string, TrackingStatus['status']> = {
          'NotFound': '0',
          'InfoReceived': '10',
          'InTransit': '20',
          'PickUp': '30',
          'OutForDelivery': '35',
          'Undelivered': '36',
          'Delivered': '40',
          'Alert': '50',
          'Expired': '60'
        };
        
        const statusCode = statusMap[apiStatus] || '0';
        const statusText = STATUS_MAP[statusCode] || 'Not Found';
        
        return {
          trackingNumber,
          status: statusCode as TrackingStatus['status'],
          statusText,
          lastUpdate
        };
      } else if (responseData.data?.rejected && responseData.data.rejected.length > 0) {
        // Tracking number was rejected or not found
        const rejection = responseData.data.rejected[0];
        this.fastify.log.warn(`Tracking number ${trackingNumber} rejected:`, rejection.error);
        
        // Check if it's a "no tracking info yet" error
        if (rejection.error?.code === -18019909) {
          // This means the tracking number is registered but data isn't available yet
          return {
            trackingNumber,
            status: '10',
            statusText: 'Info Received',
            lastUpdate: new Date().toISOString()
          };
        }
        
        return {
          trackingNumber,
          status: '0',
          statusText: 'Not Found',
          lastUpdate: new Date().toISOString()
        };
      }

      // Default response if no data
      return {
        trackingNumber,
        status: '10',
        statusText: 'Info Received',
        lastUpdate: new Date().toISOString()
      };
    } catch (error) {
      this.fastify.log.error(`Error getting tracking status: ${error}`);
      
      // Return a default status on error
      return {
        trackingNumber,
        status: '10',
        statusText: 'Info Received',
        lastUpdate: new Date().toISOString()
      };
    }
  }

  /**
   * Generate tracking URL for 17track.net
   */
  generateTrackingUrl(trackingNumber: string): string {
    return `https://t.17track.net/en#nums=${encodeURIComponent(trackingNumber)}`;
  }
}