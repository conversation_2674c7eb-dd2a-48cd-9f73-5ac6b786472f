import nodemailer from 'nodemailer';
import type { Order, OrderItem } from '../generated/prisma/index.js';
import { PDFService } from './pdf.service';
import { ExcelService } from './excel.service';
import path from 'path';
import fs from 'fs/promises';

interface EmailConfig {
  gmailUser: string;
  gmailAppPassword: string;
}

class EmailService {
  private transporter: nodemailer.Transporter | null = null;
  private config: EmailConfig;
  private readonly maxRetries = 3;
  private readonly retryDelay = 2000; // 2 seconds
  private pdfService: PDFService;
  private excelService: ExcelService;

  constructor() {
    this.config = {
      gmailUser: process.env.GMAIL_USER || '',
      gmailAppPassword: process.env.GOOGLE_APP_PASSWORD || '',
    };
    this.pdfService = new PDFService();
    this.excelService = new ExcelService();
  }

  private getDiscountRate(orderNumber: string | null): number {
    if (!orderNumber) return 0.05;
    const match = orderNumber.match(/^ORD-\d{4}-(\d{4})$/);
    if (!match) return 0.05;
    const orderNum = parseInt(match[1]);
    return orderNum <= 35 ? 0.03 : 0.05;
  }

  private getDiscountPercentage(orderNumber: string | null): string {
    const rate = this.getDiscountRate(orderNumber);
    return `${(rate * 100).toFixed(0)}%`;
  }

  private async initializeTransporter() {
    if (!this.config.gmailUser || !this.config.gmailAppPassword) {
      throw new Error('Email configuration missing. Please set GMAIL_USER and GOOGLE_APP_PASSWORD environment variables.');
    }

    if (!this.transporter) {
      this.transporter = nodemailer.createTransport({
        service: 'gmail',
        auth: {
          user: this.config.gmailUser,
          pass: this.config.gmailAppPassword,
        },
      });

      // Verify the transporter configuration
      await this.transporter.verify();
    }
  }

  async sendOrderToSupplier(order: Order & { items: OrderItem[] }, supplierEmail: string = '<EMAIL>'): Promise<void> {
    await this.initializeTransporter();

    // Generate PDF and Excel files
    const pdfPath = path.join(process.cwd(), 'exports', `order-${order.id}.pdf`);
    const excelPath = path.join(process.cwd(), 'exports', `order-${order.id}.xlsx`);

    try {
      // Ensure exports directory exists
      await fs.mkdir(path.join(process.cwd(), 'exports'), { recursive: true });

      // Generate files
      await this.pdfService.generateOrderPDF(order, pdfPath);
      await this.excelService.generateOrderExcel(order, excelPath);

      // Prepare email content
      const emailContent = this.generateEmailContent(order);

      // Email options
      const mailOptions: nodemailer.SendMailOptions = {
        from: this.config.gmailUser,
        to: supplierEmail,
        subject: `New Peptide Order - ${order.orderNumber || order.id} - ${order.customerName}`,
        html: emailContent,
        attachments: [
          {
            filename: `order-${order.id}.pdf`,
            path: pdfPath,
          },
          {
            filename: `order-${order.id}.xlsx`,
            path: excelPath,
          },
        ],
      };

      // Send email with retry logic
      let lastError: Error | null = null;
      for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
        try {
          const info = await this.transporter!.sendMail(mailOptions);
          console.log(`Email sent successfully to ${supplierEmail}. Message ID: ${info.messageId}`);
          
          // Clean up temporary files
          await this.cleanupFiles([pdfPath, excelPath]);
          
          return;
        } catch (error) {
          lastError = error as Error;
          console.error(`Email send attempt ${attempt} failed:`, error);
          
          if (attempt < this.maxRetries) {
            await this.delay(this.retryDelay * attempt);
          }
        }
      }

      // If all retries failed, throw the last error
      throw new Error(`Failed to send email after ${this.maxRetries} attempts: ${lastError?.message}`);
    } catch (error) {
      // Clean up files even if email fails
      await this.cleanupFiles([pdfPath, excelPath]).catch(console.error);
      throw error;
    }
  }

  private generateEmailContent(order: Order & { items: OrderItem[] }): string {
    const itemsHtml = order.items
      .map(
        (item) => `
        <tr>
          <td style="border: 1px solid #ddd; padding: 8px;">${item.code}</td>
          <td style="border: 1px solid #ddd; padding: 8px;">${item.dose}</td>
          <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${item.qty}</td>
          <td style="border: 1px solid #ddd; padding: 8px; text-align: right;">$${(Number(item.buyingPrice) * item.qty).toFixed(2)}</td>
        </tr>
      `
      )
      .join('');

    const subtotal = order.items.reduce((sum, item) => sum + Number(item.buyingPrice) * item.qty, 0);
    const shipping = 40;
    const discountRate = this.getDiscountRate(order.orderNumber);
    const discount = subtotal * discountRate;

    return `
      <!DOCTYPE html>
      <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 800px; margin: 0 auto; padding: 20px; }
            .header { background-color: #4f46e5; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background-color: #f9fafb; padding: 30px; border-radius: 0 0 8px 8px; }
            .info-section { margin-bottom: 30px; }
            .info-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
            .info-item { margin-bottom: 10px; }
            .label { font-weight: bold; color: #6b7280; }
            table { width: 100%; border-collapse: collapse; margin: 20px 0; }
            th { background-color: #e5e7eb; text-align: left; padding: 12px; border: 1px solid #ddd; }
            .totals { margin-top: 20px; text-align: right; }
            .total-row { margin: 5px 0; }
            .grand-total { font-size: 1.2em; font-weight: bold; color: #4f46e5; margin-top: 10px; padding-top: 10px; border-top: 2px solid #e5e7eb; }
            .footer { margin-top: 40px; padding-top: 20px; border-top: 1px solid #e5e7eb; text-align: center; color: #6b7280; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>New Peptide Order</h1>
              <p>Order: ${order.orderNumber || order.id}</p>
            </div>
            
            <div class="content">
              <div class="info-section">
                <h2>Customer Information</h2>
                <div class="info-grid">
                  <div>
                    <div class="info-item">
                      <span class="label">Name:</span> ${order.customerName}
                    </div>
                    <div class="info-item">
                      <span class="label">Order Date:</span> ${new Date(order.placedAt).toLocaleString()}
                    </div>
                  </div>
                  <div>
                    <div class="info-item">
                      <span class="label">Shipping Address:</span><br>
                      ${order.street1}<br>
                      ${order.street2 ? order.street2 + '<br>' : ''}
                      ${order.city}, ${order.state} ${order.postalCode}<br>
                      ${order.country}
                    </div>
                  </div>
                </div>
              </div>

              <div class="info-section">
                <h2>Order Items</h2>
                <table>
                  <thead>
                    <tr>
                      <th>Product Code</th>
                      <th>Dose</th>
                      <th style="text-align: center;">Quantity</th>
                      <th style="text-align: right;">Total</th>
                    </tr>
                  </thead>
                  <tbody>
                    ${itemsHtml}
                  </tbody>
                </table>
              </div>

              <div class="totals">
                <div class="total-row">Subtotal: $${subtotal.toFixed(2)}</div>
                <div class="total-row">Shipping: $${shipping.toFixed(2)}</div>
                <div class="total-row">Discount (${this.getDiscountPercentage(order.orderNumber)}): -$${discount.toFixed(2)}</div>
                <div class="grand-total">Grand Total: $${Number(order.totalUsd).toFixed(2)}</div>
              </div>

              ${order.paymentUrl ? `
                <div class="info-section">
                  <h3>Payment Information</h3>
                  <p><span class="label">Transaction URL:</span> <a href="${order.paymentUrl}">${order.paymentUrl}</a></p>
                </div>
              ` : ''}

              <div class="footer">
                <p>This is an automated email. Please process this order at your earliest convenience.</p>
                <p>If you have any questions, please contact us.</p>
              </div>
            </div>
          </div>
        </body>
      </html>
    `;
  }

  private async cleanupFiles(paths: string[]): Promise<void> {
    for (const filePath of paths) {
      try {
        await fs.unlink(filePath);
      } catch (error) {
        console.error(`Failed to delete file ${filePath}:`, error);
      }
    }
  }

  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  async sendBulkOrdersToSupplier(orders: (Order & { items: OrderItem[] })[], pdfPath: string, excelPath: string, supplierEmail: string = '<EMAIL>'): Promise<void> {
    await this.initializeTransporter();

    try {
      // Prepare email content
      const emailContent = this.generateBulkEmailContent(orders);

      // Email options
      const mailOptions: nodemailer.SendMailOptions = {
        from: this.config.gmailUser,
        to: supplierEmail,
        subject: `Bulk Peptide Orders (${orders.length} orders) - ${new Date().toLocaleDateString()}`,
        html: emailContent,
        attachments: [
          {
            filename: path.basename(pdfPath),
            path: pdfPath,
          },
          {
            filename: path.basename(excelPath),
            path: excelPath,
          },
        ],
      };

      // Check file sizes
      const pdfStats = await fs.stat(pdfPath);
      const excelStats = await fs.stat(excelPath);
      const totalSize = pdfStats.size + excelStats.size;
      
      // Gmail attachment limit is 25MB
      if (totalSize > 25 * 1024 * 1024) {
        throw new Error(`Total file size (${(totalSize / 1024 / 1024).toFixed(2)}MB) exceeds Gmail's 25MB attachment limit`);
      }

      // Send email with retry logic
      let lastError: Error | null = null;
      for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
        try {
          const info = await this.transporter!.sendMail(mailOptions);
          console.log(`Bulk email sent successfully to ${supplierEmail}. Message ID: ${info.messageId}`);
          return;
        } catch (error) {
          lastError = error as Error;
          console.error(`Bulk email send attempt ${attempt} failed:`, error);
          
          if (attempt < this.maxRetries) {
            await this.delay(this.retryDelay * attempt);
          }
        }
      }

      // If all retries failed, throw the last error
      throw new Error(`Failed to send bulk email after ${this.maxRetries} attempts: ${lastError?.message}`);
    } catch (error) {
      throw error;
    }
  }

  private generateBulkEmailContent(orders: (Order & { items: OrderItem[] })[]): string {
    // Calculate summary statistics
    let totalSupplierPayment = 0;
    let totalItems = 0;
    
    for (const order of orders) {
      // Calculate using buying prices for supplier, not selling prices
      const subtotal = order.items.reduce((sum, item) => sum + Number(item.buyingPrice) * item.qty, 0);
      const shipping = 40;
      const discountRate = this.getDiscountRate(order.orderNumber);
      const discount = subtotal * discountRate;
      const orderTotal = subtotal + shipping - discount;
      totalSupplierPayment += orderTotal;
      totalItems += order.items.reduce((sum, item) => sum + item.qty, 0);
    }


    const ordersTableHtml = orders
      .map((order) => {
        // Calculate order total using buying prices
        const subtotal = order.items.reduce((sum, item) => sum + Number(item.buyingPrice) * item.qty, 0);
        const shipping = 40;
        const discountRate = this.getDiscountRate(order.orderNumber);
        const discount = subtotal * discountRate;
        const orderTotal = subtotal + shipping - discount;
        
        return `
        <tr>
          <td style="border: 1px solid #ddd; padding: 8px; font-size: 12px;">${order.orderNumber || order.id.substring(0, 8) + '...'}</td>
          <td style="border: 1px solid #ddd; padding: 8px;">${order.customerName}</td>
          <td style="border: 1px solid #ddd; padding: 8px;">${new Date(order.placedAt).toLocaleDateString()}</td>
          <td style="border: 1px solid #ddd; padding: 8px; text-align: right;">$${orderTotal.toFixed(2)}</td>
        </tr>
      `;
      })
      .join('');

    return `
      <!DOCTYPE html>
      <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 900px; margin: 0 auto; padding: 20px; }
            .header { background-color: #4f46e5; color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background-color: #f9fafb; padding: 30px; border-radius: 0 0 8px 8px; }
            .summary-card { background: white; padding: 20px; border-radius: 8px; margin-bottom: 30px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
            .summary-grid { display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px; margin-top: 15px; }
            .summary-item { padding: 15px; background: #f3f4f6; border-radius: 6px; }
            .summary-label { font-size: 14px; color: #6b7280; margin-bottom: 5px; }
            .summary-value { font-size: 24px; font-weight: bold; color: #1f2937; }
            table { width: 100%; border-collapse: collapse; margin: 20px 0; background: white; }
            th { background-color: #e5e7eb; text-align: left; padding: 12px; border: 1px solid #ddd; font-weight: 600; }
            .footer { margin-top: 40px; padding-top: 20px; border-top: 1px solid #e5e7eb; text-align: center; color: #6b7280; }
            .highlight { color: #4f46e5; font-weight: bold; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>Bulk Peptide Orders</h1>
              <p style="font-size: 18px; margin: 10px 0;">${orders.length} Orders • ${new Date().toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}</p>
            </div>
            
            <div class="content">
              <div class="summary-card">
                <h2 style="margin-top: 0; color: #1f2937;">Order Summary</h2>
                <div class="summary-grid">
                  <div class="summary-item">
                    <div class="summary-label">Total Orders</div>
                    <div class="summary-value">${orders.length}</div>
                  </div>
                  <div class="summary-item">
                    <div class="summary-label">Total Items</div>
                    <div class="summary-value">${totalItems}</div>
                  </div>
                </div>
                <div style="margin-top: 20px; padding: 15px; background: #4f46e5; color: white; border-radius: 6px; text-align: center;">
                  <div style="font-size: 14px; margin-bottom: 5px;">Total Amount to Pay Supplier</div>
                  <div style="font-size: 28px; font-weight: bold;">$${totalSupplierPayment.toFixed(2)}</div>
                </div>
              </div>

              <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                <h2 style="margin-top: 0; color: #1f2937;">Orders Included</h2>
                <table>
                  <thead>
                    <tr>
                      <th>Order ID</th>
                      <th>Customer</th>
                      <th>Date</th>
                      <th style="text-align: right;">Total</th>
                    </tr>
                  </thead>
                  <tbody>
                    ${ordersTableHtml}
                  </tbody>
                </table>
              </div>

              <div class="footer">
                <p><strong>Important:</strong> This email contains ${orders.length} peptide orders.</p>
                <p>Please find the detailed order information in the attached PDF and Excel files.</p>
                <p>The PDF contains individual pages for each order, while the Excel file provides structured data for analysis.</p>
                <p style="margin-top: 20px; color: #4f46e5;">This is an automated bulk order notification. Please process these orders at your earliest convenience.</p>
              </div>
            </div>
          </div>
        </body>
      </html>
    `;
  }
}

export const emailService = new EmailService();