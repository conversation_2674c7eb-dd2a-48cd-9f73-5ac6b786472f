import type { FastifyInstance } from 'fastify';

export interface TrackingStatus {
  trackingNumber: string;
  status: '40' | '10' | '20' | '30' | '35' | '36' | '50' | '0' | '60'; // Status codes for compatibility
  statusText: 'Not Found' | 'Info Received' | 'In Transit' | 'Pick Up' | 'Out for Delivery' | 'Undelivered' | 'Delivered' | 'Alert' | 'Expired';
  lastUpdate?: string;
  lastEventDescription?: string; // Detailed description of the last tracking event
}

// Parcels API interfaces
interface ParcelsShipment {
  trackingId: string;
  destinationCountry?: string;
  language?: string;
}

interface ParcelsTrackingRequest {
  apiKey: string;
  shipments: ParcelsShipment[];
  language?: string;
  webhookUrl?: string;
}

interface ParcelsTrackingResponse {
  uuid: string;
  done?: boolean;
  fromCache?: boolean;
  shipments?: ParcelsShipmentResult[];
}

interface ParcelsShipmentResult {
  trackingId: string;
  carrier?: {
    name?: string;
    slug?: string;
  };
  origin?: {
    country?: string;
  };
  destination?: {
    country?: string;
  };
  states?: ParcelsState[];
  lastState?: ParcelsState;
  attributes?: string[];
  status?: string; // Overall shipment status e.g. "delivered", "in_transit"
}

interface ParcelsState {
  date?: string;
  status?: string;
  location?: string;
}

interface CacheEntry {
  status: TrackingStatus;
  timestamp: number;
  uuid?: string;  // Store UUID for reuse without consuming credits
}

export class ParcelsAppService {
  private baseUrl = 'https://parcelsapp.com/api/v3';
  private pollInterval = 2000; // 2 seconds
  private maxPollAttempts = 30; // Max 1 minute of polling
  private cache: Map<string, CacheEntry> = new Map();
  private uuidMap: Map<string, string> = new Map(); // Store tracking number -> UUID mapping
  private cacheTTL = 30 * 60 * 1000; // 30 minutes cache TTL
  private apiRequestCount = 0; // Only counts NEW tracking requests (1 credit each)
  private apiRequestCountStart = Date.now();

  constructor(protected fastify: FastifyInstance, private apiKey: string) {
    // Clean up cache every 60 minutes
    setInterval(() => this.cleanupCache(), 60 * 60 * 1000);
    
    // Log API usage every hour
    setInterval(() => this.logApiUsage(), 60 * 60 * 1000);
  }

  /**
   * Clean up expired cache entries
   */
  private cleanupCache(): void {
    const now = Date.now();
    let cleaned = 0;
    let uuidsCleaned = 0;
    
    // Clean cache entries
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > this.cacheTTL) {
        this.cache.delete(key);
        cleaned++;
      }
    }
    
    // Clean UUID mappings older than 24 hours (UUIDs are valid for longer)
    const uuidTTL = 24 * 60 * 60 * 1000; // 24 hours
    for (const [trackingNumber, uuid] of this.uuidMap.entries()) {
      // Check if we have a cache entry for this tracking number
      const cacheKey = Array.from(this.cache.keys()).find(k => k.startsWith(trackingNumber));
      if (!cacheKey || !this.cache.has(cacheKey)) {
        this.uuidMap.delete(trackingNumber);
        uuidsCleaned++;
      }
    }
    
    if (cleaned > 0 || uuidsCleaned > 0) {
      this.fastify.log.info(`Cleaned up ${cleaned} cache entries and ${uuidsCleaned} UUID mappings`);
    }
  }

  /**
   * Log API usage statistics
   */
  private logApiUsage(): void {
    const duration = Date.now() - this.apiRequestCountStart;
    const hours = duration / (1000 * 60 * 60);
    const rate = this.apiRequestCount / hours;
    
    this.fastify.log.info(`Parcels API Usage: ${this.apiRequestCount} credits used for ${this.uuidMap.size} unique tracking numbers`);
    this.fastify.log.info(`Rate: ${rate.toFixed(2)} credits/hr | Avg: ${this.uuidMap.size > 0 ? (this.apiRequestCount / this.uuidMap.size).toFixed(2) : '0'} credits per tracking number`);
    
    // Cost calculation: $9 for 100 credits
    const cost = (this.apiRequestCount / 100) * 9;
    this.fastify.log.info(`Estimated cost: $${cost.toFixed(2)} | Cache size: ${this.cache.size} entries`);
  }

  /**
   * Map Parcels status strings to our numeric status codes
   */
  private mapParcelsStatusToCode(status?: string, attributes?: string[]): TrackingStatus['status'] {
    if (!status) return '0';
    
    const statusLower = status.toLowerCase();
    
    // Check attributes for delivered status
    if (attributes?.includes('Delivered')) return '40';
    
    // Map based on status text
    if (statusLower.includes('delivered')) return '40';
    if (statusLower.includes('out for delivery')) return '35';
    if (statusLower.includes('undelivered') || statusLower.includes('failed')) return '36';
    if (statusLower.includes('transit') || statusLower.includes('shipped') || 
        statusLower.includes('arrived') || statusLower.includes('departed') ||
        statusLower.includes('cleared customs') || statusLower.includes('processed')) return '20';
    if (statusLower.includes('pick up') || statusLower.includes('collected')) return '30';
    if (statusLower.includes('alert') || statusLower.includes('exception')) return '50';
    if (statusLower.includes('expired')) return '60';
    if (statusLower.includes('info received') || statusLower.includes('registered') ||
        statusLower.includes('information')) return '10';
    
    // Default to info received for any other status
    return '10';
  }

  /**
   * Map status code to text (for compatibility)
   */
  private mapCodeToStatusText(code: TrackingStatus['status']): TrackingStatus['statusText'] {
    const statusMap: Record<TrackingStatus['status'], TrackingStatus['statusText']> = {
      '0': 'Not Found',
      '10': 'Info Received',
      '20': 'In Transit',
      '30': 'Pick Up',
      '35': 'Out for Delivery',
      '36': 'Undelivered',
      '40': 'Delivered',
      '50': 'Alert',
      '60': 'Expired'
    };
    return statusMap[code] || 'Not Found';
  }

  /**
   * Create or retrieve tracking request with Parcels API
   * Returns either a UUID string or the full response if results are cached
   */
  private async createTrackingRequest(trackingNumber: string, destinationCountry: string): Promise<string | ParcelsTrackingResponse | null> {
    try {
      // Check if we already have a UUID for this tracking number
      const existingUuid = this.uuidMap.get(trackingNumber);
      if (existingUuid) {
        this.fastify.log.info(`Reusing existing UUID ${existingUuid} for ${trackingNumber} (no credit consumed)`);
        return existingUuid;
      }

      const requestBody: ParcelsTrackingRequest = {
        apiKey: this.apiKey,
        shipments: [{
          trackingId: trackingNumber,
          destinationCountry: destinationCountry,
          language: 'en'
        }],
        language: 'en'
      };

      const response = await fetch(`${this.baseUrl}/shipments/tracking`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      // Only increment counter for new tracking requests (not reused UUIDs)
      this.apiRequestCount++;

      if (!response.ok) {
        const errorText = await response.text();
        this.fastify.log.error(`Failed to create tracking request: ${response.status} ${response.statusText} - ${errorText}`);
        return null;
      }

      const data = await response.json() as ParcelsTrackingResponse;
      
      // Store UUID for reuse (whether new or existing)
      if (data.uuid) {
        this.uuidMap.set(trackingNumber, data.uuid);
        this.fastify.log.info(`Stored UUID ${data.uuid} for tracking number ${trackingNumber}`);
      }
      
      // If we get immediate results (fromCache), return them
      if (data.done && data.shipments) {
        this.fastify.log.info('Got immediate cached results from Parcels API');
        return data;
      }
      
      return data.uuid || null;
    } catch (error) {
      this.fastify.log.error(`Error creating tracking request: ${error}`);
      return null;
    }
  }

  /**
   * Poll for tracking results
   */
  private async pollTrackingResults(uuid: string): Promise<ParcelsTrackingResponse | null> {
    let attempts = 0;
    
    while (attempts < this.maxPollAttempts) {
      try {
        const response = await fetch(
          `${this.baseUrl}/shipments/tracking?uuid=${uuid}&apiKey=${this.apiKey}`,
          {
            method: 'GET',
            headers: {
              'Accept': 'application/json'
            }
          }
        );

        if (!response.ok) {
          this.fastify.log.error(`Failed to poll tracking results: ${response.status} ${response.statusText}`);
          return null;
        }

        const data = await response.json() as ParcelsTrackingResponse;
        
        // If tracking is complete, return the results
        if (data.done) {
          return data;
        }
        
        // If we have partial results from cache, we can return them
        if (data.fromCache && data.shipments && data.shipments.length > 0) {
          this.fastify.log.info('Returning cached tracking results');
          return data;
        }
        
        // Wait before next poll
        await new Promise(resolve => setTimeout(resolve, this.pollInterval));
        attempts++;
      } catch (error) {
        this.fastify.log.error(`Error polling tracking results: ${error}`);
        return null;
      }
    }
    
    this.fastify.log.warn(`Max polling attempts reached for UUID: ${uuid}`);
    return null;
  }

  /**
   * Get tracking status for a tracking number
   * Uses Parcels API's 2-phase tracking system
   */
  async getTrackingStatus(trackingNumber: string, destinationCountry: string = 'United States'): Promise<TrackingStatus | null> {
    if (!this.apiKey) {
      this.fastify.log.warn('Parcels API key not configured, returning default status');
      return {
        trackingNumber,
        status: '10',
        statusText: 'Info Received',
        lastUpdate: new Date().toISOString()
      };
    }

    // Check cache first
    const cacheKey = `${trackingNumber}:${destinationCountry}`;
    const cached = this.cache.get(cacheKey);
    if (cached && Date.now() - cached.timestamp < this.cacheTTL) {
      // Don't return cached results for delivered packages - they're handled upstream
      if (cached.status.statusText !== 'Delivered') {
        // Restore UUID mapping if available
        if (cached.uuid && !this.uuidMap.has(trackingNumber)) {
          this.uuidMap.set(trackingNumber, cached.uuid);
        }
        this.fastify.log.info(`Returning cached tracking status for ${trackingNumber}`);
        return cached.status;
      }
    }

    try {
      // Phase 1: Create tracking request
      const response = await this.createTrackingRequest(trackingNumber, destinationCountry);
      if (!response) {
        this.fastify.log.error('Failed to create tracking request');
        return null;
      }

      let results: ParcelsTrackingResponse | null;
      
      // Check if we got immediate results or need to poll
      if (typeof response === 'object' && 'shipments' in response) {
        // We got immediate cached results
        results = response;
      } else {
        // We got a UUID, need to poll
        const uuid = response as string;
        this.fastify.log.info(`Created tracking request with UUID: ${uuid}`);
        results = await this.pollTrackingResults(uuid);
      }
      if (!results || !results.shipments || results.shipments.length === 0) {
        this.fastify.log.warn(`No tracking results found for ${trackingNumber}`);
        return {
          trackingNumber,
          status: '0',
          statusText: 'Not Found',
          lastUpdate: new Date().toISOString(),
          lastEventDescription: undefined
        };
      }

      const shipment = results.shipments[0];
      const lastState = shipment.lastState;
      
      // Map Parcels status to our status code
      // Check both shipment.status (overall status) and lastState.status (last event)
      const statusCode = this.mapParcelsStatusToCode(
        shipment.status || lastState?.status, 
        shipment.attributes
      );
      const statusText = this.mapCodeToStatusText(statusCode);
      const lastUpdate = lastState?.date || new Date().toISOString();

      const result: TrackingStatus = {
        trackingNumber,
        status: statusCode,
        statusText,
        lastUpdate,
        lastEventDescription: lastState?.status
      };

      // Cache the result (except for delivered packages)
      if (statusText !== 'Delivered') {
        const uuid = this.uuidMap.get(trackingNumber);
        this.cache.set(cacheKey, {
          status: result,
          timestamp: Date.now(),
          uuid: uuid
        });
        this.fastify.log.info(`Cached tracking status for ${trackingNumber}${uuid ? ' with UUID' : ''}`);
      }

      return result;
    } catch (error) {
      this.fastify.log.error(`Error getting tracking status: ${error}`);
      
      // Return a default status on error
      return {
        trackingNumber,
        status: '10',
        statusText: 'Info Received',
        lastUpdate: new Date().toISOString(),
        lastEventDescription: undefined
      };
    }
  }

  /**
   * Generate tracking URL for parcelsapp.com
   */
  generateTrackingUrl(trackingNumber: string): string {
    return `https://parcelsapp.com/tracking/${encodeURIComponent(trackingNumber)}`;
  }

  /**
   * Register tracking number - not needed for Parcels API
   * Kept for compatibility with existing interface
   */
  async registerTrackingNumber(trackingNumber: string): Promise<void> {
    // Parcels API doesn't require registration
    this.fastify.log.info(`Tracking number ${trackingNumber} ready for tracking (no registration needed with Parcels API)`);
  }

  /**
   * Get current API usage statistics
   */
  getApiUsageStats(): { requests: number; estimatedCost: number; cacheSize: number; uuidCount: number; averageCreditsPerTracking: number } {
    const cost = (this.apiRequestCount / 100) * 9;
    const avgCredits = this.uuidMap.size > 0 ? this.apiRequestCount / this.uuidMap.size : 0;
    return {
      requests: this.apiRequestCount,
      estimatedCost: cost,
      cacheSize: this.cache.size,
      uuidCount: this.uuidMap.size,
      averageCreditsPerTracking: avgCredits
    };
  }
}