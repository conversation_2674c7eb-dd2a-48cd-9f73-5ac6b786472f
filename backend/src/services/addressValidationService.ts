import axios from 'axios';
import { FastifyInstance } from 'fastify';

interface AddressComponent {
  componentType: string;
  componentName: {
    text: string;
    languageCode?: string;
  };
  confirmationLevel?: 'CONFIRMED' | 'UNCONFIRMED_BUT_PLAUSIBLE' | 'UNCONFIRMED_AND_SUSPICIOUS';
  inferred?: boolean;
  spellCorrected?: boolean;
  replaced?: boolean;
  unexpected?: boolean;
}

interface Address {
  regionCode: string;
  languageCode?: string;
  postalAddress: {
    revision?: number;
    regionCode?: string;
    languageCode?: string;
    postalCode?: string;
    administrativeArea?: string;
    locality?: string;
    addressLines?: string[];
  };
  addressComponents?: AddressComponent[];
}

interface ValidationResult {
  result: {
    verdict: {
      inputGranularity: 'GRANULARITY_UNSPECIFIED' | 'GRANULARITY_SUB_PREMISE' | 'GRANULARITY_PREMISE' | 'GRANULARITY_BLOCK' | 'GRANULARITY_ROUTE' | 'G<PERSON><PERSON>LARITY_OTHER';
      validationGranularity: 'GRANULARITY_UNSPECIFIED' | 'GRANULARITY_SUB_PREMISE' | 'GRANULARITY_PREMISE' | 'GRANULARITY_BLOCK' | 'GRANULARITY_ROUTE' | 'GRANULARITY_OTHER';
      geocodeGranularity?: 'GRANULARITY_UNSPECIFIED' | 'GRANULARITY_SUB_PREMISE' | 'GRANULARITY_PREMISE' | 'GRANULARITY_PREMISE_PROXIMITY' | 'GRANULARITY_BLOCK' | 'GRANULARITY_ROUTE' | 'GRANULARITY_OTHER';
      addressComplete?: boolean;
      hasUnconfirmedComponents?: boolean;
      hasInferredComponents?: boolean;
      hasReplacedComponents?: boolean;
    };
    address: Address;
    geocode?: {
      location?: {
        latitude: number;
        longitude: number;
      };
      plusCode?: {
        globalCode: string;
        compoundCode?: string;
      };
      bounds?: {
        low: { latitude: number; longitude: number };
        high: { latitude: number; longitude: number };
      };
      featureSizeMeters?: number;
      placeId?: string;
      placeTypes?: string[];
    };
    metadata?: {
      business?: boolean;
      poBox?: boolean;
      residential?: boolean;
    };
    uspsData?: {
      standardizedAddress?: {
        firstAddressLine?: string;
        secondAddressLine?: string;
        cityStateZipAddressLine?: string;
        city?: string;
        state?: string;
        zipCode?: string;
        zipCodeExtension?: string;
      };
      deliveryPointCode?: string;
      deliveryPointCheckDigit?: string;
      dpvConfirmation?: string;
      dpvFootnote?: string;
      dpvCmra?: string;
      dpvVacant?: string;
      dpvNoStat?: string;
      carrierRoute?: string;
      carrierRouteIndicator?: string;
      postOfficeCity?: string;
      postOfficeState?: string;
    };
  };
  responseId: string;
}

interface AddressValidationRequest {
  street1: string;
  street2?: string;
  city: string;
  state?: string;
  postalCode: string;
  country: string;
}

export class AddressValidationService {
  private apiKey: string;
  private baseUrl = 'https://addressvalidation.googleapis.com/v1:validateAddress';
  private cache: Map<string, { result: any; timestamp: number }> = new Map();
  private CACHE_TTL = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

  constructor(private fastify: FastifyInstance) {
    this.apiKey = fastify.config.GOOGLE_API_KEY as string;
    
    // Clean up expired cache entries every hour
    setInterval(() => {
      this.cleanupCache();
    }, 60 * 60 * 1000);
  }

  /**
   * Convert country name to ISO 3166-1 alpha-2 code
   */
  private getRegionCode(countryName: string): string {
    const countryMap: Record<string, string> = {
      'United States': 'US',
      'Canada': 'CA',
      'United Kingdom': 'GB',
      'France': 'FR',
      'Germany': 'DE',
      'Italy': 'IT',
      'Spain': 'ES',
      'Netherlands': 'NL',
      'Belgium': 'BE',
      'Switzerland': 'CH',
      'Austria': 'AT',
      'Sweden': 'SE',
      'Norway': 'NO',
      'Denmark': 'DK',
      'Finland': 'FI',
      'Poland': 'PL',
      'Czech Republic': 'CZ',
      'Hungary': 'HU',
      'Romania': 'RO',
      'Bulgaria': 'BG',
      'Greece': 'GR',
      'Portugal': 'PT',
      'Ireland': 'IE',
      'Australia': 'AU',
      'New Zealand': 'NZ',
      'Japan': 'JP',
      'China': 'CN',
      'South Korea': 'KR',
      'India': 'IN',
      'Singapore': 'SG',
      'Malaysia': 'MY',
      'Thailand': 'TH',
      'Philippines': 'PH',
      'Indonesia': 'ID',
      'Vietnam': 'VN',
      'Brazil': 'BR',
      'Argentina': 'AR',
      'Chile': 'CL',
      'Colombia': 'CO',
      'Peru': 'PE',
      'Venezuela': 'VE',
      'South Africa': 'ZA',
      'Egypt': 'EG',
      'Nigeria': 'NG',
      'Kenya': 'KE',
      'Morocco': 'MA',
      'Israel': 'IL',
      'United Arab Emirates': 'AE',
      'Saudi Arabia': 'SA',
      'Turkey': 'TR',
      'Russia': 'RU',
      'Ukraine': 'UA',
      'Mexico': 'MX',
      'Lithuania': 'LT'
    };

    return countryMap[countryName] || countryName;
  }

  /**
   * Validate an address using Google Address Validation API
   */
  async validateAddress(addressRequest: AddressValidationRequest): Promise<{
    isValid: boolean;
    confidence: 'HIGH' | 'MEDIUM' | 'LOW';
    standardizedAddress?: AddressValidationRequest;
    issues?: string[];
    metadata?: {
      residential?: boolean;
      business?: boolean;
      poBox?: boolean;
    };
  }> {
    try {
      const regionCode = this.getRegionCode(addressRequest.country);
      
      // Build address lines
      const addressLines: string[] = [];
      if (addressRequest.street1) {
        addressLines.push(addressRequest.street1);
      }
      if (addressRequest.street2) {
        addressLines.push(addressRequest.street2);
      }

      // Prepare request payload
      const payload = {
        address: {
          regionCode,
          administrativeArea: addressRequest.state,
          locality: addressRequest.city,
          postalCode: addressRequest.postalCode,
          addressLines
        }
      };

      // Make API request
      const response = await axios.post<ValidationResult>(
        this.baseUrl,
        payload,
        {
          params: { key: this.apiKey },
          headers: { 'Content-Type': 'application/json' }
        }
      );

      const result = response.data.result;
      const verdict = result.verdict;
      
      // Determine validation status and confidence
      let isValid = true;
      let confidence: 'HIGH' | 'MEDIUM' | 'LOW' = 'HIGH';
      const issues: string[] = [];

      // Check for address completeness
      if (!verdict.addressComplete) {
        isValid = false;
        issues.push('Address appears to be incomplete');
      }

      // Check for unconfirmed components
      if (verdict.hasUnconfirmedComponents) {
        confidence = 'MEDIUM';
        issues.push('Some address components could not be confirmed');
      }

      // Check for inferred components
      if (verdict.hasInferredComponents) {
        if (confidence === 'HIGH') confidence = 'MEDIUM';
        issues.push('Some address components were inferred');
      }

      // Check validation granularity
      if (verdict.validationGranularity === 'GRANULARITY_ROUTE' || 
          verdict.validationGranularity === 'GRANULARITY_OTHER') {
        confidence = 'LOW';
        issues.push('Could not validate to a specific address');
      }

      // For US addresses, check USPS data
      if (regionCode === 'US' && result.uspsData) {
        const dpvConfirmation = result.uspsData.dpvConfirmation;
        if (dpvConfirmation === 'N' || dpvConfirmation === 'D') {
          isValid = false;
          issues.push('Address not deliverable according to USPS');
        }
        if (result.uspsData.dpvVacant === 'Y') {
          issues.push('Address is vacant');
        }
      }

      // Build standardized address from the validated result
      let standardizedAddress: AddressValidationRequest | undefined;
      if (result.address.postalAddress) {
        const postalAddress = result.address.postalAddress;
        standardizedAddress = {
          street1: postalAddress.addressLines?.[0] || addressRequest.street1,
          street2: postalAddress.addressLines?.[1] || addressRequest.street2,
          city: postalAddress.locality || addressRequest.city,
          state: postalAddress.administrativeArea || addressRequest.state,
          postalCode: postalAddress.postalCode || addressRequest.postalCode,
          country: addressRequest.country
        };
      }

      // Extract metadata
      const metadata = result.metadata ? {
        residential: result.metadata.residential,
        business: result.metadata.business,
        poBox: result.metadata.poBox
      } : undefined;

      return {
        isValid,
        confidence,
        standardizedAddress,
        issues: issues.length > 0 ? issues : undefined,
        metadata
      };

    } catch (error: any) {
      this.fastify.log.error({ 
        error: error.message,
        status: error.response?.status,
        data: error.response?.data,
        addressRequest
      }, 'Address validation failed');
      
      // Handle specific API errors
      if (error.response?.status === 400) {
        return {
          isValid: false,
          confidence: 'LOW',
          issues: ['Invalid address format or unsupported region']
        };
      }
      
      // Re-throw the original error to preserve all details
      throw error;
    }
  }

  /**
   * Get cached validation result
   */
  async getCachedValidation(addressHash: string): Promise<any | null> {
    const cached = this.cache.get(addressHash);
    
    if (!cached) {
      return null;
    }
    
    // Check if cache entry is expired
    const now = Date.now();
    if (now - cached.timestamp > this.CACHE_TTL) {
      this.cache.delete(addressHash);
      return null;
    }
    
    this.fastify.log.info({ addressHash, age: now - cached.timestamp }, 'Cache hit for address validation');
    return cached.result;
  }

  /**
   * Cache validation result
   */
  async cacheValidation(addressHash: string, result: any): Promise<void> {
    this.cache.set(addressHash, {
      result,
      timestamp: Date.now()
    });
    
    this.fastify.log.info({ addressHash, cacheSize: this.cache.size }, 'Cached address validation result');
  }

  /**
   * Clean up expired cache entries
   */
  private cleanupCache(): void {
    const now = Date.now();
    let cleaned = 0;
    
    for (const [hash, entry] of this.cache.entries()) {
      if (now - entry.timestamp > this.CACHE_TTL) {
        this.cache.delete(hash);
        cleaned++;
      }
    }
    
    if (cleaned > 0) {
      this.fastify.log.info({ cleaned, remaining: this.cache.size }, 'Cleaned up expired cache entries');
    }
  }

  /**
   * Generate hash for address caching
   */
  generateAddressHash(address: AddressValidationRequest): string {
    const normalized = `${address.street1}|${address.street2 || ''}|${address.city}|${address.state || ''}|${address.postalCode}|${address.country}`.toLowerCase();
    
    // Simple hash function for demo - in production use crypto.createHash
    let hash = 0;
    for (let i = 0; i < normalized.length; i++) {
      const char = normalized.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(16);
  }
}