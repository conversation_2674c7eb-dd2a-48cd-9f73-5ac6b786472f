import { cleanEnv, str, port, url, email } from 'envalid';
import dotenvFlow from 'dotenv-flow';

// Load environment-specific .env files
dotenvFlow.config({
  default_node_env: 'development'
});

// Validate environment variables
const env = cleanEnv(process.env, {
  NODE_ENV: str({ choices: ['development', 'production'] }),
  PORT: port({ default: 3000 }),
  DATABASE_URL: str({ desc: 'PostgreSQL connection string' }),
  GMAIL_USER: email(),
  GOOGLE_APP_PASSWORD: str(),
  GOOGLE_API_KEY: str(),
  SEVENTEEN_TRACK_API_KEY: str({ default: '' }),
  PARCELS_APP_API_KEY: str(),
  COINGECKO_URL: url({ default: 'https://api.coingecko.com/api/v3' })
});

// Enhanced database safety checks
function validateDatabaseSafety() {
  const dbUrl = env.DATABASE_URL;

  if (dbUrl.includes('_prod') && env.isDev) {
    console.error('🚨 CRITICAL ERROR: Attempting to access production database in development mode!');
    console.error('Please check your environment configuration.');
    process.exit(1);
  }

  if (env.isDev && !dbUrl.includes('_dev')) {
    console.error('🚨 CRITICAL ERROR: Development mode requires _dev database!');
    console.error('Current DATABASE_URL:', dbUrl.split('@')[1] || 'invalid');
    process.exit(1);
  }

  if (env.isProduction && !dbUrl.includes('_prod')) {
    console.error('🚨 CRITICAL ERROR: Production mode requires _prod database!');
    console.error('Current DATABASE_URL:', dbUrl.split('@')[1] || 'invalid');
    process.exit(1);
  }
}

// Run safety checks
validateDatabaseSafety();

export const config = {
  env: env.NODE_ENV,
  isDevelopment: env.isDev,
  isProduction: env.isProduction,
  port: env.PORT,
  database: {
    url: env.DATABASE_URL
  },
  email: {
    user: env.GMAIL_USER,
    password: env.GOOGLE_APP_PASSWORD,
  },
  apis: {
    googleApiKey: env.GOOGLE_API_KEY,
    seventeenTrackKey: env.SEVENTEEN_TRACK_API_KEY,
    parcelsAppKey: env.PARCELS_APP_API_KEY,
    coinGeckoUrl: env.COINGECKO_URL,
  }
};