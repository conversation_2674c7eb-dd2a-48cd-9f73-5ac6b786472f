import Fastify from 'fastify';
import cors from '@fastify/cors';
import { config } from './config.js';
import pricingRoute from './routes/pricing.route.js';
import productsRoute from './routes/products.route.js';
import ordersRoute from './routes/orders.route.js';
import exportsRoute from './routes/exports.route.js';
import placesRoute from './routes/places.route.js';
import addressRoute from './routes/address.route.js';
import dashboardRoute from './routes/dashboard.route.js';

const buildApp = async () => {
  const fastify = Fastify({
    logger: {
      level: 'info',
      transport: {
        target: 'pino-pretty',
        options: {
          translateTime: 'HH:MM:ss Z',
          ignore: 'pid,hostname'
        }
      }
    },
    // Add custom serializer to handle BigInt
    ajv: {
      customOptions: {
        coerceTypes: 'array'
      }
    }
  });

  // Add BigInt serialization support
  fastify.addHook('preSerialization', async (request, reply, payload) => {
    return JSON.parse(JSON.stringify(payload, (key, value) =>
      typeof value === 'bigint' ? value.toString() : value
    ));
  });

  // Add config to fastify instance
  fastify.decorate('config', {
    PORT: Number(config.port),
    DATABASE_URL: process.env.DATABASE_URL!,
    COINGECKO_URL: config.apis.coinGeckoUrl,
    GMAIL_USER: config.email.user,
    GOOGLE_APP_PASSWORD: config.email.password,
    GOOGLE_API_KEY: config.apis.googleApiKey,
    SEVENTEEN_TRACK_API_KEY: config.apis.seventeenTrackKey,
    PARCELS_APP_API_KEY: config.apis.parcelsAppKey
  });

  // Environment already loaded via config.ts
  await fastify.register(cors, {
    origin: (origin, cb) => {
      // Allow requests from localhost and local network IPs
      const allowedPatterns = [
        /^http:\/\/localhost:\d+$/,
        /^http:\/\/127\.0\.0\.1:\d+$/,
        /^http:\/\/10\.\d+\.\d+\.\d+:\d+$/,
        /^http:\/\/192\.168\.\d+\.\d+:\d+$/,
        /^http:\/\/172\.(1[6-9]|2\d|3[0-1])\.\d+\.\d+:\d+$/
      ];
      
      if (!origin || allowedPatterns.some(pattern => pattern.test(origin))) {
        cb(null, true);
      } else {
        cb(new Error('Not allowed by CORS'), false);
      }
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS']
  });

  fastify.get('/health', async () => {
    return { status: 'ok', timestamp: new Date().toISOString() };
  });

  // Register routes
  await fastify.register(pricingRoute, { prefix: '/api' });
  await fastify.register(productsRoute, { prefix: '/api' });
  await fastify.register(ordersRoute, { prefix: '/api' });
  await fastify.register(exportsRoute, { prefix: '/api' });
  await fastify.register(placesRoute, { prefix: '/api' });
  await fastify.register(addressRoute, { prefix: '/api' });
  await fastify.register(dashboardRoute, { prefix: '/api/dashboard' });

  return fastify;
};

const start = async () => {
  try {
    const app = await buildApp();
    await app.listen({ port: config.port, host: '0.0.0.0' });
    app.log.info(`Server listening on http://localhost:${config.port} [${config.env}]`);
  } catch (err) {
    console.error(err);
    process.exit(1);
  }
};

start();