import { FastifyPluginAsync } from 'fastify';
import { PrismaClient } from '../generated/prisma/index.js';
import { PDFService } from '../services/pdf.service.js';
import { ExcelService } from '../services/excel.service.js';
import { emailService } from '../services/email.service.js';
import path from 'path';
import { fileURLToPath } from 'url';
import { promises as fs } from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const prisma = new PrismaClient();
const pdfService = new PDFService();
const excelService = new ExcelService();

const exportsRoute: FastifyPluginAsync = async (fastify) => {
  // GET /exports/:orderId/pdf - Generate and download PDF
  fastify.get<{
    Params: { orderId: string };
  }>('/exports/:orderId/pdf', async (request, reply) => {
    try {
      const order = await prisma.order.findUnique({
        where: { id: request.params.orderId },
        include: { items: true }
      });

      if (!order) {
        return reply.status(404).send({ error: 'Order not found' });
      }

      // Generate filename
      const filename = `order-${order.id.substring(0, 8)}.pdf`;
      const outputPath = path.join(__dirname, '../../exports', filename);

      // Generate PDF
      await pdfService.generateOrderPDF(order, outputPath);

      // Set headers for download
      reply.header('Content-Type', 'application/pdf');
      reply.header('Content-Disposition', `attachment; filename="${filename}"`);

      // Stream the file
      const stream = await fs.readFile(outputPath);
      return reply.send(stream);
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({ 
        error: 'Failed to generate PDF' 
      });
    }
  });

  // GET /exports/:orderId/excel - Generate and download Excel
  fastify.get<{
    Params: { orderId: string };
  }>('/exports/:orderId/excel', async (request, reply) => {
    try {
      const order = await prisma.order.findUnique({
        where: { id: request.params.orderId },
        include: { items: true }
      });

      if (!order) {
        return reply.status(404).send({ error: 'Order not found' });
      }

      // Generate filename
      const filename = `order-${order.id.substring(0, 8)}.xlsx`;
      const outputPath = path.join(__dirname, '../../exports', filename);

      // Generate Excel
      await excelService.generateOrderExcel(order, outputPath);

      // Set headers for download
      reply.header('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      reply.header('Content-Disposition', `attachment; filename="${filename}"`);

      // Stream the file
      const stream = await fs.readFile(outputPath);
      return reply.send(stream);
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({ 
        error: 'Failed to generate Excel file' 
      });
    }
  });

  // POST /exports/bulk/pdf - Generate bulk PDF
  fastify.post<{
    Body: { orderIds: string[] };
  }>('/exports/bulk/pdf', async (request, reply) => {
    try {
      const { orderIds } = request.body;

      if (!orderIds || orderIds.length === 0) {
        return reply.status(400).send({ error: 'No order IDs provided' });
      }

      if (orderIds.length > 50) {
        return reply.status(400).send({ error: 'Maximum 50 orders allowed per bulk export' });
      }

      // Fetch all orders
      const orders = await prisma.order.findMany({
        where: { id: { in: orderIds } },
        include: { items: true },
        orderBy: { placedAt: 'desc' }
      });

      if (orders.length === 0) {
        return reply.status(404).send({ error: 'No orders found' });
      }

      // Generate filename
      const filename = `bulk-orders-${Date.now()}.pdf`;
      const outputPath = path.join(__dirname, '../../exports', filename);

      // Generate bulk PDF
      await pdfService.generateBulkOrdersPDF(orders, outputPath);

      // Set headers for download
      reply.header('Content-Type', 'application/pdf');
      reply.header('Content-Disposition', `attachment; filename="${filename}"`);

      // Stream the file
      const stream = await fs.readFile(outputPath);

      // Clean up file after sending
      setTimeout(() => {
        fs.unlink(outputPath).catch(err => 
          fastify.log.error('Failed to cleanup PDF file:', err)
        );
      }, 5000);

      return reply.send(stream);
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({ 
        error: 'Failed to generate bulk PDF' 
      });
    }
  });

  // POST /exports/bulk/excel - Generate bulk Excel
  fastify.post<{
    Body: { orderIds: string[] };
  }>('/exports/bulk/excel', async (request, reply) => {
    try {
      const { orderIds } = request.body;

      if (!orderIds || orderIds.length === 0) {
        return reply.status(400).send({ error: 'No order IDs provided' });
      }

      if (orderIds.length > 50) {
        return reply.status(400).send({ error: 'Maximum 50 orders allowed per bulk export' });
      }

      // Fetch all orders
      const orders = await prisma.order.findMany({
        where: { id: { in: orderIds } },
        include: { items: true },
        orderBy: { placedAt: 'desc' }
      });

      if (orders.length === 0) {
        return reply.status(404).send({ error: 'No orders found' });
      }

      // Generate filename
      const filename = `bulk-orders-${Date.now()}.xlsx`;
      const outputPath = path.join(__dirname, '../../exports', filename);

      // Generate bulk Excel
      await excelService.generateBulkOrdersExcel(orders, outputPath);

      // Set headers for download
      reply.header('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      reply.header('Content-Disposition', `attachment; filename="${filename}"`);

      // Stream the file
      const stream = await fs.readFile(outputPath);

      // Clean up file after sending
      setTimeout(() => {
        fs.unlink(outputPath).catch(err => 
          fastify.log.error('Failed to cleanup Excel file:', err)
        );
      }, 5000);

      return reply.send(stream);
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({ 
        error: 'Failed to generate bulk Excel file' 
      });
    }
  });

  // POST /exports/bulk/send - Send bulk orders to supplier
  fastify.post<{
    Body: { orderIds: string[] };
  }>('/exports/bulk/send', async (request, reply) => {
    try {
      const { orderIds } = request.body;

      if (!orderIds || orderIds.length === 0) {
        return reply.status(400).send({ error: 'No order IDs provided' });
      }

      if (orderIds.length > 50) {
        return reply.status(400).send({ error: 'Maximum 50 orders allowed per bulk export' });
      }

      // Fetch all orders
      const orders = await prisma.order.findMany({
        where: { id: { in: orderIds } },
        include: { items: true },
        orderBy: { placedAt: 'desc' }
      });

      if (orders.length === 0) {
        return reply.status(404).send({ error: 'No orders found' });
      }

      // Generate files
      const pdfFilename = `bulk-orders-${Date.now()}.pdf`;
      const excelFilename = `bulk-orders-${Date.now()}.xlsx`;
      const pdfPath = path.join(__dirname, '../../exports', pdfFilename);
      const excelPath = path.join(__dirname, '../../exports', excelFilename);

      await pdfService.generateBulkOrdersPDF(orders, pdfPath);
      await excelService.generateBulkOrdersExcel(orders, excelPath);

      // Send email
      await emailService.sendBulkOrdersToSupplier(orders, pdfPath, excelPath);

      // Update all orders as sent
      await prisma.order.updateMany({
        where: { id: { in: orderIds } },
        data: { sentToSupplier: true }
      });

      // Fetch updated orders
      const updatedOrders = await prisma.order.findMany({
        where: { id: { in: orderIds } },
        include: { items: true }
      });

      // Clean up files
      setTimeout(() => {
        fs.unlink(pdfPath).catch(err => 
          fastify.log.error('Failed to cleanup PDF file:', err)
        );
        fs.unlink(excelPath).catch(err => 
          fastify.log.error('Failed to cleanup Excel file:', err)
        );
      }, 5000);

      return reply.send({
        success: true,
        orders: updatedOrders,
        message: `Successfully sent ${orders.length} orders to supplier`
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({ 
        error: 'Failed to send bulk orders to supplier' 
      });
    }
  });
};

export default exportsRoute;