import { FastifyPluginAsync } from 'fastify';
import axios from 'axios';

interface PlacesEnv {
  GOOGLE_API_KEY: string;
}

interface AutocompleteQuery {
  input: string;
}

const placesRoute: FastifyPluginAsync = async (fastify) => {
  const { GOOGLE_API_KEY } = fastify.config as PlacesEnv;

  fastify.get<{ Querystring: AutocompleteQuery }>('/places/autocomplete', async (request, reply) => {
    const { input } = request.query;

    if (!input || input.length < 3) {
      return reply.status(400).send({ error: 'Input must be at least 3 characters' });
    }

    try {
      const response = await axios.get('https://maps.googleapis.com/maps/api/place/autocomplete/json', {
        params: {
          input,
          key: GOOGLE_API_KEY,
          types: 'address',
          language: 'en'
        }
      });

      const predictions = response.data.predictions || [];
      
      // Simplify the response for <PERSON>
      const simplifiedPredictions = predictions.slice(0, 5).map((prediction: any) => ({
        placeId: prediction.place_id,
        description: prediction.description,
        mainText: prediction.structured_formatting?.main_text || '',
        secondaryText: prediction.structured_formatting?.secondary_text || ''
      }));

      return { predictions: simplifiedPredictions };
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({ error: 'Failed to fetch address suggestions' });
    }
  });

  // Get place details to extract address components
  fastify.get<{ Querystring: { placeId: string } }>('/places/details', async (request, reply) => {
    const { placeId } = request.query;
    
    if (!placeId) {
      return reply.status(400).send({ error: 'placeId is required' });
    }

    try {
      const response = await axios.get('https://maps.googleapis.com/maps/api/place/details/json', {
        params: {
          place_id: placeId,
          key: GOOGLE_API_KEY,
          fields: 'address_components,formatted_address'
        }
      });

      const { result } = response.data;
      if (!result || !result.address_components) {
        return reply.status(404).send({ error: 'Address details not found' });
      }

      // Parse address components with enhanced international support
      const addressData: any = {
        street1: '',
        city: '',
        state: '',
        postalCode: '',
        country: ''
      };

      let streetNumber = '';
      let route = '';
      let countryCode = '';
      let countryName = '';
      let premise = '';
      let subpremise = '';
      
      // Store all administrative levels for fallback
      const adminLevels: { [key: string]: { long_name: string; short_name: string } } = {};

      result.address_components.forEach((component: any) => {
        const types = component.types;
        
        if (types.includes('street_number')) {
          streetNumber = component.long_name;
        } else if (types.includes('route')) {
          route = component.long_name;
        } else if (types.includes('premise')) {
          premise = component.long_name;
        } else if (types.includes('subpremise')) {
          subpremise = component.long_name;
        } else if (types.includes('locality')) {
          addressData.city = component.long_name;
        } else if (types.includes('administrative_area_level_1')) {
          adminLevels['1'] = { long_name: component.long_name, short_name: component.short_name };
        } else if (types.includes('administrative_area_level_2')) {
          adminLevels['2'] = { long_name: component.long_name, short_name: component.short_name };
        } else if (types.includes('administrative_area_level_3')) {
          adminLevels['3'] = { long_name: component.long_name, short_name: component.short_name };
        } else if (types.includes('postal_code')) {
          addressData.postalCode = component.long_name;
        } else if (types.includes('country')) {
          countryCode = component.short_name;
          countryName = component.long_name;
          addressData.country = countryName;
        } else if (types.includes('postal_town') && !addressData.city) {
          // Fallback for UK addresses
          addressData.city = component.long_name;
        } else if (types.includes('sublocality_level_1') && !addressData.city) {
          // Fallback for some Asian addresses
          addressData.city = component.long_name;
        }
      });

      // Apply country-specific rules for state/region handling
      if (adminLevels['1']) {
        // Default: use administrative_area_level_1 for state
        addressData.state = adminLevels['1'].short_name || adminLevels['1'].long_name;
      }

      // Country-specific overrides
      switch (countryCode) {
        case 'GB': // United Kingdom
        case 'IE': // Ireland
          // These countries don't use states in addresses
          addressData.state = '';
          // Sometimes city is in admin_area_level_2
          if (!addressData.city && adminLevels['2']) {
            addressData.city = adminLevels['2'].long_name;
          }
          break;
          
        case 'JP': // Japan
          // Prefecture is in admin_area_level_1
          if (adminLevels['1']) {
            addressData.state = adminLevels['1'].long_name;
          }
          // City might be in admin_area_level_2
          if (!addressData.city && adminLevels['2']) {
            addressData.city = adminLevels['2'].long_name;
          }
          break;
          
        case 'FR': // France
        case 'IT': // Italy
        case 'ES': // Spain
          // These use regions/provinces but they're optional in addresses
          // Clear state as it's not commonly used in these countries
          addressData.state = '';
          // City might be in admin_area_level_2
          if (!addressData.city && adminLevels['2']) {
            addressData.city = adminLevels['2'].long_name;
          }
          break;
          
        case 'CN': // China
        case 'IN': // India
          // State/Province is important
          if (adminLevels['1']) {
            addressData.state = adminLevels['1'].long_name;
          }
          // City might be in admin_area_level_2
          if (!addressData.city && adminLevels['2']) {
            addressData.city = adminLevels['2'].long_name;
          }
          break;
      }

      // Construct street address with country-specific formatting
      if (countryCode === 'JP' || countryCode === 'KR') {
        // Asian format: route comes before street number
        if (route && streetNumber) {
          addressData.street1 = `${route} ${streetNumber}`;
        } else if (premise) {
          addressData.street1 = premise;
        } else if (route) {
          addressData.street1 = route;
        }
      } else {
        // Western format: street number before route
        if (streetNumber && route) {
          addressData.street1 = `${streetNumber} ${route}`;
        } else if (route) {
          addressData.street1 = route;
        } else if (premise) {
          addressData.street1 = premise;
        }
      }

      // Add apartment/unit if available
      if (subpremise) {
        addressData.street1 = addressData.street1 
          ? `${addressData.street1}, ${subpremise}`
          : subpremise;
      }

      // Log for debugging international addresses
      fastify.log.info({
        placeId,
        countryCode,
        adminLevels,
        parsedAddress: addressData,
        rawComponents: result.address_components.map((c: any) => ({
          types: c.types,
          long_name: c.long_name,
          short_name: c.short_name
        }))
      });

      return addressData;
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({ error: 'Failed to fetch address details' });
    }
  });
};

export default placesRoute;