import { FastifyPluginAsync } from 'fastify';
import { AddressValidationService } from '../services/addressValidationService';

interface ValidateAddressBody {
  street1: string;
  street2?: string;
  city: string;
  state?: string;
  postalCode: string;
  country: string;
}

const addressRoute: FastifyPluginAsync = async (fastify) => {
  const validationService = new AddressValidationService(fastify);

  // Rate limiting for this endpoint (using in-memory store for now)
  const rateLimitStore = new Map<string, { count: number; resetTime: number }>();
  const RATE_LIMIT = 10; // 10 requests per minute per IP
  const RATE_WINDOW = 60 * 1000; // 1 minute in milliseconds

  // Simple rate limiting middleware
  const checkRateLimit = (ip: string): boolean => {
    const now = Date.now();
    const record = rateLimitStore.get(ip);

    if (!record || now > record.resetTime) {
      rateLimitStore.set(ip, { count: 1, resetTime: now + RATE_WINDOW });
      return true;
    }

    if (record.count >= RATE_LIMIT) {
      return false;
    }

    record.count++;
    return true;
  };

  // Clean up old rate limit entries periodically
  setInterval(() => {
    const now = Date.now();
    for (const [ip, record] of rateLimitStore.entries()) {
      if (now > record.resetTime) {
        rateLimitStore.delete(ip);
      }
    }
  }, RATE_WINDOW);

  fastify.post<{ Body: ValidateAddressBody }>('/address/validate', async (request, reply) => {
    // Rate limiting check
    const clientIp = request.ip;
    if (!checkRateLimit(clientIp)) {
      return reply.status(429).send({
        error: 'Too many requests',
        message: 'Please wait before validating another address'
      });
    }

    const { street1, street2, city, state, postalCode, country } = request.body;

    // Basic validation
    if (!street1 || !city || !postalCode || !country) {
      return reply.status(400).send({
        error: 'Missing required fields',
        message: 'street1, city, postalCode, and country are required'
      });
    }

    try {
      // Check cache first
      const addressHash = validationService.generateAddressHash(request.body);
      const cachedResult = await validationService.getCachedValidation(addressHash);
      
      if (cachedResult) {
        fastify.log.info({ addressHash }, 'Returning cached validation result');
        return cachedResult;
      }

      // Perform validation
      const result = await validationService.validateAddress(request.body);

      // Cache the result for future use
      await validationService.cacheValidation(addressHash, result);

      // Log validation for monitoring
      fastify.log.info({
        country,
        isValid: result.isValid,
        confidence: result.confidence,
        issues: result.issues
      }, 'Address validation completed');

      return result;

    } catch (error: any) {
      fastify.log.error({
        error: error.message,
        status: error.response?.status,
        data: error.response?.data,
        stack: error.stack
      }, 'Address validation error');
      
      // Handle specific Google API errors
      if (error.response?.status === 403 && error.response?.data?.error?.details?.[0]?.reason === 'API_KEY_SERVICE_BLOCKED') {
        return reply.status(503).send({
          error: 'Address Validation API not enabled',
          message: 'The Google Address Validation API is not enabled for this project. Please contact your administrator.'
        });
      }
      
      // Don't expose internal errors to client
      return reply.status(500).send({
        error: 'Validation service error',
        message: 'Unable to validate address at this time'
      });
    }
  });

  // Get validation statistics (for monitoring)
  fastify.get('/address/validation-stats', async (request, reply) => {
    // TODO: Implement statistics tracking
    return {
      totalValidations: 0,
      successfulValidations: 0,
      failedValidations: 0,
      averageConfidence: 0,
      commonIssues: []
    };
  });
};

export default addressRoute;