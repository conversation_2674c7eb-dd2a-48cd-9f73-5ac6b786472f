import { FastifyPluginAsync } from 'fastify';
import { PrismaClient, Prisma } from '../generated/prisma/index.js';
import type { 
  Product, 
  ProductVariant
} from '../generated/prisma/index.js';

const prisma = new PrismaClient();

// Type definitions
interface ProductWithVariants extends Product {
  variants: ProductVariant[];
}

interface CreateProductRequest {
  name: string;
  variants: {
    dose: string;
    code: string;
    buyingPrice: number;
    sellingPrice: number;
  }[];
}

interface CreateVariantRequest {
  dose: string;
  code: string;
  buyingPrice: number;
  sellingPrice: number;
}

interface UpdateVariantRequest {
  dose?: string;
  buyingPrice?: number;
  sellingPrice?: number;
  sortOrder?: number;
}

interface ReorderVariantsRequest {
  variantIds: string[];
}

// Validation helpers
function validateVariant(data: Partial<CreateVariantRequest>): string[] {
  const errors: string[] = [];

  if (!data.dose || data.dose.trim() === '') {
    errors.push('Dose is required');
  }

  if (!data.code || data.code.trim() === '') {
    errors.push('Product code is required');
  } else if (data.code.length > 10) {
    errors.push('Product code must be 10 characters or less');
  }

  if (data.buyingPrice === undefined || data.buyingPrice === null || data.buyingPrice < 0) {
    errors.push('Valid buying price is required');
  }

  if (data.sellingPrice === undefined || data.sellingPrice === null || data.sellingPrice < 0) {
    errors.push('Valid selling price is required');
  }

  if (data.buyingPrice !== undefined && data.sellingPrice !== undefined) {
    if (data.sellingPrice <= data.buyingPrice) {
      errors.push('Selling price must be greater than buying price');
    }
  }

  return errors;
}

const productsRoute: FastifyPluginAsync = async (fastify) => {
  // Get all products with variants
  fastify.get('/products', async (request, reply) => {
    try {
      const products = await prisma.product.findMany({
        include: {
          variants: {
            orderBy: { sortOrder: 'asc' }
          }
        },
        orderBy: { name: 'asc' }
      });

      // Add calculated fields
      const productsWithStats = products.map(product => ({
        ...product,
        variants: product.variants.map(variant => ({
          ...variant,
          buyingPrice: Number(variant.buyingPrice),
          sellingPrice: Number(variant.sellingPrice),
          profitMargin: calculateProfitMargin(Number(variant.buyingPrice), Number(variant.sellingPrice))
        }))
      }));

      return reply.send({ products: productsWithStats });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({ error: 'Failed to fetch products' });
    }
  });

  // Get single product with variants
  fastify.get<{
    Params: { id: string };
  }>('/products/:id', async (request, reply) => {
    try {
      const product = await prisma.product.findUnique({
        where: { id: request.params.id },
        include: {
          variants: {
            orderBy: { sortOrder: 'asc' }
          }
        }
      });

      if (!product) {
        return reply.status(404).send({ error: 'Product not found' });
      }

      return reply.send({ product });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({ error: 'Failed to fetch product' });
    }
  });

  // Create new product with variants
  fastify.post<{
    Body: CreateProductRequest;
  }>('/products', async (request, reply) => {
    try {
      const { name, variants } = request.body;

      if (!name || name.trim() === '') {
        return reply.status(400).send({ error: 'Product name is required' });
      }

      if (!variants || variants.length === 0) {
        return reply.status(400).send({ error: 'At least one variant is required' });
      }

      // Validate all variants
      const variantErrors: { index: number; errors: string[] }[] = [];
      variants.forEach((variant, index) => {
        const errors = validateVariant(variant);
        if (errors.length > 0) {
          variantErrors.push({ index, errors });
        }
      });

      if (variantErrors.length > 0) {
        return reply.status(400).send({ 
          error: 'Validation failed', 
          variantErrors 
        });
      }

      // Check for duplicate codes
      const codes = variants.map(v => v.code);
      const existingVariants = await prisma.productVariant.findMany({
        where: { code: { in: codes } }
      });

      if (existingVariants.length > 0) {
        return reply.status(409).send({ 
          error: 'Duplicate product codes', 
          duplicates: existingVariants.map(v => v.code)
        });
      }

      // Create product with variants
      const product = await prisma.product.create({
        data: {
          name,
          variants: {
            create: variants.map((variant, index) => ({
              dose: variant.dose,
              code: variant.code,
              buyingPrice: variant.buyingPrice,
              sellingPrice: variant.sellingPrice,
              sortOrder: index * 10
            }))
          }
        },
        include: {
          variants: {
            orderBy: { sortOrder: 'asc' }
          }
        }
      });

      return reply.status(201).send({ success: true, product });
    } catch (error) {
      fastify.log.error(error);
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2002') {
        return reply.status(409).send({ error: 'Product name already exists' });
      }
      return reply.status(500).send({ error: 'Failed to create product' });
    }
  });

  // Update product name
  fastify.put<{
    Params: { id: string };
    Body: { name: string };
  }>('/products/:id', async (request, reply) => {
    try {
      const { id } = request.params;
      const { name } = request.body;

      if (!name || name.trim() === '') {
        return reply.status(400).send({ error: 'Product name is required' });
      }

      const product = await prisma.product.update({
        where: { id },
        data: { name },
        include: {
          variants: {
            orderBy: { sortOrder: 'asc' }
          }
        }
      });

      return reply.send({ success: true, product });
    } catch (error) {
      fastify.log.error(error);
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2025') {
          return reply.status(404).send({ error: 'Product not found' });
        }
        if (error.code === 'P2002') {
          return reply.status(409).send({ error: 'Product name already exists' });
        }
      }
      return reply.status(500).send({ error: 'Failed to update product' });
    }
  });

  // Delete product
  fastify.delete<{
    Params: { id: string };
  }>('/products/:id', async (request, reply) => {
    try {
      const { id } = request.params;

      // Check if any variants are used in orders
      const product = await prisma.product.findUnique({
        where: { id },
        include: {
          variants: {
            include: {
              orderItems: true
            }
          }
        }
      });

      if (!product) {
        return reply.status(404).send({ error: 'Product not found' });
      }

      const hasOrders = product.variants.some(v => v.orderItems.length > 0);
      if (hasOrders) {
        return reply.status(409).send({ 
          error: 'Cannot delete product with existing orders' 
        });
      }

      await prisma.product.delete({ where: { id } });

      return reply.send({ success: true });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({ error: 'Failed to delete product' });
    }
  });

  // Add variant to product
  fastify.post<{
    Params: { id: string };
    Body: CreateVariantRequest;
  }>('/products/:id/variants', async (request, reply) => {
    try {
      const { id } = request.params;
      const variantData = request.body;

      // Validate variant
      const errors = validateVariant(variantData);
      if (errors.length > 0) {
        return reply.status(400).send({ error: 'Validation failed', errors });
      }

      // Check if product exists
      const product = await prisma.product.findUnique({
        where: { id },
        include: { variants: true }
      });

      if (!product) {
        return reply.status(404).send({ error: 'Product not found' });
      }

      // Check for duplicate code
      const existing = await prisma.productVariant.findUnique({
        where: { code: variantData.code }
      });

      if (existing) {
        return reply.status(409).send({ error: 'Product code already exists' });
      }

      // Get max sort order
      const maxSortOrder = Math.max(...product.variants.map(v => v.sortOrder), 0);

      // Create variant
      const variant = await prisma.productVariant.create({
        data: {
          productId: id,
          dose: variantData.dose,
          code: variantData.code,
          buyingPrice: variantData.buyingPrice,
          sellingPrice: variantData.sellingPrice,
          sortOrder: maxSortOrder + 10
        }
      });

      return reply.status(201).send({ 
        success: true, 
        variant: {
          ...variant,
          buyingPrice: Number(variant.buyingPrice),
          sellingPrice: Number(variant.sellingPrice),
          profitMargin: calculateProfitMargin(Number(variant.buyingPrice), Number(variant.sellingPrice))
        }
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({ error: 'Failed to create variant' });
    }
  });

  // Update variant
  fastify.put<{
    Params: { id: string };
    Body: UpdateVariantRequest;
  }>('/variants/:id', async (request, reply) => {
    try {
      const { id } = request.params;
      const updates = request.body;

      // Get existing variant
      const existing = await prisma.productVariant.findUnique({
        where: { id }
      });

      if (!existing) {
        return reply.status(404).send({ error: 'Variant not found' });
      }

      // Prepare update data
      const updateData: Prisma.ProductVariantUpdateInput = {};
      
      if (updates.dose !== undefined) updateData.dose = updates.dose;
      if (updates.buyingPrice !== undefined) updateData.buyingPrice = updates.buyingPrice;
      if (updates.sellingPrice !== undefined) updateData.sellingPrice = updates.sellingPrice;
      if (updates.sortOrder !== undefined) updateData.sortOrder = updates.sortOrder;

      // Validate if prices are being updated
      if (updates.buyingPrice !== undefined || updates.sellingPrice !== undefined) {
        const buyingPrice = updates.buyingPrice ?? Number(existing.buyingPrice);
        const sellingPrice = updates.sellingPrice ?? Number(existing.sellingPrice);
        
        if (sellingPrice <= buyingPrice) {
          return reply.status(400).send({ 
            error: 'Selling price must be greater than buying price' 
          });
        }
      }

      const variant = await prisma.productVariant.update({
        where: { id },
        data: updateData
      });

      return reply.send({ 
        success: true, 
        variant: {
          ...variant,
          buyingPrice: Number(variant.buyingPrice),
          sellingPrice: Number(variant.sellingPrice),
          profitMargin: calculateProfitMargin(Number(variant.buyingPrice), Number(variant.sellingPrice))
        }
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({ error: 'Failed to update variant' });
    }
  });

  // Delete variant
  fastify.delete<{
    Params: { id: string };
  }>('/variants/:id', async (request, reply) => {
    try {
      const { id } = request.params;

      // Check if variant is used in orders
      const variant = await prisma.productVariant.findUnique({
        where: { id },
        include: { orderItems: true }
      });

      if (!variant) {
        return reply.status(404).send({ error: 'Variant not found' });
      }

      if (variant.orderItems.length > 0) {
        return reply.status(409).send({ 
          error: 'Cannot delete variant used in existing orders' 
        });
      }

      await prisma.productVariant.delete({ where: { id } });

      return reply.send({ success: true });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({ error: 'Failed to delete variant' });
    }
  });

  // Reorder variants
  fastify.put<{
    Params: { id: string };
    Body: ReorderVariantsRequest;
  }>('/products/:id/variants/reorder', async (request, reply) => {
    try {
      const { id } = request.params;
      const { variantIds } = request.body;

      if (!variantIds || !Array.isArray(variantIds)) {
        return reply.status(400).send({ error: 'Variant IDs array is required' });
      }

      // Update sort order for each variant
      const updates = variantIds.map((variantId, index) => 
        prisma.productVariant.update({
          where: { id: variantId },
          data: { sortOrder: index * 10 }
        })
      );

      await prisma.$transaction(updates);

      return reply.send({ success: true });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({ error: 'Failed to reorder variants' });
    }
  });
};

// Helper function
function calculateProfitMargin(buyingPrice: number, sellingPrice: number): number {
  if (sellingPrice === 0) return 0;
  return Number(((sellingPrice - buyingPrice) / sellingPrice * 100).toFixed(2));
}

export default productsRoute;