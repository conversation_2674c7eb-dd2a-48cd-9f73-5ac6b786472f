import { FastifyPluginAsync } from 'fastify';
import { PrismaClient } from '../generated/prisma/index.js';
import { CoinGeckoService } from '../services/coingecko.service.js';
import type { 
  CreatePricingRequest, 
  UpdatePricingRequest, 
  BulkImportRequest,
  PricingItem,
  PricingValidationError,
  BulkImportResult
} from '../../../shared/types/pricing.types.js';

const prisma = new PrismaClient();

// Validation helpers
function validatePricing(data: Partial<CreatePricingRequest>): PricingValidationError[] {
  const errors: PricingValidationError[] = [];

  if (!data.product || data.product.trim() === '') {
    errors.push({ field: 'product', message: 'Product name is required' });
  }

  if (!data.dose || data.dose.trim() === '') {
    errors.push({ field: 'dose', message: 'Dose is required' });
  }

  if (!data.code || data.code.trim() === '') {
    errors.push({ field: 'code', message: 'Product code is required' });
  } else if (data.code.length > 10) {
    errors.push({ field: 'code', message: 'Product code must be 10 characters or less' });
  }

  if (data.buyingPrice === undefined || data.buyingPrice === null) {
    errors.push({ field: 'buyingPrice', message: 'Buying price is required' });
  } else if (data.buyingPrice < 0) {
    errors.push({ field: 'buyingPrice', message: 'Buying price must be positive' });
  }

  if (data.sellingPrice === undefined || data.sellingPrice === null) {
    errors.push({ field: 'sellingPrice', message: 'Selling price is required' });
  } else if (data.sellingPrice < 0) {
    errors.push({ field: 'sellingPrice', message: 'Selling price must be positive' });
  }

  if (data.buyingPrice !== undefined && data.sellingPrice !== undefined) {
    if (data.sellingPrice <= data.buyingPrice) {
      errors.push({ 
        field: 'sellingPrice', 
        message: 'Selling price must be greater than buying price',
        value: { buyingPrice: data.buyingPrice, sellingPrice: data.sellingPrice }
      });
    }
  }

  return errors;
}

async function checkProductInUse(code: string): Promise<boolean> {
  const orderItem = await prisma.orderItem.findFirst({
    where: { code }
  });
  return !!orderItem;
}

const pricingRoute: FastifyPluginAsync = async (fastify) => {
  const coinGeckoService = new CoinGeckoService(
    fastify.config.COINGECKO_URL || 'https://api.coingecko.com/api/v3'
  );

  // Get all pricing items - now reading from products/variants tables
  fastify.get('/pricing', async (request, reply) => {
    try {
      // Fetch products with their variants
      const products = await prisma.product.findMany({
        include: {
          variants: {
            orderBy: { sortOrder: 'asc' }
          }
        },
        orderBy: { name: 'asc' }
      });

      // Flatten to match the old pricing API format
      const formattedPricing = products.flatMap(product => 
        product.variants.map(variant => ({
          code: variant.code,
          product: product.name,
          dose: variant.dose,
          buyingPrice: Number(variant.buyingPrice),
          sellingPrice: Number(variant.sellingPrice),
          createdAt: variant.createdAt
        }))
      );

      return reply.send({ pricing: formattedPricing });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({ 
        error: 'Failed to fetch pricing data' 
      });
    }
  });

  // Create single product
  fastify.post<{
    Body: CreatePricingRequest;
  }>('/pricing', async (request, reply) => {
    try {
      // Validate request
      const validationErrors = validatePricing(request.body);
      if (validationErrors.length > 0) {
        return reply.status(400).send({ 
          error: 'Validation failed', 
          validationErrors 
        });
      }

      // Check if code already exists
      const existing = await prisma.pricing.findUnique({
        where: { code: request.body.code }
      });

      if (existing) {
        return reply.status(409).send({ 
          error: 'Product code already exists',
          validationErrors: [{ field: 'code', message: 'This product code is already in use' }]
        });
      }

      // Create product
      const created = await prisma.pricing.create({
        data: {
          code: request.body.code,
          product: request.body.product,
          dose: request.body.dose,
          buyingPrice: request.body.buyingPrice,
          sellingPrice: request.body.sellingPrice
        }
      });

      const product: PricingItem = {
        code: created.code,
        product: created.product,
        dose: created.dose,
        buyingPrice: Number(created.buyingPrice),
        sellingPrice: Number(created.sellingPrice),
        createdAt: created.updatedAt
      };

      return reply.status(201).send({ success: true, product });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({ error: 'Failed to create product' });
    }
  });

  // Update product
  fastify.put<{
    Params: { code: string };
    Body: UpdatePricingRequest;
  }>('/pricing/:code', async (request, reply) => {
    try {
      const { code } = request.params;

      // Check if product exists
      const existing = await prisma.pricing.findUnique({
        where: { code }
      });

      if (!existing) {
        return reply.status(404).send({ error: 'Product not found' });
      }

      // Prepare update data with existing values as defaults
      const updateData = {
        product: request.body.product ?? existing.product,
        dose: request.body.dose ?? existing.dose,
        buyingPrice: request.body.buyingPrice ?? Number(existing.buyingPrice),
        sellingPrice: request.body.sellingPrice ?? Number(existing.sellingPrice)
      };

      // Validate the complete updated data
      const validationErrors = validatePricing({ ...updateData, code });
      if (validationErrors.length > 0) {
        return reply.status(400).send({ 
          error: 'Validation failed', 
          validationErrors 
        });
      }

      // Update product
      const updated = await prisma.pricing.update({
        where: { code },
        data: updateData
      });

      const product: PricingItem = {
        code: updated.code,
        product: updated.product,
        dose: updated.dose,
        buyingPrice: Number(updated.buyingPrice),
        sellingPrice: Number(updated.sellingPrice),
        createdAt: updated.updatedAt
      };

      return reply.send({ success: true, product });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({ error: 'Failed to update product' });
    }
  });

  // Delete product
  fastify.delete<{
    Params: { code: string };
  }>('/pricing/:code', async (request, reply) => {
    try {
      const { code } = request.params;

      // Check if product exists
      const existing = await prisma.pricing.findUnique({
        where: { code }
      });

      if (!existing) {
        return reply.status(404).send({ error: 'Product not found' });
      }

      // Check if product is used in any orders
      const inUse = await checkProductInUse(code);
      if (inUse) {
        return reply.status(409).send({ 
          error: 'Cannot delete product that is used in existing orders' 
        });
      }

      // Delete product
      await prisma.pricing.delete({
        where: { code }
      });

      return reply.send({ success: true });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({ error: 'Failed to delete product' });
    }
  });

  // Bulk import products
  fastify.post<{
    Body: BulkImportRequest;
  }>('/pricing/bulk', async (request, reply) => {
    try {
      const { mode, products } = request.body;

      if (!products || !Array.isArray(products)) {
        return reply.status(400).send({ error: 'Products array is required' });
      }

      if (!mode || !['append', 'replace'].includes(mode)) {
        return reply.status(400).send({ error: 'Mode must be either "append" or "replace"' });
      }

      // Validate all products
      const validationResults: { row: number; errors: PricingValidationError[] }[] = [];
      const validProducts: CreatePricingRequest[] = [];

      products.forEach((product, index) => {
        const errors = validatePricing(product);
        if (errors.length > 0) {
          validationResults.push({ row: index + 1, errors });
        } else {
          validProducts.push(product);
        }
      });

      if (validationResults.length > 0) {
        return reply.status(400).send({
          success: false,
          imported: 0,
          errors: validationResults
        });
      }

      // Check for duplicate codes in the import
      const codes = new Set<string>();
      const duplicates: { row: number; errors: PricingValidationError[] }[] = [];

      validProducts.forEach((product, index) => {
        if (codes.has(product.code)) {
          duplicates.push({
            row: index + 1,
            errors: [{ field: 'code', message: 'Duplicate code in import data' }]
          });
        }
        codes.add(product.code);
      });

      if (duplicates.length > 0) {
        return reply.status(400).send({
          success: false,
          imported: 0,
          errors: duplicates
        });
      }

      // Perform the import
      if (mode === 'replace') {
        // Check if any existing products are used in orders
        const existingProducts = await prisma.pricing.findMany();
        for (const product of existingProducts) {
          const inUse = await checkProductInUse(product.code);
          if (inUse) {
            return reply.status(409).send({ 
              error: `Cannot replace products: Product code "${product.code}" is used in existing orders` 
            });
          }
        }

        // Delete all existing products and insert new ones in a transaction
        await prisma.$transaction(async (tx) => {
          await tx.pricing.deleteMany();
          await tx.pricing.createMany({
            data: validProducts.map(p => ({
              code: p.code,
              product: p.product,
              dose: p.dose,
              buyingPrice: p.buyingPrice,
              sellingPrice: p.sellingPrice
            }))
          });
        });
      } else {
        // Append mode - check for existing codes
        const existingCodes = await prisma.pricing.findMany({
          where: { code: { in: Array.from(codes) } },
          select: { code: true }
        });

        if (existingCodes.length > 0) {
          const conflicts = existingCodes.map(item => ({
            row: validProducts.findIndex(p => p.code === item.code) + 1,
            errors: [{ field: 'code', message: `Code "${item.code}" already exists` }]
          }));

          return reply.status(400).send({
            success: false,
            imported: 0,
            errors: conflicts
          });
        }

        // Insert new products
        await prisma.pricing.createMany({
          data: validProducts.map(p => ({
            code: p.code,
            product: p.product,
            dose: p.dose,
            buyingPrice: p.buyingPrice,
            sellingPrice: p.sellingPrice
          }))
        });
      }

      return reply.send({
        success: true,
        imported: validProducts.length
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({ error: 'Failed to import products' });
    }
  });

  // Export products
  fastify.get<{
    Querystring: { format?: 'csv' | 'json' };
  }>('/pricing/export', async (request, reply) => {
    try {
      const format = request.query.format || 'json';

      const pricingData = await prisma.pricing.findMany({
        orderBy: [
          { product: 'asc' },
          { dose: 'asc' }
        ]
      });

      const formattedPricing = pricingData.map(item => ({
        code: item.code,
        product: item.product,
        dose: item.dose,
        buyingPrice: Number(item.buyingPrice),
        sellingPrice: Number(item.sellingPrice)
      }));

      if (format === 'csv') {
        // Generate CSV
        const header = 'Product,Dose,Code,Buying Price,Selling Price';
        const rows = formattedPricing.map(item => 
          `${item.product},${item.dose},${item.code},${item.buyingPrice},${item.sellingPrice}`
        );
        const csv = [header, ...rows].join('\n');

        reply.header('Content-Type', 'text/csv');
        reply.header('Content-Disposition', 'attachment; filename="pricing-export.csv"');
        return reply.send(csv);
      } else {
        // Return JSON
        reply.header('Content-Type', 'application/json');
        reply.header('Content-Disposition', 'attachment; filename="pricing-export.json"');
        return reply.send({ pricing: formattedPricing });
      }
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({ error: 'Failed to export pricing data' });
    }
  });

  // BTC rate endpoint (existing)
  fastify.get('/pricing/btc-rate', async (request, reply) => {
    try {
      const rate = await coinGeckoService.getBtcRate();
      
      return reply.send({ 
        rate,
        source: 'coingecko',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({ 
        error: 'Failed to fetch BTC rate' 
      });
    }
  });
};

export default pricingRoute;