import { FastifyPluginAsync } from 'fastify';
import { DashboardService } from '../services/dashboard.service.js';

const dashboardRoute: FastifyPluginAsync = async (fastify) => {
  const dashboardService = new DashboardService();

  // GET /dashboard/metrics - Get dashboard metrics
  fastify.get('/metrics', {
    schema: {
      querystring: {
        type: 'object',
        properties: {
          dateRange: { 
            type: 'string',
            enum: ['today', 'yesterday', 'last7Days', 'last30Days', 'custom', 'range', 'lifetime']
          },
          startDate: {
            type: 'string',
            pattern: '^\\d{4}-\\d{2}-\\d{2}$'
          },
          endDate: {
            type: 'string',
            pattern: '^\\d{4}-\\d{2}-\\d{2}$'
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { dateRange, startDate, endDate } = request.query as { 
        dateRange?: 'today' | 'yesterday' | 'last7Days' | 'last30Days' | 'custom' | 'range' | 'lifetime';
        startDate?: string;
        endDate?: string;
      };
      const metrics = await dashboardService.getDashboardMetrics(dateRange, startDate, endDate);

      return reply.send(metrics);
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({ 
        error: error instanceof Error ? error.message : 'Failed to fetch dashboard metrics' 
      });
    }
  });

  // GET /dashboard/revenue-profit-trend - Get daily revenue and profit trend
  fastify.get('/revenue-profit-trend', {
    schema: {
      querystring: {
        type: 'object',
        properties: {
          days: { 
            type: 'number',
            minimum: 1,
            maximum: 365,
            default: 30
          },
          dateRange: { 
            type: 'string',
            enum: ['today', 'yesterday', 'last7Days', 'last30Days', 'custom', 'range', 'lifetime']
          },
          startDate: {
            type: 'string',
            pattern: '^\\d{4}-\\d{2}-\\d{2}$'
          },
          endDate: {
            type: 'string',
            pattern: '^\\d{4}-\\d{2}-\\d{2}$'
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { days, dateRange, startDate, endDate } = request.query as { 
        days?: number;
        dateRange?: 'today' | 'yesterday' | 'last7Days' | 'last30Days' | 'custom' | 'range' | 'lifetime';
        startDate?: string;
        endDate?: string;
      };
      const data = await dashboardService.getDailyRevenueProfit(days, dateRange, startDate, endDate);

      return reply.send(data);
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({ 
        error: error instanceof Error ? error.message : 'Failed to fetch revenue and profit trend' 
      });
    }
  });

  // GET /dashboard/daily-orders - Get daily order count
  fastify.get('/daily-orders', {
    schema: {
      querystring: {
        type: 'object',
        properties: {
          days: { 
            type: 'number',
            minimum: 1,
            maximum: 365,
            default: 30
          },
          dateRange: { 
            type: 'string',
            enum: ['today', 'yesterday', 'last7Days', 'last30Days', 'custom', 'range', 'lifetime']
          },
          startDate: {
            type: 'string',
            pattern: '^\\d{4}-\\d{2}-\\d{2}$'
          },
          endDate: {
            type: 'string',
            pattern: '^\\d{4}-\\d{2}-\\d{2}$'
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { days, dateRange, startDate, endDate } = request.query as { 
        days?: number;
        dateRange?: 'today' | 'yesterday' | 'last7Days' | 'last30Days' | 'custom' | 'range' | 'lifetime';
        startDate?: string;
        endDate?: string;
      };
      const data = await dashboardService.getDailyOrderCount(days, dateRange, startDate, endDate);

      return reply.send(data);
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({ 
        error: error instanceof Error ? error.message : 'Failed to fetch daily order count' 
      });
    }
  });

  // GET /dashboard/shipping-status - Get shipping status distribution
  fastify.get('/shipping-status', async (request, reply) => {
    try {
      const data = await dashboardService.getShippingStatusDistribution();

      return reply.send(data);
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({ 
        error: error instanceof Error ? error.message : 'Failed to fetch shipping status distribution' 
      });
    }
  });
};

export default dashboardRoute;