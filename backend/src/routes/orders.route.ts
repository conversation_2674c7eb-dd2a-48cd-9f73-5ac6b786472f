import { FastifyPluginAsync } from 'fastify';
import { PrismaClient } from '../generated/prisma/index.js';
import { CoinGeckoService } from '../services/coingecko.service.js';
import { emailService } from '../services/email.service.js';
import { EnhancedTrackingService } from '../services/enhancedTracking.service.js';
import type { CreateOrderRequest, UpdateOrderRequest, OrderCalculations } from '../../../shared/types/order.types.js';

const prisma = new PrismaClient();

const ordersRoute: FastifyPluginAsync = async (fastify) => {
  const coinGeckoService = new CoinGeckoService(fastify.config.COINGECKO_URL);
  const parcelsAppService = new EnhancedTrackingService(fastify, fastify.config.PARCELS_APP_API_KEY, prisma);

  // Helper function to compute shipping status
  const getShippingStatus = (order: any): string | null => {
    if (!order.tracking17) return null;
    
    if (!order.trackingStatus) return 'Info Received';
    
    const status = order.trackingStatus.toLowerCase();
    if (status === 'delivered') return 'Delivered';
    if (['in transit', 'pick up', 'out for delivery', 'alert', 'undelivered'].includes(status)) return 'In Transit';
    
    return 'Info Received';
  };

  // Helper function to determine discount rate based on order number
  const getDiscountRate = (orderNumber: string | null): number => {
    if (!orderNumber) return 0.05; // Default to 5% for new orders without number yet
    
    // Extract the order sequence number from format ORD-YYYY-####
    const match = orderNumber.match(/^ORD-\d{4}-(\d{4})$/);
    if (!match) return 0.05; // Invalid format, default to 5%
    
    const orderNum = parseInt(match[1]);
    
    // Orders 1-35 get 3% discount, 36+ get 5% discount
    return orderNum <= 35 ? 0.03 : 0.05;
  };

  // Helper function to calculate order totals
  async function calculateOrderTotals(items: CreateOrderRequest['items'], orderNumber?: string | null): Promise<OrderCalculations> {
    // Fetch pricing from product variants
    const codes = items.map(item => item.code);
    const variantsData = await prisma.productVariant.findMany({
      where: {
        code: { in: codes }
      }
    });

    // Create a map for easy lookup
    const pricingMap = new Map(
      variantsData.map(v => [`${v.code}-${v.dose}`, v])
    );

    // Debug logging
    console.log('calculateOrderTotals - Items:', items);
    console.log('calculateOrderTotals - Variants found:', variantsData.length);
    console.log('calculateOrderTotals - Map keys:', Array.from(pricingMap.keys()));

    // Calculate subtotal and profit
    let subtotal = 0;
    let totalBuyingPrice = 0;

    const itemsWithPricing = items.map(item => {
      const lookupKey = `${item.code}-${item.dose}`;
      console.log(`calculateOrderTotals - Looking up: "${lookupKey}"`);
      const pricing = pricingMap.get(lookupKey);
      if (!pricing) {
        console.error(`calculateOrderTotals - Not found in map. Available keys:`, Array.from(pricingMap.keys()));
        throw new Error(`Pricing not found for ${item.code} ${item.dose}`);
      }

      const itemTotal = Number(pricing.sellingPrice) * item.qty;
      const itemBuyingTotal = Number(pricing.buyingPrice) * item.qty;
      
      subtotal += itemTotal;
      totalBuyingPrice += itemBuyingTotal;

      return {
        ...item,
        buyingPrice: Number(pricing.buyingPrice),
        sellingPrice: Number(pricing.sellingPrice)
      };
    });

    // Fixed shipping cost
    const shipping = 40;

    // Calculate discount based on order number
    const discountRate = getDiscountRate(orderNumber);
    const discount = subtotal * discountRate;

    // Calculate total
    const totalUsd = subtotal + shipping - discount;

    // Get BTC rate
    const btcRate = await coinGeckoService.getBtcRate();
    const totalBtc = totalUsd / btcRate;

    // Calculate profit
    const profitUsd = subtotal - totalBuyingPrice;
    const profitInr = profitUsd * 86;
    const profitMargin = (profitUsd / subtotal) * 100;

    return {
      subtotal,
      shipping,
      discount,
      totalUsd,
      totalBtc,
      profitUsd,
      profitInr,
      profitMargin
    };
  }

  // POST /orders - Create new order
  fastify.post<{
    Body: CreateOrderRequest;
  }>('/orders', async (request, reply) => {
    try {
      const { items, ...orderData } = request.body;

      // Handle order date - use provided date or current timestamp
      let placedAt: Date;
      if (orderData.orderDate) {
        // Validate and parse the provided date
        try {
          placedAt = new Date(orderData.orderDate);
          if (isNaN(placedAt.getTime())) {
            return reply.status(400).send({ 
              error: 'Invalid date format. Please use ISO 8601 format (YYYY-MM-DDTHH:mm:ss.sssZ)' 
            });
          }
          // Prevent future dates
          if (placedAt > new Date()) {
            return reply.status(400).send({ 
              error: 'Order date cannot be in the future' 
            });
          }
        } catch (error) {
          return reply.status(400).send({ 
            error: 'Invalid date format. Please use ISO 8601 format (YYYY-MM-DDTHH:mm:ss.sssZ)' 
          });
        }
      } else {
        placedAt = new Date();
      }

      // Validate required fields
      if (!orderData.customerName || !orderData.street1 || !orderData.city || 
          !orderData.postalCode || !items || items.length === 0) {
        return reply.status(400).send({ 
          error: 'Missing required fields' 
        });
      }

      // Generate the order number first to calculate correct discount
      const year = placedAt.getFullYear();
      
      // Get all orders for the current year to determine next number
      const yearOrders = await prisma.order.findMany({
        where: {
          orderNumber: {
            startsWith: `ORD-${year}-`
          }
        },
        select: {
          orderNumber: true
        }
      });

      let nextNumber = 1;
      if (yearOrders.length > 0) {
        const orderNumbers = yearOrders
          .map(order => {
            const parts = order.orderNumber.split('-');
            return parts.length === 3 ? parseInt(parts[2]) : 0;
          })
          .filter(num => !isNaN(num));
        
        if (orderNumbers.length > 0) {
          nextNumber = Math.max(...orderNumbers) + 1;
        }
      }

      const orderNumber = `ORD-${year}-${String(nextNumber).padStart(4, '0')}`;

      // Calculate order totals with the generated order number
      const calculations = await calculateOrderTotals(items, orderNumber);
      
      // Get pricing data from product variants
      const codes = items.map(item => item.code);
      
      // Debug logging
      console.log('Order creation - Requested items:', items);
      console.log('Order creation - Codes to search:', codes);
      
      const variantsData = await prisma.productVariant.findMany({
        where: {
          code: { in: codes }
        },
        include: {
          product: true
        }
      });

      // Debug logging
      console.log('Order creation - Found variants:', variantsData.map(v => ({
        code: v.code,
        dose: v.dose,
        productName: v.product.name
      })));

      const pricingMap = new Map(
        variantsData.map(v => [`${v.code}-${v.dose}`, v])
      );
      
      // Debug logging
      console.log('Order creation - Map keys:', Array.from(pricingMap.keys()));

      // Create order with items in a transaction
      const order = await prisma.$transaction(async (tx) => {
        // Create the order
        const newOrder = await tx.order.create({
          data: {
            orderNumber,
            placedAt,
            customerName: orderData.customerName,
            street1: orderData.street1,
            street2: orderData.street2 || null,
            city: orderData.city,
            state: orderData.state,
            postalCode: orderData.postalCode,
            country: orderData.country || 'United States',
            totalUsd: calculations.totalUsd,
            totalBtc: calculations.totalBtc,
            profitUsd: calculations.profitUsd,
            profitInr: calculations.profitInr,
            profitMargin: calculations.profitMargin,
            paymentMethod: orderData.paymentMethod,
            paymentUrl: orderData.paymentUrl || null,
            status: 'pending',
            items: {
              create: items.map(item => {
                const lookupKey = `${item.code}-${item.dose}`;
                console.log(`Order creation items - Looking up: "${lookupKey}"`);
                const pricing = pricingMap.get(lookupKey);
                if (!pricing) {
                  console.error(`Order creation items - Not found. Item:`, item);
                  console.error(`Order creation items - Available keys:`, Array.from(pricingMap.keys()));
                  throw new Error(`Pricing not found for ${item.code} ${item.dose}`);
                }
                return {
                  code: item.code,
                  dose: item.dose,
                  qty: item.qty,
                  buyingPrice: pricing.buyingPrice,
                  sellingPrice: pricing.sellingPrice,
                  variantId: pricing.id
                };
              })
            }
          },
          include: {
            items: true
          }
        });

        return newOrder;
      });

      // Convert Decimal strings to numbers
      const formattedOrder = {
        ...order,
        orderNumber: order.orderNumber,
        totalUsd: Number(order.totalUsd),
        totalBtc: Number(order.totalBtc),
        profitUsd: Number(order.profitUsd),
        profitInr: Number(order.profitInr),
        profitMargin: Number(order.profitMargin),
        // Legacy status field (to be removed later)
        status: order.status,
        // Two-category system
        supplierStatus: order.sentToSupplier ? 'sent' : 'not_sent',
        shippingStatus: getShippingStatus(order),
        trackingStatus: order.trackingStatus,
        sentToSupplier: order.sentToSupplier,
        sentAt: order.sentAt,
        items: order.items.map(item => ({
          ...item,
          id: item.id.toString(), // Convert BigInt to string
          buyingPrice: Number(item.buyingPrice),
          sellingPrice: Number(item.sellingPrice)
        }))
      };

      fastify.log.info(`Order created: ${order.orderNumber} (ID: ${order.id})`);
      return reply.status(201).send({ 
        order: {
          ...formattedOrder,
          calculations
        }
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({ 
        error: error instanceof Error ? error.message : 'Failed to create order' 
      });
    }
  });

  // GET /orders - List all orders with pagination
  fastify.get<{
    Querystring: {
      page?: string;
      limit?: string;
      search?: string;
    };
  }>('/orders', async (request, reply) => {
    try {
      // Parse pagination parameters
      const page = parseInt(request.query.page || '1', 10);
      const limit = parseInt(request.query.limit || '20', 10);
      const search = request.query.search?.trim();
      
      // Validate pagination parameters
      if (page < 1 || limit < 1 || limit > 100) {
        return reply.status(400).send({ 
          error: 'Invalid pagination parameters' 
        });
      }
      
      // Calculate offset
      const offset = (page - 1) * limit;
      
      // Build search filter
      let whereClause: any = {};
      if (search) {
        whereClause = {
          OR: [
            { customerName: { contains: search, mode: 'insensitive' } },
            { orderNumber: { contains: search, mode: 'insensitive' } },
            { id: { contains: search, mode: 'insensitive' } }
          ]
        };
      }
      
      // Get total count of orders with search filter
      const totalCount = await prisma.order.count({
        where: whereClause
      });
      
      // Fetch paginated orders with search filter
      const orders = await prisma.order.findMany({
        where: whereClause,
        orderBy: { orderNumber: 'desc' },
        include: { 
          items: true,
          history: true 
        },
        skip: offset,
        take: limit
      });

      // Convert Decimal strings to numbers
      const formattedOrders = orders.map(order => ({
        ...order,
        orderNumber: order.orderNumber,
        totalUsd: Number(order.totalUsd),
        totalBtc: Number(order.totalBtc),
        profitUsd: Number(order.profitUsd),
        profitInr: Number(order.profitInr),
        profitMargin: Number(order.profitMargin),
        // Legacy status field (to be removed later)
        status: order.status,
        // Two-category system
        supplierStatus: order.sentToSupplier ? 'sent' : 'not_sent',
        shippingStatus: getShippingStatus(order),
        trackingStatus: order.trackingStatus,
        sentToSupplier: order.sentToSupplier,
        sentAt: order.sentAt,
        items: order.items.map(item => ({
          ...item,
          id: item.id.toString(), // Convert BigInt to string
          buyingPrice: Number(item.buyingPrice),
          sellingPrice: Number(item.sellingPrice)
        })),
        history: order.history.map(h => ({
          ...h,
          oldQty: h.oldQty || null,
          newQty: h.newQty || null,
          oldPrice: h.oldPrice ? Number(h.oldPrice) : null,
          newPrice: h.newPrice ? Number(h.newPrice) : null
        }))
      }));

      // Calculate pagination metadata
      const totalPages = Math.ceil(totalCount / limit);
      const hasNextPage = page < totalPages;
      const hasPrevPage = page > 1;

      return reply.send({ 
        orders: formattedOrders,
        pagination: {
          currentPage: page,
          totalPages,
          totalCount,
          limit,
          hasNextPage,
          hasPrevPage
        }
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({ 
        error: 'Failed to fetch orders' 
      });
    }
  });

  // GET /orders/:id - Get single order
  fastify.get<{
    Params: { id: string };
  }>('/orders/:id', async (request, reply) => {
    try {
      const order = await prisma.order.findUnique({
        where: { id: request.params.id },
        include: { 
          items: true,
          history: true 
        }
      });

      if (!order) {
        return reply.status(404).send({ 
          error: 'Order not found' 
        });
      }

      // Convert Decimal strings to numbers
      const formattedOrder = {
        ...order,
        orderNumber: order.orderNumber,
        totalUsd: Number(order.totalUsd),
        totalBtc: Number(order.totalBtc),
        profitUsd: Number(order.profitUsd),
        profitInr: Number(order.profitInr),
        profitMargin: Number(order.profitMargin),
        // Legacy status field (to be removed later)
        status: order.status,
        // Two-category system
        supplierStatus: order.sentToSupplier ? 'sent' : 'not_sent',
        shippingStatus: getShippingStatus(order),
        trackingStatus: order.trackingStatus,
        sentToSupplier: order.sentToSupplier,
        sentAt: order.sentAt,
        trackingUrl: order.tracking17 ? parcelsAppService.generateTrackingUrl(order.tracking17) : undefined,
        items: order.items.map(item => ({
          ...item,
          id: item.id.toString(), // Convert BigInt to string
          buyingPrice: Number(item.buyingPrice),
          sellingPrice: Number(item.sellingPrice)
        })),
        history: order.history.map(h => ({
          ...h,
          oldQty: h.oldQty || null,
          newQty: h.newQty || null,
          oldPrice: h.oldPrice ? Number(h.oldPrice) : null,
          newPrice: h.newPrice ? Number(h.newPrice) : null
        }))
      };

      return reply.send({ order: formattedOrder });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({ 
        error: 'Failed to fetch order' 
      });
    }
  });

  // PATCH /orders/:id/tracking - Update tracking number
  fastify.patch<{
    Params: { id: string };
    Body: { tracking: string };
  }>('/orders/:id/tracking', async (request, reply) => {
    try {
      const { tracking } = request.body;

      if (!tracking) {
        return reply.status(400).send({ 
          error: 'Tracking number is required' 
        });
      }

      // Check if order exists and get current tracking
      const existingOrder = await prisma.order.findUnique({
        where: { id: request.params.id },
        select: { tracking17: true }
      });

      if (!existingOrder) {
        return reply.status(404).send({ 
          error: 'Order not found' 
        });
      }

      // Register tracking number with 17track if it's new or changed
      if (!existingOrder.tracking17 || existingOrder.tracking17 !== tracking) {
        await parcelsAppService.registerTrackingNumber(tracking);
      }

      const order = await prisma.order.update({
        where: { id: request.params.id },
        data: { 
          tracking17: tracking,
          status: 'shipped'
        },
        include: { items: true }
      });

      // Convert Decimal strings to numbers
      const formattedOrder = {
        ...order,
        orderNumber: order.orderNumber,
        totalUsd: Number(order.totalUsd),
        totalBtc: Number(order.totalBtc),
        profitUsd: Number(order.profitUsd),
        profitInr: Number(order.profitInr),
        profitMargin: Number(order.profitMargin),
        // Legacy status field (to be removed later)
        status: order.status,
        // Two-category system
        supplierStatus: order.sentToSupplier ? 'sent' : 'not_sent',
        shippingStatus: getShippingStatus(order),
        trackingStatus: order.trackingStatus,
        sentToSupplier: order.sentToSupplier,
        sentAt: order.sentAt,
        trackingUrl: order.tracking17 ? parcelsAppService.generateTrackingUrl(order.tracking17) : undefined,
        items: order.items.map(item => ({
          ...item,
          id: item.id.toString(), // Convert BigInt to string
          buyingPrice: Number(item.buyingPrice),
          sellingPrice: Number(item.sellingPrice)
        }))
      };

      fastify.log.info(`Tracking updated for order ${order.id}: ${tracking}`);
      return reply.send({ order: formattedOrder });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({ 
        error: 'Failed to update tracking' 
      });
    }
  });

  // GET /orders/:id/tracking-status - Get tracking status
  fastify.get<{
    Params: { id: string };
  }>('/orders/:id/tracking-status', async (request, reply) => {
    try {
      // Get order with tracking number and current status
      const order = await prisma.order.findUnique({
        where: { id: request.params.id },
        select: { 
          tracking17: true,
          trackingStatus: true,
          country: true 
        }
      });

      if (!order) {
        return reply.status(404).send({ 
          error: 'Order not found' 
        });
      }

      if (!order.tracking17) {
        return reply.status(400).send({ 
          error: 'No tracking number available' 
        });
      }

      // Check if already delivered to avoid wasting API credits
      if (order.trackingStatus === 'Delivered') {
        fastify.log.info(`Order ${request.params.id} already delivered, skipping API call`);
        return reply.send({ 
          trackingNumber: order.tracking17,
          status: 'Delivered',
          statusCode: '40',
          lastUpdate: new Date().toISOString(),
          trackingUrl: parcelsAppService.generateTrackingUrl(order.tracking17),
          cached: true
        });
      }

      // Get tracking status with destination country
      const status = await parcelsAppService.getTrackingStatus(order.tracking17, order.country || 'United States');
      
      if (!status) {
        return reply.status(500).send({ 
          error: 'Failed to get tracking status' 
        });
      }

      // Update order status based on tracking status
      let orderStatus: 'shipped' | 'delivered' | 'in_transit' = 'shipped';
      if (status.statusText === 'Delivered') {
        orderStatus = 'delivered';
      } else if (['In Transit', 'Pick Up', 'Out for Delivery'].includes(status.statusText)) {
        orderStatus = 'in_transit';
      }
      
      // Update order with tracking status and order status
      await prisma.order.update({
        where: { id: request.params.id },
        data: {
          trackingStatus: status.statusText,
          status: orderStatus
        }
      });

      return reply.send({ 
        trackingNumber: status.trackingNumber,
        status: status.statusText,
        statusCode: status.status,
        lastUpdate: status.lastUpdate,
        lastEventDescription: status.lastEventDescription,
        trackingUrl: parcelsAppService.generateTrackingUrl(order.tracking17)
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({ 
        error: 'Failed to get tracking status' 
      });
    }
  });

  // POST /orders/:id/send - Send order to supplier via email
  fastify.post<{
    Params: { id: string };
  }>('/orders/:id/send', async (request, reply) => {
    try {
      // Fetch the order with items
      const order = await prisma.order.findUnique({
        where: { id: request.params.id },
        include: { items: true }
      });

      if (!order) {
        return reply.status(404).send({ 
          error: 'Order not found' 
        });
      }

      // Check if already sent
      if (order.sentToSupplier) {
        return reply.status(400).send({ 
          error: 'Order has already been sent to supplier' 
        });
      }

      // Send email
      await emailService.sendOrderToSupplier(order);

      // Update order status
      const updatedOrder = await prisma.order.update({
        where: { id: request.params.id },
        data: { 
          sentToSupplier: true,
          sentAt: new Date(),
          status: 'sent'
        },
        include: { items: true }
      });

      // Convert Decimal strings to numbers
      const formattedOrder = {
        ...updatedOrder,
        orderNumber: updatedOrder.orderNumber,
        totalUsd: Number(updatedOrder.totalUsd),
        totalBtc: Number(updatedOrder.totalBtc),
        profitUsd: Number(updatedOrder.profitUsd),
        profitInr: Number(updatedOrder.profitInr),
        profitMargin: Number(updatedOrder.profitMargin),
        // Legacy status field (to be removed later)
        status: updatedOrder.status,
        // Two-category system
        supplierStatus: updatedOrder.sentToSupplier ? 'sent' : 'not_sent',
        shippingStatus: getShippingStatus(updatedOrder),
        trackingStatus: updatedOrder.trackingStatus,
        sentToSupplier: updatedOrder.sentToSupplier,
        sentAt: updatedOrder.sentAt,
        items: updatedOrder.items.map(item => ({
          ...item,
          id: item.id.toString(), // Convert BigInt to string
          buyingPrice: Number(item.buyingPrice),
          sellingPrice: Number(item.sellingPrice)
        }))
      };

      fastify.log.info(`Order ${order.id} sent to supplier`);
      return reply.send({ 
        success: true,
        order: formattedOrder,
        message: 'Order successfully sent to supplier'
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({ 
        error: error instanceof Error ? error.message : 'Failed to send order to supplier' 
      });
    }
  });

  // PATCH /orders/:id/mark-sent - Manually mark order as sent to supplier
  fastify.patch<{
    Params: { id: string };
  }>('/orders/:id/mark-sent', async (request, reply) => {
    try {
      // Fetch the order with items
      const order = await prisma.order.findUnique({
        where: { id: request.params.id },
        include: { items: true }
      });

      if (!order) {
        return reply.status(404).send({ 
          error: 'Order not found' 
        });
      }

      // Check if already sent
      if (order.sentToSupplier) {
        return reply.status(400).send({ 
          error: 'Order has already been marked as sent to supplier' 
        });
      }

      // Determine if order is backdated (placedAt is in the past compared to current date)
      const currentDate = new Date();
      const orderDate = new Date(order.placedAt);
      
      // Set sentAt to order date if order is backdated, otherwise use current date
      const sentAtDate = orderDate < currentDate ? orderDate : currentDate;

      // Update order status - mark as sent without actually sending email
      const updatedOrder = await prisma.order.update({
        where: { id: request.params.id },
        data: { 
          sentToSupplier: true,
          sentAt: sentAtDate,
          status: 'sent'
        },
        include: { items: true }
      });

      // Convert Decimal strings to numbers
      const formattedOrder = {
        ...updatedOrder,
        orderNumber: updatedOrder.orderNumber,
        totalUsd: Number(updatedOrder.totalUsd),
        totalBtc: Number(updatedOrder.totalBtc),
        profitUsd: Number(updatedOrder.profitUsd),
        profitInr: Number(updatedOrder.profitInr),
        profitMargin: Number(updatedOrder.profitMargin),
        // Legacy status field (to be removed later)
        status: updatedOrder.status,
        // Two-category system
        supplierStatus: updatedOrder.sentToSupplier ? 'sent' : 'not_sent',
        shippingStatus: getShippingStatus(updatedOrder),
        trackingStatus: updatedOrder.trackingStatus,
        sentToSupplier: updatedOrder.sentToSupplier,
        sentAt: updatedOrder.sentAt,
        items: updatedOrder.items.map(item => ({
          ...item,
          id: item.id.toString(), // Convert BigInt to string
          buyingPrice: Number(item.buyingPrice),
          sellingPrice: Number(item.sellingPrice)
        }))
      };

      fastify.log.info(`Order ${order.id} manually marked as sent to supplier`);
      return reply.send({ 
        success: true,
        order: formattedOrder,
        message: 'Order successfully marked as sent to supplier'
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({ 
        error: error instanceof Error ? error.message : 'Failed to mark order as sent' 
      });
    }
  });

  // PUT /orders/:id - Update order (only if no tracking number)
  fastify.put<{
    Params: { id: string };
    Body: UpdateOrderRequest;
  }>('/orders/:id', async (request, reply) => {
    try {
      const { items, ...orderData } = request.body;

      // First check if order exists and has tracking
      const existingOrder = await prisma.order.findUnique({
        where: { id: request.params.id },
        select: { 
          id: true,
          tracking17: true,
          orderNumber: true
        }
      });
      
      // Get total count of orders with search filter
      const totalCount = await prisma.order.count({
        where: whereClause
      });
      
      // Fetch paginated orders with search filter
      const orders = await prisma.order.findMany({
        where: whereClause,
        orderBy: { orderNumber: 'desc' },
        include: { 
          items: true,
          history: true 
        },
        skip: offset,
        take: limit
      });

      // Build update data object with only provided fields
      const updateData: any = {};
      
      // Handle order date if provided
      if (orderData.orderDate !== undefined) {
        try {
          const newDate = new Date(orderData.orderDate);
          if (isNaN(newDate.getTime())) {
            return reply.status(400).send({ 
              error: 'Invalid date format. Please use ISO 8601 format (YYYY-MM-DDTHH:mm:ss.sssZ)' 
            });
          }
          // Prevent future dates
          if (newDate > new Date()) {
            return reply.status(400).send({ 
              error: 'Order date cannot be in the future' 
            });
          }
          updateData.placedAt = newDate;
        } catch (error) {
          return reply.status(400).send({ 
            error: 'Invalid date format. Please use ISO 8601 format (YYYY-MM-DDTHH:mm:ss.sssZ)' 
          });
        }
      }
      
      // Update customer and address fields if provided
      if (orderData.customerName !== undefined) updateData.customerName = orderData.customerName;
      if (orderData.street1 !== undefined) updateData.street1 = orderData.street1;
      if (orderData.street2 !== undefined) updateData.street2 = orderData.street2 || null;
      if (orderData.city !== undefined) updateData.city = orderData.city;
      if (orderData.state !== undefined) updateData.state = orderData.state;
      if (orderData.postalCode !== undefined) updateData.postalCode = orderData.postalCode;
      if (orderData.country !== undefined) updateData.country = orderData.country;
      if (orderData.paymentMethod !== undefined) updateData.paymentMethod = orderData.paymentMethod;
      if (orderData.paymentUrl !== undefined) updateData.paymentUrl = orderData.paymentUrl || null;

      // If items are provided, recalculate totals
      if (items && items.length > 0) {
        // Calculate new order totals with existing order number
        const calculations = await calculateOrderTotals(items, existingOrder.orderNumber);
        
        // Add financial fields to update
        updateData.totalUsd = calculations.totalUsd;
        updateData.totalBtc = calculations.totalBtc;
        updateData.profitUsd = calculations.profitUsd;
        updateData.profitInr = calculations.profitInr;
        updateData.profitMargin = calculations.profitMargin;

        // Get pricing data from product variants
        const codes = items.map(item => item.code);
        const variantsData = await prisma.productVariant.findMany({
          where: {
            code: { in: codes }
          },
          include: {
            product: true
          }
        });

        const pricingMap = new Map(
          variantsData.map(v => [`${v.code}-${v.dose}`, v])
        );

        // Update order and items in transaction
        const updatedOrder = await prisma.$transaction(async (tx) => {
          // Get existing items before deletion for history tracking
          const existingItems = await tx.orderItem.findMany({
            where: { orderId: request.params.id }
          });

          // Create a map of existing items for comparison
          const existingItemsMap = new Map(
            existingItems.map(item => [`${item.code}-${item.dose}`, item])
          );

          // Create a map of new items for comparison
          const newItemsMap = new Map(
            items.map(item => [`${item.code}-${item.dose}`, item])
          );

          // Track changes for history
          const historyEntries: any[] = [];

          // Find removed items
          for (const [key, existingItem] of existingItemsMap) {
            if (!newItemsMap.has(key)) {
              historyEntries.push({
                orderId: request.params.id,
                changeType: 'removed',
                itemCode: existingItem.code,
                itemDose: existingItem.dose,
                oldQty: existingItem.qty,
                newQty: null,
                oldPrice: existingItem.sellingPrice,
                newPrice: null
              });
            }
          }

          // Find added or modified items
          for (const [key, newItem] of newItemsMap) {
            const existingItem = existingItemsMap.get(key);
            const pricing = pricingMap.get(key);
            
            if (!existingItem) {
              // Item was added
              historyEntries.push({
                orderId: request.params.id,
                changeType: 'added',
                itemCode: newItem.code,
                itemDose: newItem.dose,
                oldQty: null,
                newQty: newItem.qty,
                oldPrice: null,
                newPrice: pricing?.sellingPrice || 0
              });
            } else if (existingItem.qty !== newItem.qty) {
              // Quantity was modified
              historyEntries.push({
                orderId: request.params.id,
                changeType: 'modified',
                itemCode: newItem.code,
                itemDose: newItem.dose,
                oldQty: existingItem.qty,
                newQty: newItem.qty,
                oldPrice: existingItem.sellingPrice,
                newPrice: pricing?.sellingPrice || existingItem.sellingPrice
              });
            }
          }

          // Delete existing items
          await tx.orderItem.deleteMany({
            where: { orderId: request.params.id }
          });

          // Create history entries if there are any changes
          if (historyEntries.length > 0) {
            await tx.orderHistory.createMany({
              data: historyEntries
            });
          }

          // Update order and create new items
          const order = await tx.order.update({
            where: { id: request.params.id },
            data: {
              ...updateData,
              // Reset supplier status when order is edited
              sentToSupplier: false,
              sentAt: null,
              status: 'pending',
              // Mark as edited
              isEdited: true,
              editedAt: new Date(),
              // Create new items
              items: {
                create: items.map(item => {
                  const pricing = pricingMap.get(`${item.code}-${item.dose}`);
                  if (!pricing) {
                    throw new Error(`Pricing not found for ${item.code} ${item.dose}`);
                  }
                  return {
                    code: item.code,
                    dose: item.dose,
                    qty: item.qty,
                    buyingPrice: pricing.buyingPrice,
                    sellingPrice: pricing.sellingPrice,
                    variantId: pricing.id
                  };
                })
              }
            },
            include: {
              items: true,
              history: true
            }
          });

          return order;
        });

        // Convert Decimal strings to numbers and include calculations
        const formattedOrder = {
          ...updatedOrder,
          orderNumber: updatedOrder.orderNumber,
          totalUsd: Number(updatedOrder.totalUsd),
          totalBtc: Number(updatedOrder.totalBtc),
          profitUsd: Number(updatedOrder.profitUsd),
          profitInr: Number(updatedOrder.profitInr),
          profitMargin: Number(updatedOrder.profitMargin),
          status: updatedOrder.status,
          supplierStatus: updatedOrder.sentToSupplier ? 'sent' : 'not_sent',
          shippingStatus: getShippingStatus(updatedOrder),
          trackingStatus: updatedOrder.trackingStatus,
          sentToSupplier: updatedOrder.sentToSupplier,
          sentAt: updatedOrder.sentAt,
          items: updatedOrder.items.map(item => ({
            ...item,
            id: item.id.toString(),
            buyingPrice: Number(item.buyingPrice),
            sellingPrice: Number(item.sellingPrice)
          })),
          calculations
        };

        fastify.log.info(`Order updated with items: ${existingOrder.orderNumber} (ID: ${request.params.id})`);
        return reply.send({ 
          order: formattedOrder
        });
      } else {
        // Update without changing items
        updateData.sentToSupplier = false;
        updateData.sentAt = null;
        updateData.status = 'pending';

        const updatedOrder = await prisma.order.update({
          where: { id: request.params.id },
          data: updateData,
          include: {
            items: true
          }
        });

        // Recalculate based on existing items
        const calculations = await calculateOrderTotals(
          updatedOrder.items.map(item => ({
            code: item.code,
            dose: item.dose,
            qty: item.qty
          })),
          existingOrder.orderNumber
        );

        // Convert Decimal strings to numbers
        const formattedOrder = {
          ...updatedOrder,
          orderNumber: updatedOrder.orderNumber,
          totalUsd: Number(updatedOrder.totalUsd),
          totalBtc: Number(updatedOrder.totalBtc),
          profitUsd: Number(updatedOrder.profitUsd),
          profitInr: Number(updatedOrder.profitInr),
          profitMargin: Number(updatedOrder.profitMargin),
          status: updatedOrder.status,
          supplierStatus: updatedOrder.sentToSupplier ? 'sent' : 'not_sent',
          shippingStatus: getShippingStatus(updatedOrder),
          trackingStatus: updatedOrder.trackingStatus,
          sentToSupplier: updatedOrder.sentToSupplier,
          sentAt: updatedOrder.sentAt,
          items: updatedOrder.items.map(item => ({
            ...item,
            id: item.id.toString(),
            buyingPrice: Number(item.buyingPrice),
            sellingPrice: Number(item.sellingPrice)
          })),
          calculations
        };

        fastify.log.info(`Order updated: ${existingOrder.orderNumber} (ID: ${request.params.id})`);
        return reply.send({ 
          order: formattedOrder
        });
      }
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({ 
        error: error instanceof Error ? error.message : 'Failed to update order' 
      });
    }
  });

  // GET /orders/:id/navigation - Get previous and next order IDs
  fastify.get<{
    Params: { id: string };
  }>('/orders/:id/navigation', async (request, reply) => {
    try {
      // Get the current order to find its placedAt date
      const currentOrder = await prisma.order.findUnique({
        where: { id: request.params.id },
        select: { placedAt: true }
      });

      if (!currentOrder) {
        return reply.status(404).send({ 
          error: 'Order not found' 
        });
      }

      // Get the previous order (newer - with placedAt greater than current)
      const previousOrder = await prisma.order.findFirst({
        where: {
          placedAt: {
            gt: currentOrder.placedAt
          }
        },
        orderBy: {
          placedAt: 'asc' // Get the closest newer order
        },
        select: { id: true }
      });

      // Get the next order (older - with placedAt less than current)
      const nextOrder = await prisma.order.findFirst({
        where: {
          placedAt: {
            lt: currentOrder.placedAt
          }
        },
        orderBy: {
          placedAt: 'desc' // Get the closest older order
        },
        select: { id: true }
      });

      return reply.send({
        prevId: previousOrder?.id || null,
        nextId: nextOrder?.id || null
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({ 
        error: 'Failed to get order navigation' 
      });
    }
  });

  // DELETE /orders/:id - Delete single order
  fastify.delete<{
    Params: { id: string };
  }>('/orders/:id', async (request, reply) => {
    try {
      // Check if order exists
      const order = await prisma.order.findUnique({
        where: { id: request.params.id }
      });

      if (!order) {
        return reply.status(404).send({ 
          error: 'Order not found' 
        });
      }

      // Delete order (will cascade delete order items)
      await prisma.order.delete({
        where: { id: request.params.id }
      });

      fastify.log.info(`Order deleted: ${request.params.id}`);
      return reply.send({ 
        success: true,
        message: 'Order successfully deleted'
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({ 
        error: 'Failed to delete order' 
      });
    }
  });

  // DELETE /orders/bulk - Delete multiple orders
  fastify.delete<{
    Body: { orderIds: string[] };
  }>('/orders/bulk', async (request, reply) => {
    try {
      const { orderIds } = request.body;

      if (!orderIds || !Array.isArray(orderIds) || orderIds.length === 0) {
        return reply.status(400).send({ 
          error: 'Order IDs are required' 
        });
      }

      // Delete orders in a transaction
      const result = await prisma.$transaction(async (tx) => {
        // First verify all orders exist
        const existingOrders = await tx.order.findMany({
          where: { id: { in: orderIds } },
          select: { id: true }
        });

        const existingIds = existingOrders.map(o => o.id);
        const missingIds = orderIds.filter(id => !existingIds.includes(id));

        if (missingIds.length > 0) {
          throw new Error(`Orders not found: ${missingIds.join(', ')}`);
        }

        // Delete all orders
        const deleteResult = await tx.order.deleteMany({
          where: { id: { in: orderIds } }
        });

        return deleteResult;
      });

      fastify.log.info(`Bulk deleted ${result.count} orders`);
      return reply.send({ 
        success: true,
        message: `Successfully deleted ${result.count} orders`,
        deletedCount: result.count
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({ 
        error: error instanceof Error ? error.message : 'Failed to delete orders' 
      });
    }
  });

  // POST /orders/bulk-tracking-status - Get tracking status for multiple orders
  fastify.post<{
    Body: { orderIds: string[] };
  }>('/orders/bulk-tracking-status', async (request, reply) => {
    try {
      const { orderIds } = request.body;

      if (!orderIds || !Array.isArray(orderIds) || orderIds.length === 0) {
        return reply.status(400).send({ 
          error: 'Order IDs are required' 
        });
      }

      // Fetch orders with tracking numbers
      const orders = await prisma.order.findMany({
        where: { 
          id: { in: orderIds },
          tracking17: { not: null }
        },
        select: { 
          id: true,
          tracking17: true,
          trackingStatus: true,
          country: true 
        }
      });

      if (orders.length === 0) {
        return reply.send({ 
          trackingStatuses: {}
        });
      }

      // Prepare tracking requests
      const trackingRequests = orders
        .filter(order => order.trackingStatus !== 'Delivered') // Skip delivered orders
        .map(order => ({
          trackingNumber: order.tracking17!,
          destinationCountry: order.country || 'United States'
        }));

      // Use bulk method for optimized caching and batch processing
      const bulkResults = await parcelsAppService.bulkGetTrackingStatuses(trackingRequests);

      // Build results and update orders
      const trackingStatuses: Record<string, any> = {};
      const updatePromises: Promise<any>[] = [];

      for (const order of orders) {
        if (!order.tracking17) continue;

        // Handle already delivered orders
        if (order.trackingStatus === 'Delivered') {
          trackingStatuses[order.id] = {
            trackingNumber: order.tracking17,
            status: 'Delivered',
            statusCode: '40',
            lastUpdate: new Date().toISOString(),
            trackingUrl: parcelsAppService.generateTrackingUrl(order.tracking17),
            cached: true
          };
          continue;
        }

        // Get status from bulk results
        const status = bulkResults.get(order.tracking17);
        
        if (!status) {
          fastify.log.warn(`No tracking status found for order ${order.id}`);
          continue;
        }

        // Update order status based on tracking status
        let orderStatus: 'shipped' | 'delivered' | 'in_transit' = 'shipped';
        if (status.statusText === 'Delivered') {
          orderStatus = 'delivered';
        } else if (['In Transit', 'Pick Up', 'Out for Delivery'].includes(status.statusText)) {
          orderStatus = 'in_transit';
        }

        // Queue order update
        updatePromises.push(
          prisma.order.update({
            where: { id: order.id },
            data: {
              trackingStatus: status.statusText,
              status: orderStatus
            }
          }).catch(error => {
            fastify.log.error(`Failed to update order ${order.id}: ${error}`);
          })
        );

        // Add to response
        trackingStatuses[order.id] = {
          trackingNumber: status.trackingNumber,
          status: status.statusText,
          statusCode: status.status,
          lastUpdate: status.lastUpdate,
          lastEventDescription: status.lastEventDescription,
          trackingUrl: parcelsAppService.generateTrackingUrl(order.tracking17)
        };
      }

      // Execute all order updates in parallel
      await Promise.all(updatePromises);

      return reply.send({ 
        trackingStatuses
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({ 
        error: 'Failed to get bulk tracking status' 
      });
    }
  });

  // GET /orders/tracking-cache-stats - Get tracking cache statistics
  fastify.get('/orders/tracking-cache-stats', async (request, reply) => {
    try {
      const stats = await parcelsAppService.getCacheStats();
      return reply.send(stats);
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({ 
        error: 'Failed to get cache statistics' 
      });
    }
  });
};

export default ordersRoute;