# Server Configuration
PORT=3000
NODE_ENV=development  # or 'production'

# Database Configuration
# CRITICAL: Use different database names for dev and prod!
# Dev: peptide_portal_dev
# Prod: peptide_portal_prod
DATABASE_URL=postgresql://user:password@localhost:5432/peptide_portal_dev

# Email Configuration (Gmail SMTP)
# To get app password: Go to Google Account Settings > Security > 2-Step Verification > App passwords
GMAIL_USER=<EMAIL>
GOOGLE_APP_PASSWORD=your-16-character-app-password

# External APIs
COINGECKO_URL=https://api.coingecko.com/api/v3

# API Keys
GOOGLE_API_KEY=your-google-api-key
# SEVENTEEN_TRACK_API_KEY=your-17track-key (deprecated - use Parcels API instead)
PARCELS_APP_API_KEY=your-parcels-app-api-key