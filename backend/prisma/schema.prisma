generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Order {
  id              String         @id @default(uuid())
  orderNumber     String         @unique @map("order_number") @db.VarChar(20)
  placedAt        DateTime       @default(now()) @map("placed_at_gz")
  customerName    String         @map("customer_name") @db.VarChar(100)
  email           String?        @db.VarChar(255)
  street1         String         @db.VarChar(100)
  street2         String?        @db.VarChar(100)
  city            String         @db.VarChar(50)
  state           String?        @db.VarChar(50)
  postalCode      String         @map("postal_code") @db.VarChar(20)
  country         String         @default("United States") @db.VarChar(50)
  totalUsd        Decimal        @map("total_usd") @db.Decimal(10, 2)
  totalBtc        Decimal        @map("total_btc") @db.Decimal(10, 8)
  profitUsd       Decimal        @map("profit_usd") @db.Decimal(10, 2)
  profitInr       Decimal        @map("profit_inr") @db.Decimal(10, 2)
  profitMargin    Decimal        @map("profit_margin") @db.Decimal(5, 2)
  paymentMethod   String         @map("payment_method") @db.VarChar(10)
  paymentUrl      String?        @map("payment_url") @db.Text
  tracking17      String?        @map("tracking_17") @db.VarChar(255)
  trackingStatus  String?        @map("tracking_status") @db.VarChar(50)
  sentToSupplier  Boolean        @default(false) @map("sent_to_supplier")
  sentAt          DateTime?      @map("sent_at")
  status          OrderStatus    @default(pending)
  isEdited        Boolean        @default(false) @map("is_edited")
  editedAt        DateTime?      @map("edited_at")
  items           OrderItem[]
  history         OrderHistory[]

  @@map("orders")
}

model OrderItem {
  id           BigInt          @id @default(autoincrement())
  orderId      String          @map("order_id")
  code         String          @db.VarChar(10)
  dose         String          @db.VarChar(20)
  qty          Int             @db.SmallInt
  buyingPrice  Decimal         @map("buying_price") @db.Decimal(8, 2)
  sellingPrice Decimal         @map("selling_price") @db.Decimal(8, 2)
  variantId    String?         @map("variant_id")
  order        Order           @relation(fields: [orderId], references: [id], onDelete: Cascade)
  variant      ProductVariant? @relation(fields: [variantId], references: [id])

  @@map("order_items")
}

model Pricing {
  code         String   @id @db.VarChar(10)
  product      String   @db.VarChar(20)
  dose         String   @db.VarChar(20)
  buyingPrice  Decimal  @map("buying_price") @db.Decimal(8, 2)
  sellingPrice Decimal  @map("selling_price") @db.Decimal(8, 2)
  updatedAt    DateTime @default(now()) @updatedAt @map("updated_at")

  @@map("pricing")
}

model ExchangeRate {
  pair      String   @id @db.VarChar(20)
  rate      Decimal  @db.Decimal(18, 6)
  fetchedAt DateTime @default(now()) @map("fetched_at")

  @@map("exchange_rates")
}

model Product {
  id        String           @id @default(uuid())
  name      String           @unique @db.VarChar(50)
  createdAt DateTime         @default(now()) @map("created_at")
  updatedAt DateTime         @updatedAt @map("updated_at")
  variants  ProductVariant[]

  @@map("products")
}

model ProductVariant {
  id           String      @id @default(uuid())
  productId    String      @map("product_id")
  dose         String      @db.VarChar(20)
  code         String      @unique @db.VarChar(10)
  buyingPrice  Decimal     @map("buying_price") @db.Decimal(8, 2)
  sellingPrice Decimal     @map("selling_price") @db.Decimal(8, 2)
  sortOrder    Int         @default(0) @map("sort_order")
  createdAt    DateTime    @default(now()) @map("created_at")
  updatedAt    DateTime    @updatedAt @map("updated_at")
  orderItems   OrderItem[]
  product      Product     @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@index([productId])
  @@map("product_variants")
}

model OrderHistory {
  id            String         @id @default(uuid())
  orderId       String         @map("order_id")
  editedAt      DateTime       @default(now()) @map("edited_at")
  changeType    String         @map("change_type") @db.VarChar(20) // 'added', 'removed', 'modified'
  itemCode      String         @map("item_code") @db.VarChar(10)
  itemDose      String         @map("item_dose") @db.VarChar(20)
  oldQty        Int?           @map("old_qty") @db.SmallInt
  newQty        Int?           @map("new_qty") @db.SmallInt
  oldPrice      Decimal?       @map("old_price") @db.Decimal(8, 2)
  newPrice      Decimal?       @map("new_price") @db.Decimal(8, 2)
  order         Order          @relation(fields: [orderId], references: [id], onDelete: Cascade)
  
  @@map("order_history")
}

model TrackingCache {
  trackingNumber       String   @map("tracking_number") @db.VarChar(255)
  destinationCountry   String   @map("destination_country") @default("United States") @db.VarChar(50)
  statusCode           String   @map("status_code") @db.VarChar(2)
  statusText           String   @map("status_text") @db.VarChar(50)
  lastUpdate           DateTime? @map("last_update")
  lastEventDescription String?  @map("last_event_description") @db.Text
  uuid                 String?  @db.VarChar(255)
  cachedAt            DateTime @default(now()) @map("cached_at")
  expiresAt           DateTime @map("expires_at")
  isFinalStatus       Boolean  @default(false) @map("is_final_status")

  @@id([trackingNumber, destinationCountry])
  @@index([expiresAt])
  @@index([isFinalStatus])
  @@map("tracking_cache")
}

model TrackingMetric {
  id         String   @id @default(uuid())
  metricType String   @map("metric_type") @db.VarChar(50)
  value      Decimal  @db.Decimal(10, 2)
  metadata   Json?
  createdAt  DateTime @default(now()) @map("created_at")

  @@index([createdAt])
  @@index([metricType])
  @@map("tracking_metrics")
}

enum OrderStatus {
  pending
  sent
  shipped
  in_transit
  delivered
}
