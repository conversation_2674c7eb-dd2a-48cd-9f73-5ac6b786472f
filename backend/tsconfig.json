{"extends": "@tsconfig/node20/tsconfig.json", "compilerOptions": {"outDir": "./dist", "module": "ES2022", "target": "ES2022", "moduleResolution": "node", "strict": false, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "noUnusedLocals": false, "noUnusedParameters": false, "noImplicitReturns": false, "noFallthroughCasesInSwitch": false, "noImplicitAny": false, "allowJs": true}, "include": ["src/**/*", "../shared/**/*"], "exclude": ["node_modules", "dist"]}