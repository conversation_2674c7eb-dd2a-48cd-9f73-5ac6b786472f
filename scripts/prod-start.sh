#!/bin/bash

# Production environment startup script
# This script starts both frontend and backend in production mode with PM2

echo "🔴 Starting production environment..."

# Check if PM2 is installed globally, install if not
if ! command -v pm2 &> /dev/null; then
    echo "📦 Installing PM2 globally..."
    npm install -g pm2
    echo "✅ PM2 installed successfully!"
fi

# Stop any existing production processes
echo "🧹 Cleaning up existing production processes..."
pm2 stop backend-prod frontend-prod 2>/dev/null || true
pm2 delete backend-prod frontend-prod 2>/dev/null || true

# Start production backend
echo "🔧 Starting production backend (API)..."
pm2 start "npm run prod:api" --name backend-prod

# Start production frontend
echo "🎨 Starting production frontend (UI)..."
pm2 start "npm run prod:ui" --name frontend-prod

# Show status
echo "✅ Production environment started!"
echo ""
echo "📊 Process Status:"
pm2 list

echo ""
echo "🌐 URLs:"
echo "  Frontend: http://localhost:5174"
echo "  Backend:  http://localhost:3001"
echo ""
echo "📝 Useful commands:"
echo "  pm2 logs backend-prod   # Backend logs only"
echo "  pm2 logs frontend-prod  # Frontend logs only"
echo "  pm2 stop backend-prod frontend-prod  # Stop production servers"
echo "  pm2 restart backend-prod frontend-prod  # Restart production servers"
echo ""
echo "⚠️  PRODUCTION MODE ACTIVE - BE CAREFUL!"
echo "🗄️  Using production database: peptide_portal_prod"
echo ""
echo "📊 Environment Status:"
./scripts/env-status.sh
