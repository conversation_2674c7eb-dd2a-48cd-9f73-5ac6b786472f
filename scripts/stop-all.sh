#!/bin/bash

# Stop all servers script
# This script stops both development and production processes

echo "🛑 Stopping all servers (development and production)..."

# Check if PM2 is available
if ! command -v pm2 &> /dev/null; then
    echo "⚠️  PM2 not found. No processes to stop."
    exit 1
fi

# Stop all development and production processes
echo "🧹 Stopping all development and production servers..."
pm2 stop backend frontend backend-prod frontend-prod 2>/dev/null || true

# Show final status
echo "✅ All servers stopped!"
echo ""
echo "📊 Remaining processes:"
pm2 list

echo ""
echo "💡 All development and production servers have been stopped."
echo "🔄 To restart development: npm run dev"
echo "🔄 To restart production: npm run prod"
