#!/bin/bash

# Production environment shutdown script
# This script stops all production processes

echo "🛑 Stopping production environment..."

# Check if PM2 is available
if ! command -v pm2 &> /dev/null; then
    echo "⚠️  PM2 not found. No production processes to stop."
    exit 1
fi

# Stop production processes
echo "🔴 Stopping production servers..."
pm2 stop backend-prod frontend-prod 2>/dev/null || true

# Show final status
echo "✅ Production environment stopped!"
echo ""
echo "📊 Remaining processes:"
pm2 list

echo ""
echo "💡 Production servers have been stopped safely."
echo "🔄 To restart: npm run prod"
