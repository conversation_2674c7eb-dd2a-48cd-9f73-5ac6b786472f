#!/bin/bash

# Environment Status Display Script
# Shows current environment with color coding and process status

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Get current git branch
CURRENT_BRANCH=$(git branch --show-current 2>/dev/null || echo "unknown")

# Check PM2 processes
DEV_BACKEND_STATUS="stopped"
DEV_FRONTEND_STATUS="stopped"
PROD_BACKEND_STATUS="stopped"
PROD_FRONTEND_STATUS="stopped"

if command -v pm2 &> /dev/null; then
    # Check development processes
    if pm2 list | grep -q "backend.*online"; then
        DEV_BACKEND_STATUS="running"
    fi
    if pm2 list | grep -q "frontend.*online"; then
        DEV_FRONTEND_STATUS="running"
    fi

    # Check production processes
    if pm2 list | grep -q "backend-prod.*online"; then
        PROD_BACKEND_STATUS="running"
    fi
    if pm2 list | grep -q "frontend-prod.*online"; then
        PROD_FRONTEND_STATUS="running"
    fi
fi

# Determine active environment based on running processes
ACTIVE_ENV="none"
if [ "$DEV_BACKEND_STATUS" = "running" ] || [ "$DEV_FRONTEND_STATUS" = "running" ]; then
    ACTIVE_ENV="development"
fi
if [ "$PROD_BACKEND_STATUS" = "running" ] || [ "$PROD_FRONTEND_STATUS" = "running" ]; then
    if [ "$ACTIVE_ENV" = "development" ]; then
        ACTIVE_ENV="mixed"
    else
        ACTIVE_ENV="production"
    fi
fi

# Display header with prominent environment indicator
echo ""
case $ACTIVE_ENV in
    "development")
        echo -e "🔧 ${GREEN}DEVELOPMENT ENVIRONMENT ACTIVE${NC}"
        ;;
    "production")
        echo -e "🔧 ${RED}PRODUCTION ENVIRONMENT ACTIVE${NC}"
        ;;
    "mixed")
        echo -e "🔧 ${YELLOW}MIXED ENVIRONMENTS RUNNING${NC}"
        ;;
    *)
        echo -e "🔧 ${YELLOW}NO ENVIRONMENT ACTIVE${NC}"
        ;;
esac
echo "========================================"

# Git branch indicator
echo -e "Git Branch:  ${BLUE}${CURRENT_BRANCH}${NC}"
echo ""

# Development servers status
echo -e "${GREEN}Development Servers:${NC}"
if [ "$DEV_BACKEND_STATUS" = "running" ]; then
    echo -e "  Backend:   ${GREEN}●${NC} ${GREEN}RUNNING${NC} (port 3000)"
else
    echo -e "  Backend:   ${YELLOW}●${NC} ${YELLOW}STOPPED${NC}"
fi

if [ "$DEV_FRONTEND_STATUS" = "running" ]; then
    echo -e "  Frontend:  ${GREEN}●${NC} ${GREEN}RUNNING${NC} (port 5173)"
else
    echo -e "  Frontend:  ${YELLOW}●${NC} ${YELLOW}STOPPED${NC}"
fi

echo ""

# Production servers status
echo -e "${RED}Production Servers:${NC}"
if [ "$PROD_BACKEND_STATUS" = "running" ]; then
    echo -e "  Backend:   ${RED}●${NC} ${RED}RUNNING${NC} (port 3001)"
else
    echo -e "  Backend:   ${YELLOW}●${NC} ${YELLOW}STOPPED${NC}"
fi

if [ "$PROD_FRONTEND_STATUS" = "running" ]; then
    echo -e "  Frontend:  ${RED}●${NC} ${RED}RUNNING${NC} (port 5174)"
else
    echo -e "  Frontend:  ${YELLOW}●${NC} ${YELLOW}STOPPED${NC}"
fi

echo ""

# URLs section
if [ "$DEV_BACKEND_STATUS" = "running" ] || [ "$DEV_FRONTEND_STATUS" = "running" ]; then
    echo -e "${GREEN}Development URLs:${NC}"
    [ "$DEV_FRONTEND_STATUS" = "running" ] && echo "  Frontend: http://localhost:5173"
    [ "$DEV_BACKEND_STATUS" = "running" ] && echo "  Backend:  http://localhost:3000"
    echo ""
fi

if [ "$PROD_BACKEND_STATUS" = "running" ] || [ "$PROD_FRONTEND_STATUS" = "running" ]; then
    echo -e "${RED}Production URLs:${NC}"
    [ "$PROD_FRONTEND_STATUS" = "running" ] && echo "  Frontend: http://localhost:5174"
    [ "$PROD_BACKEND_STATUS" = "running" ] && echo "  Backend:  http://localhost:3001"
    echo ""
fi

# Quick commands
echo -e "${PURPLE}💡 Quick Commands:${NC}"
echo "   npm run dev          # Start development servers"
echo "   npm run prod         # Start production servers"
echo "   npm run dev:stop     # Stop development servers"
echo "   npm run prod:stop    # Stop production servers"
echo "   npm run status       # Show this status"
echo ""
