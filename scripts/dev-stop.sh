#!/bin/bash

# Development environment shutdown script
# This script stops all development processes

echo "🛑 Stopping development environment..."

# Check if PM2 is available
if ! command -v pm2 &> /dev/null; then
    echo "⚠️  PM2 not found. Run 'npm run dev' first to install PM2."
    exit 1
fi

# Stop development processes
pm2 stop backend frontend 2>/dev/null || true

# Show final status
echo "✅ Development environment stopped!"
echo ""
echo "📊 Remaining processes:"
pm2 list
