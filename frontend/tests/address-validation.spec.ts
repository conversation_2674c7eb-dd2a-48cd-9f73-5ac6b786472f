import { test, expect, Page } from '@playwright/test';

test.describe('Address Validation Workflow', () => {
  let page: Page;

  test.beforeEach(async ({ page: testPage }) => {
    page = testPage;
    // Clear localStorage before each test
    await page.goto('/');
    await page.evaluate(() => localStorage.clear());
  });

  test('Settings toggle controls address validation', async () => {
    // Navigate to Settings page
    await page.goto('/settings');
    
    // Find the address validation toggle
    const toggle = page.locator('button[role="switch"]');
    await expect(toggle).toBeVisible();
    
    // Initially, validation should be enabled (toggle off)
    await expect(toggle).toHaveAttribute('aria-checked', 'false');
    
    // Take screenshot of initial state
    await page.screenshot({ path: 'screenshots/settings-validation-enabled.png' });
    
    // Click to disable validation
    await toggle.click();
    
    // Should show success message
    await expect(page.locator('text=Address validation disabled')).toBeVisible();
    
    // Toggle should now be checked (disabled state)
    await expect(toggle).toHaveAttribute('aria-checked', 'true');
    
    // Take screenshot of disabled state
    await page.screenshot({ path: 'screenshots/settings-validation-disabled.png' });
    
    // Verify localStorage was updated
    const skipValidation = await page.evaluate(() => 
      localStorage.getItem('skipAddressValidation')
    );
    expect(skipValidation).toBe('true');
    
    // Click again to re-enable
    await toggle.click();
    await expect(page.locator('text=Address validation enabled')).toBeVisible();
    await expect(toggle).toHaveAttribute('aria-checked', 'false');
  });

  test('Order form with address validation enabled', async () => {
    // Ensure validation is enabled
    await page.goto('/settings');
    const toggle = page.locator('button[role="switch"]');
    const isDisabled = await toggle.getAttribute('aria-checked') === 'true';
    if (isDisabled) {
      await toggle.click();
      await expect(page.locator('text=Address validation enabled')).toBeVisible();
    }
    
    // Set up network interception to capture API calls
    const validationRequests: any[] = [];
    page.on('request', request => {
      if (request.url().includes('/api/address/validate')) {
        validationRequests.push({
          url: request.url(),
          method: request.method(),
          postData: request.postData()
        });
      }
    });
    
    // Navigate to order form
    await page.goto('/order');
    
    // Fill out the order form
    await page.fill('#customerName', 'John Doe');
    await page.fill('#street1', '1600 Amphitheatre Parkway');
    await page.fill('#city', 'Mountain View');
    await page.selectOption('#state', 'CA');
    await page.fill('#postalCode', '94043');
    await page.selectOption('#country', 'United States');
    
    // Add a peptide (first select product, then dose)
    await page.selectOption('select:has-text("Select product")', 'Semaglutide');
    await page.selectOption('select:has-text("Select dose")', '1mg');
    
    // Take screenshot before submitting
    await page.screenshot({ path: 'screenshots/order-form-filled.png' });
    
    // Submit the form
    await page.click('button[type="submit"]');
    
    // Wait for validation to complete
    await page.waitForTimeout(2000);
    
    // Should have made an API call for validation
    expect(validationRequests.length).toBeGreaterThan(0);
    
    // Take screenshot of validation result
    await page.screenshot({ path: 'screenshots/order-form-validation-result.png' });
    
    console.log('Address validation API calls made:', validationRequests.length);
    if (validationRequests.length > 0) {
      console.log('First validation request:', validationRequests[0]);
    }
  });

  test('Order form with address validation disabled', async () => {
    // Disable validation first
    await page.goto('/settings');
    const toggle = page.locator('button[role="switch"]');
    const isEnabled = await toggle.getAttribute('aria-checked') === 'false';
    if (isEnabled) {
      await toggle.click();
      await expect(page.locator('text=Address validation disabled')).toBeVisible();
    }
    
    // Set up network interception
    const validationRequests: any[] = [];
    page.on('request', request => {
      if (request.url().includes('/api/address/validate')) {
        validationRequests.push({
          url: request.url(),
          method: request.method(),
          postData: request.postData()
        });
      }
    });
    
    // Navigate to order form
    await page.goto('/order');
    
    // Fill out the order form with same data
    await page.fill('#customerName', 'John Doe');
    await page.fill('#street1', '1600 Amphitheatre Parkway');
    await page.fill('#city', 'Mountain View');
    await page.selectOption('#state', 'CA');
    await page.fill('#postalCode', '94043');
    await page.selectOption('#country', 'United States');
    
    // Add a peptide (first select product, then dose)
    await page.selectOption('select:has-text("Select product")', 'Semaglutide');
    await page.selectOption('select:has-text("Select dose")', '1mg');
    
    // Take screenshot before submitting
    await page.screenshot({ path: 'screenshots/order-form-validation-disabled.png' });
    
    // Submit the form
    await page.click('button[type="submit"]');
    
    // Wait a bit to ensure no API calls are made
    await page.waitForTimeout(2000);
    
    // Should NOT have made validation API calls
    expect(validationRequests.length).toBe(0);
    
    // Take screenshot of result
    await page.screenshot({ path: 'screenshots/order-form-no-validation.png' });
    
    console.log('Address validation API calls made (should be 0):', validationRequests.length);
  });

  test('Address validation API error handling', async () => {
    // Enable validation
    await page.goto('/settings');
    const toggle = page.locator('button[role="switch"]');
    const isDisabled = await toggle.getAttribute('aria-checked') === 'true';
    if (isDisabled) {
      await toggle.click();
    }
    
    // Mock API to return error
    await page.route('**/api/address/validate', route => {
      route.fulfill({
        status: 503,
        contentType: 'application/json',
        body: JSON.stringify({
          error: 'Address Validation API not enabled',
          message: 'The Google Address Validation API is not enabled for this project.'
        })
      });
    });
    
    // Navigate to order form and fill it
    await page.goto('/order');
    await page.fill('#customerName', 'John Doe');
    await page.fill('#street1', '123 Test Street');
    await page.fill('#city', 'Test City');
    await page.selectOption('#state', 'CA');
    await page.fill('#postalCode', '12345');
    await page.selectOption('#country', 'United States');
    
    await page.selectOption('select:has-text("Select product")', 'Semaglutide');
    await page.selectOption('select:has-text("Select dose")', '1mg');
    
    // Submit form
    await page.click('button[type="submit"]');
    
    // Should show error dialog
    await expect(page.locator('text=Address validation is currently unavailable')).toBeVisible();
    
    // Take screenshot of error state
    await page.screenshot({ path: 'screenshots/order-form-validation-error.png' });
    
    // Should have option to proceed without validation
    await expect(page.locator('text=Proceed without validation')).toBeVisible();
  });

  test('Address validation workflow demonstration', async () => {
    test.setTimeout(120000); // 2 minutes timeout
    
    // Step 1: Show Settings page
    await page.goto('/settings');
    await page.screenshot({ path: 'screenshots/demo-1-settings.png' });
    
    const toggle = page.locator('button[role="switch"]');
    const isEnabled = await toggle.getAttribute('aria-checked') === 'false';
    console.log(`   - Address validation is currently: ${isEnabled ? 'ENABLED' : 'DISABLED'}`);
    
    // Step 2: Enable validation if disabled
    if (!isEnabled) {
      console.log('   - Enabling address validation...');
      await toggle.click();
      await expect(page.locator('text=Address validation enabled')).toBeVisible();
      await page.screenshot({ path: 'screenshots/demo-2-enabled.png' });
    }
    
    // Step 3: Go to order form
    console.log('\\n2. Order Form with Validation Enabled:');
    await page.goto('/order');
    await page.screenshot({ path: 'screenshots/demo-3-order-form.png' });
    
    // Step 4: Fill form and track API calls
    console.log('   - Filling order form...');
    const apiCalls: any[] = [];
    page.on('request', request => {
      if (request.url().includes('/api/address/validate')) {
        apiCalls.push({
          url: request.url(),
          method: request.method(),
          timestamp: new Date().toISOString()
        });
      }
    });
    
    await page.fill('#customerName', 'Test Customer');
    await page.fill('#street1', '1600 Amphitheatre Parkway');
    await page.fill('#city', 'Mountain View');
    await page.selectOption('#state', 'CA');
    await page.fill('#postalCode', '94043');
    await page.selectOption('#country', 'United States');
    await page.selectOption('select:has-text("Select product")', 'Semaglutide');
    await page.selectOption('select:has-text("Select dose")', '1mg');
    
    await page.screenshot({ path: 'screenshots/demo-4-form-filled.png' });
    
    // Step 5: Submit and observe validation
    console.log('   - Submitting form (this will trigger address validation)...');
    await page.click('button[type="submit"]');
    await page.waitForTimeout(3000);
    
    console.log(`   - API calls made: ${apiCalls.length}`);
    apiCalls.forEach((call, index) => {
      console.log(`     ${index + 1}. ${call.method} ${call.url} at ${call.timestamp}`);
    });
    
    await page.screenshot({ path: 'screenshots/demo-5-validation-result.png' });
    
    // Step 6: Now disable validation
    console.log('\\n3. Disabling Address Validation:');
    await page.goto('/settings');
    await toggle.click();
    await expect(page.locator('text=Address validation disabled')).toBeVisible();
    await page.screenshot({ path: 'screenshots/demo-6-disabled.png' });
    
    // Step 7: Test with validation disabled
    console.log('\\n4. Order Form with Validation Disabled:');
    const disabledApiCalls: any[] = [];
    page.on('request', request => {
      if (request.url().includes('/api/address/validate')) {
        disabledApiCalls.push({
          url: request.url(),
          method: request.method(),
          timestamp: new Date().toISOString()
        });
      }
    });
    
    await page.goto('/order');
    await page.fill('#customerName', 'Test Customer 2');
    await page.fill('#street1', '123 Any Street');
    await page.fill('#city', 'Any City');
    await page.selectOption('#state', 'NY');
    await page.fill('#postalCode', '10001');
    await page.selectOption('#country', 'United States');
    await page.selectOption('select:has-text("Select product")', 'Semaglutide');
    await page.selectOption('select:has-text("Select dose")', '1mg');
    
    await page.screenshot({ path: 'screenshots/demo-7-disabled-form.png' });
    
    console.log('   - Submitting form (NO validation should occur)...');
    await page.click('button[type="submit"]');
    await page.waitForTimeout(3000);
    
    console.log(`   - API calls made: ${disabledApiCalls.length} (should be 0)`);
    await page.screenshot({ path: 'screenshots/demo-8-no-validation.png' });
    
    console.log('\\n=== Demonstration Complete ===');
    console.log('Screenshots saved to screenshots/ directory');
    console.log('\\nKey Findings:');
    console.log(`- With validation ENABLED: ${apiCalls.length} API calls made`);
    console.log(`- With validation DISABLED: ${disabledApiCalls.length} API calls made`);
    console.log('- Settings toggle successfully controls validation behavior');
  });
});