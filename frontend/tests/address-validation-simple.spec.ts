import { test, expect, Page } from '@playwright/test';

test.describe('Address Validation Demo', () => {
  let page: Page;

  test.beforeEach(async ({ page: testPage }) => {
    page = testPage;
    await page.goto('/');
    await page.evaluate(() => localStorage.clear());
  });

  test('Simple address validation workflow', async () => {
    // Step 1: Go to Settings and take screenshot
    await page.goto('/settings');
    await page.screenshot({ path: 'screenshots/1-settings-page.png' });
    
    // Step 2: Check if validation is enabled/disabled
    const toggle = page.locator('button[role="switch"]');
    await expect(toggle).toBeVisible();
    const isEnabled = await toggle.getAttribute('aria-checked') === 'false';
    
    // Step 3: Enable validation if disabled
    if (!isEnabled) {
      await toggle.click();
      await expect(page.locator('text=Address validation enabled')).toBeVisible();
      await page.screenshot({ path: 'screenshots/2-validation-enabled.png' });
    }
    
    // Step 4: Go to order form
    await page.goto('/order');
    await page.screenshot({ path: 'screenshots/3-order-form.png' });
    
    // Step 5: Fill form (this will trigger validation)
    await page.fill('#customerName', 'Test Customer');
    await page.fill('#street1', '1600 Amphitheatre Parkway');
    await page.fill('#city', 'Mountain View');
    await page.selectOption('#state', 'CA');
    await page.fill('#postalCode', '94043');
    await page.selectOption('#country', 'United States');
    
    // Wait for products to load
    await page.waitForTimeout(2000);
    
    // Select product and dose
    await page.selectOption('select:has-text("Select product")', 'Semaglutide');
    await page.selectOption('select:has-text("Select dose")', '1mg');
    
    await page.screenshot({ path: 'screenshots/4-form-filled.png' });
    
    // Step 6: Submit form (this triggers validation)
    await page.click('button[type="submit"]');
    await page.waitForTimeout(3000);
    
    await page.screenshot({ path: 'screenshots/5-after-submit.png' });
    
    // Step 7: Go back to settings and disable validation
    await page.goto('/settings');
    await toggle.click();
    await expect(page.locator('text=Address validation disabled')).toBeVisible();
    await page.screenshot({ path: 'screenshots/6-validation-disabled.png' });
    
    // Step 8: Test order form with validation disabled
    await page.goto('/order');
    await page.fill('#customerName', 'Test Customer 2');
    await page.fill('#street1', '123 Any Street');
    await page.fill('#city', 'Any City');
    await page.selectOption('#state', 'NY');
    await page.fill('#postalCode', '10001');
    await page.selectOption('#country', 'United States');
    
    await page.waitForTimeout(2000);
    await page.selectOption('select:has-text("Select product")', 'Semaglutide');
    await page.selectOption('select:has-text("Select dose")', '1mg');
    
    await page.screenshot({ path: 'screenshots/7-disabled-form.png' });
    
    // Submit without validation
    await page.click('button[type="submit"]');
    await page.waitForTimeout(3000);
    
    await page.screenshot({ path: 'screenshots/8-disabled-submit.png' });
  });
});