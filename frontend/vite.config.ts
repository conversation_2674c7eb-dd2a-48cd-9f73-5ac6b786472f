import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react({
    jsxRuntime: 'classic',
    jsxImportSource: 'react'
  })],
  server: {
    host: '0.0.0.0',
    port: process.env.NODE_ENV === 'production' ? 5174 : 5173,
    strictPort: true
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
    }
  }
})
