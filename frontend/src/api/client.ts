import axios from 'axios';
import type { CreateOrderRequest, UpdateOrderRequest, Order } from '../../../shared/types/order.types';

// Use external IP when accessed externally, otherwise use localhost
const getApiBaseUrl = () => {
  const hostname = window.location.hostname;
  const port = window.location.port;
  
  // If accessing via external IP, determine API port based on frontend port
  if (hostname === '***********') {
    // Development frontend (5173) -> Development backend (3000)
    // Production frontend (5174) -> Production backend (3001)
    const apiPort = port === '5174' ? '3001' : '3000';
    return `http://***********:${apiPort}/api`;
  }
  
  // If accessing via localhost, determine API port based on frontend port
  if (hostname === 'localhost') {
    // Development frontend (5173) -> Development backend (3000)
    // Production frontend (5174) -> Production backend (3001)
    const apiPort = port === '5174' ? '3001' : '3000';
    return `http://localhost:${apiPort}/api`;
  }
  
  // Default to environment variable or localhost
  return (import.meta as any).env.VITE_API_BASE_URL || 'http://localhost:3000/api';
};

const API_BASE = getApiBaseUrl();

console.log('API Base URL:', API_BASE);

export const api = axios.create({
  baseURL: API_BASE,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 30000, // 30 second timeout
});

// Add request interceptor for debugging
api.interceptors.request.use(
  (config) => {
    console.log('API Request:', config.method?.toUpperCase(), config.url);
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// Add response interceptor for debugging
api.interceptors.response.use(
  (response) => {
    console.log('API Response:', response.status, response.config.url);
    return response;
  },
  (error) => {
    console.error('API Response Error:', error.response?.status, error.config?.url, error.message);
    return Promise.reject(error);
  }
);

export interface PricingItem {
  code: string;
  product: string;
  dose: string;
  buyingPrice: number;
  sellingPrice: number;
}

export const pricingApi = {
  getAll: async (): Promise<PricingItem[]> => {
    const response = await api.get('/pricing');
    return response.data.pricing;
  },
  
  getBtcRate: async (): Promise<number> => {
    const response = await api.get('/pricing/btc-rate');
    return response.data.rate;
  },
  
  create: async (product: Omit<PricingItem, 'code'>): Promise<PricingItem> => {
    const response = await api.post('/pricing', product);
    return response.data.product;
  },
  
  update: async (code: string, product: Partial<PricingItem>): Promise<PricingItem> => {
    const response = await api.put(`/pricing/${code}`, product);
    return response.data.product;
  },
  
  delete: async (code: string): Promise<{ success: boolean; message: string }> => {
    const response = await api.delete(`/pricing/${code}`);
    return response.data;
  },
  
  bulkImport: async (data: {
    mode: 'append' | 'replace';
    products: Array<{
      product: string;
      dose: string;
      code: string;
      buyingPrice: number;
      sellingPrice: number;
    }>;
  }): Promise<{ success: boolean; products: PricingItem[]; message: string }> => {
    const response = await api.post('/pricing/bulk', data);
    return response.data;
  },
};

export const ordersApi = {
  create: async (order: CreateOrderRequest): Promise<Order> => {
    const response = await api.post('/orders', order);
    return response.data.order;
  },
  
  getAll: async (page: number = 1, limit: number = 20, search?: string): Promise<{
    orders: Order[];
    pagination: {
      currentPage: number;
      totalPages: number;
      totalCount: number;
      limit: number;
      hasNextPage: boolean;
      hasPrevPage: boolean;
    };
  }> => {
    const response = await api.get('/orders', {
      params: { page, limit, ...(search && { search }) }
    });
    return response.data;
  },
  
  getById: async (id: string): Promise<Order> => {
    const response = await api.get(`/orders/${id}`);
    return response.data.order;
  },
  
  update: async (id: string, data: UpdateOrderRequest): Promise<Order> => {
    const response = await api.put(`/orders/${id}`, data);
    return response.data.order;
  },
  
  updateTracking: async (id: string, tracking: string): Promise<Order> => {
    const response = await api.patch(`/orders/${id}/tracking`, { tracking });
    return response.data.order;
  },
  
  getTrackingStatus: async (id: string): Promise<{
    trackingNumber: string;
    status: string;
    statusCode: string;
    lastUpdate?: string;
    trackingUrl: string;
  }> => {
    const response = await api.get(`/orders/${id}/tracking-status`);
    return response.data;
  },
  
  getBulkTrackingStatus: async (orderIds: string[]): Promise<{
    trackingStatuses: Record<string, {
      trackingNumber: string;
      status: string;
      statusCode: string;
      lastUpdate?: string;
      lastEventDescription?: string;
      trackingUrl: string;
    }>;
  }> => {
    const response = await api.post('/orders/bulk-tracking-status', { orderIds });
    return response.data;
  },

  getNavigation: async (id: string): Promise<{ prevId: string | null; nextId: string | null }> => {
    const response = await api.get(`/orders/${id}/navigation`);
    return response.data;
  },
  
  sendToSupplier: async (id: string): Promise<{ success: boolean; order: Order; message: string }> => {
    const response = await api.post(`/orders/${id}/send`);
    return response.data;
  },
  
  markAsSent: async (id: string): Promise<{ success: boolean; order: Order; message: string }> => {
    const response = await api.patch(`/orders/${id}/mark-sent`);
    return response.data;
  },
  
  delete: async (id: string): Promise<{ success: boolean; message: string }> => {
    const response = await api.delete(`/orders/${id}`);
    return response.data;
  },
  
  deleteBulk: async (orderIds: string[]): Promise<{ success: boolean; message: string; deletedCount: number }> => {
    const response = await api.delete('/orders/bulk', { data: { orderIds } });
    return response.data;
  },
};

export const exportsApi = {
  downloadPdf: async (orderId: string): Promise<Blob> => {
    const response = await api.get(`/exports/${orderId}/pdf`, {
      responseType: 'blob',
    });
    return response.data;
  },
  
  downloadExcel: async (orderId: string): Promise<Blob> => {
    const response = await api.get(`/exports/${orderId}/excel`, {
      responseType: 'blob',
    });
    return response.data;
  },
  
  bulkDownloadPdf: async (orderIds: string[]): Promise<Blob> => {
    const response = await api.post('/exports/bulk/pdf', { orderIds }, {
      responseType: 'blob',
    });
    return response.data;
  },
  
  bulkDownloadExcel: async (orderIds: string[]): Promise<Blob> => {
    const response = await api.post('/exports/bulk/excel', { orderIds }, {
      responseType: 'blob',
    });
    return response.data;
  },
  
  bulkSendToSupplier: async (orderIds: string[]): Promise<{ success: boolean; orders: Order[]; message: string }> => {
    const response = await api.post('/exports/bulk/send', { orderIds });
    return response.data;
  },
};

export interface PlacePrediction {
  placeId: string;
  description: string;
  mainText: string;
  secondaryText: string;
}

export interface AddressDetails {
  street1: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
}

export const placesApi = {
  autocomplete: async (input: string): Promise<{ predictions: PlacePrediction[] }> => {
    const response = await api.get('/places/autocomplete', {
      params: { input }
    });
    return response.data;
  },
  
  getDetails: async (placeId: string): Promise<AddressDetails> => {
    const response = await api.get('/places/details', {
      params: { placeId }
    });
    return response.data;
  },
};

export interface AddressValidationRequest {
  street1: string;
  street2?: string;
  city: string;
  state?: string;
  postalCode: string;
  country: string;
}

export interface AddressValidationResponse {
  isValid: boolean;
  confidence: 'HIGH' | 'MEDIUM' | 'LOW';
  standardizedAddress?: AddressValidationRequest;
  issues?: string[];
  metadata?: {
    residential?: boolean;
    business?: boolean;
    poBox?: boolean;
  };
}

export const addressApi = {
  validate: async (address: AddressValidationRequest): Promise<AddressValidationResponse> => {
    const response = await api.post('/address/validate', address);
    return response.data;
  },
  
  getValidationStats: async (): Promise<{
    totalValidations: number;
    successfulValidations: number;
    failedValidations: number;
    averageConfidence: number;
    commonIssues: string[];
  }> => {
    const response = await api.get('/address/validation-stats');
    return response.data;
  },
};