import { api } from './client';
import type { 
  Product, 
  ProductVariant,
  CreateProductRequest,
  CreateVariantRequest,
  UpdateVariantRequest,
  ReorderVariantsRequest,
  ProductsResponse,
  ProductResponse,
  VariantResponse,
  DeleteResponse
} from '../../../shared/types/product.types';

export const productsApi = {
  // Product operations
  getAll: async (): Promise<Product[]> => {
    const response = await api.get<ProductsResponse>('/products');
    return response.data.products;
  },

  getById: async (id: string): Promise<Product> => {
    const response = await api.get<ProductResponse>(`/products/${id}`);
    return response.data.product;
  },

  create: async (data: CreateProductRequest): Promise<Product> => {
    const response = await api.post<ProductResponse>('/products', data);
    return response.data.product;
  },

  updateName: async (id: string, name: string): Promise<Product> => {
    const response = await api.put<ProductResponse>(`/products/${id}`, { name });
    return response.data.product;
  },

  delete: async (id: string): Promise<boolean> => {
    const response = await api.delete<DeleteResponse>(`/products/${id}`);
    return response.data.success;
  },

  // Variant operations
  addVariant: async (productId: string, variant: CreateVariantRequest): Promise<ProductVariant> => {
    const response = await api.post<VariantResponse>(`/products/${productId}/variants`, variant);
    return response.data.variant;
  },

  updateVariant: async (variantId: string, updates: UpdateVariantRequest): Promise<ProductVariant> => {
    const response = await api.put<VariantResponse>(`/variants/${variantId}`, updates);
    return response.data.variant;
  },

  deleteVariant: async (variantId: string): Promise<boolean> => {
    const response = await api.delete<DeleteResponse>(`/variants/${variantId}`);
    return response.data.success;
  },

  reorderVariants: async (productId: string, variantIds: string[]): Promise<boolean> => {
    const response = await api.put<DeleteResponse>(`/products/${productId}/variants/reorder`, { 
      variantIds 
    });
    return response.data.success;
  }
};