import type { DashboardMetrics, DailyRevenueProfitData, DailyOrderCountData, ShippingStatusDistribution } from '../types/dashboard';
import { api } from './client';

export type DateRange = 'today' | 'yesterday' | 'last7Days' | 'last30Days' | 'custom' | 'range' | 'lifetime';

export interface DateFilter {
  type: DateRange;
  startDate?: Date;
  endDate?: Date;
}

export async function fetchDashboardMetrics(filter?: DateFilter): Promise<DashboardMetrics> {
  try {
    let params: any = {};
    
    if (filter) {
      params.dateRange = filter.type;
      if (filter.startDate) {
        params.startDate = filter.startDate.toISOString().split('T')[0];
      }
      if (filter.endDate) {
        params.endDate = filter.endDate.toISOString().split('T')[0];
      }
    }
    
    const response = await api.get('/dashboard/metrics', { params });
    return response.data;
  } catch (error) {
    console.log('Error fetching dashboard metrics:', error);
    throw error;
  }
}

export async function fetchRevenueProfitTrend(filter?: DateFilter): Promise<DailyRevenueProfitData[]> {
  try {
    let params: any = {};
    
    if (filter) {
      params.dateRange = filter.type;
      if (filter.startDate) {
        params.startDate = filter.startDate.toISOString().split('T')[0];
      }
      if (filter.endDate) {
        params.endDate = filter.endDate.toISOString().split('T')[0];
      }
    } else {
      // Default to 7 days if no filter provided
      params.days = 7;
    }
    
    const response = await api.get('/dashboard/revenue-profit-trend', { params });
    return response.data;
  } catch (error) {
    console.log('Error fetching revenue profit trend:', error);
    throw error;
  }
}

export async function fetchDailyOrderCount(filter?: DateFilter): Promise<DailyOrderCountData[]> {
  try {
    let params: any = {};
    
    if (filter) {
      params.dateRange = filter.type;
      if (filter.startDate) {
        params.startDate = filter.startDate.toISOString().split('T')[0];
      }
      if (filter.endDate) {
        params.endDate = filter.endDate.toISOString().split('T')[0];
      }
    } else {
      // Default to 30 days if no filter provided
      params.days = 30;
    }
    
    const response = await api.get('/dashboard/daily-orders', { params });
    return response.data;
  } catch (error) {
    console.log('Error fetching daily order count:', error);
    throw error;
  }
}

export async function fetchShippingStatusDistribution(): Promise<ShippingStatusDistribution[]> {
  try {
    const response = await api.get('/dashboard/shipping-status');
    return response.data;
  } catch (error) {
    console.log('Error fetching shipping status distribution:', error);
    throw error;
  }
}