// Mock API client for demonstration without database
import type { CreateOrderRequest, Order } from '../../../shared/types/order.types';

export interface PricingItem {
  code: string;
  product: string;
  dose: string;
  buyingPrice: number;
  sellingPrice: number;
}

// Mock pricing data
const mockPricing: PricingItem[] = [
  // Tirzepatide
  { code: 'TR5', product: 'Tirzepatide', dose: '5 mg', buyingPrice: 48, sellingPrice: 89 },
  { code: 'TR10', product: 'Tirzepatide', dose: '10 mg', buyingPrice: 70, sellingPrice: 119 },
  { code: 'TR15', product: 'Tirzepatide', dose: '15 mg', buyingPrice: 90, sellingPrice: 149 },
  { code: 'TR20', product: 'Tirzepatide', dose: '20 mg', buyingPrice: 105, sellingPrice: 169 },
  { code: 'TR30', product: 'Tirzepatide', dose: '30 mg', buyingPrice: 115, sellingPrice: 189 },
  { code: 'TR40', product: 'Tirzepatide', dose: '40 mg', buyingPrice: 145, sellingPrice: 219 },
  { code: 'TR50', product: 'Tirzepatide', dose: '50 mg', buyingPrice: 190, sellingPrice: 289 },
  { code: 'TR60', product: 'Tirzepatide', dose: '60 mg', buyingPrice: 215, sellingPrice: 319 },
  
  // Retatrutide
  { code: 'RT5', product: 'Retatrutide', dose: '5 mg', buyingPrice: 70, sellingPrice: 119 },
  { code: 'RT10', product: 'Retatrutide', dose: '10 mg', buyingPrice: 105, sellingPrice: 179 },
  { code: 'RT12', product: 'Retatrutide', dose: '12 mg', buyingPrice: 130, sellingPrice: 209 },
  { code: 'RT15', product: 'Retatrutide', dose: '15 mg', buyingPrice: 140, sellingPrice: 229 },
  { code: 'RT20', product: 'Retatrutide', dose: '20 mg', buyingPrice: 180, sellingPrice: 289 },
  
  // Semaglutide
  { code: 'SM5', product: 'Semaglutide', dose: '5 mg', buyingPrice: 48, sellingPrice: 89 },
  { code: 'SM10', product: 'Semaglutide', dose: '10 mg', buyingPrice: 60, sellingPrice: 109 },
  { code: 'SM15', product: 'Semaglutide', dose: '15 mg', buyingPrice: 75, sellingPrice: 119 },
  { code: 'SM20', product: 'Semaglutide', dose: '20 mg', buyingPrice: 85, sellingPrice: 139 },
  { code: 'SM30', product: 'Semaglutide', dose: '30 mg', buyingPrice: 115, sellingPrice: 189 },
  
  // BAC Water
  { code: 'BA10', product: 'BAC Water', dose: '10 ml × 10 vials', buyingPrice: 16, sellingPrice: 49 },
];

export const pricingApi = {
  getAll: async (): Promise<PricingItem[]> => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    return mockPricing;
  },
};

export const ordersApi = {
  create: async (order: CreateOrderRequest): Promise<Order> => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Calculate totals
    let subtotal = 0;
    let totalBuyingPrice = 0;
    
    order.items.forEach(item => {
      const pricing = mockPricing.find(p => p.code === item.code && p.dose === item.dose);
      if (pricing) {
        subtotal += pricing.sellingPrice * item.qty;
        totalBuyingPrice += pricing.buyingPrice * item.qty;
      }
    });
    
    const shipping = 40;
    const discount = subtotal * 0.05;
    const totalUsd = subtotal + shipping - discount;
    const totalBtc = totalUsd / 100000; // Mock BTC rate
    const profitUsd = subtotal - totalBuyingPrice - shipping;
    const profitInr = profitUsd * 86;
    const profitMargin = subtotal > 0 ? (profitUsd / subtotal) * 100 : 0;
    
    // Return mock order
    return {
      id: 'mock-' + Date.now(),
      orderNumber: 'MOCK-' + Date.now().toString().slice(-6),
      placedAt: new Date(),
      customerName: order.customerName,
      street1: order.street1,
      street2: order.street2,
      city: order.city,
      state: order.state,
      postalCode: order.postalCode,
      country: order.country || 'United States',
      totalUsd: Number(totalUsd.toFixed(2)),
      totalBtc: Number(totalBtc.toFixed(8)),
      profitUsd: Number(profitUsd.toFixed(2)),
      profitInr: Number(profitInr.toFixed(2)),
      profitMargin: Number(profitMargin.toFixed(2)),
      paymentMethod: order.paymentMethod,
      paymentUrl: order.paymentUrl,
      tracking17: undefined,
      sentToSupplier: false,
      sentAt: undefined,
      status: 'pending' as const,
      items: order.items.map(item => ({
        ...item,
        buyingPrice: mockPricing.find(p => p.code === item.code)?.buyingPrice || 0,
        sellingPrice: mockPricing.find(p => p.code === item.code)?.sellingPrice || 0,
      })),
    };
  },
  
  getAll: async (): Promise<Order[]> => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Return mock orders for demonstration
    return [
      {
        id: 'mock-1234567890',
        orderNumber: 'MOCK-001',
        placedAt: new Date('2025-01-09T10:30:00'),
        customerName: 'John Doe',
        street1: '123 Main St',
        street2: '',
        city: 'New York',
        state: 'NY',
        postalCode: '10001',
        country: 'United States',
        totalUsd: 584.16,
        totalBtc: 0.00584160,
        profitUsd: 182.00,
        profitInr: 15652.00,
        profitMargin: 35.71,
        paymentMethod: 'BTC',
        paymentUrl: 'https://blockchain.info/tx/sample-transaction',
        tracking17: undefined,
        sentToSupplier: false,
        sentAt: undefined,
        status: 'shipped' as const,
        items: [
          {
            code: 'TR15',
            dose: '15 mg',
            qty: 2,
            buyingPrice: 90,
            sellingPrice: 149,
          },
          {
            code: 'SM10',
            dose: '10 mg',
            qty: 3,
            buyingPrice: 60,
            sellingPrice: 109,
          }
        ],
      },
      {
        id: 'mock-0987654321',
        orderNumber: 'MOCK-002',
        placedAt: new Date('2025-01-08T15:45:00'),
        customerName: 'Jane Smith',
        street1: '456 Oak Ave',
        street2: 'Apt 5B',
        city: 'Los Angeles',
        state: 'CA',
        postalCode: '90001',
        country: 'United States',
        totalUsd: 310.68,
        totalBtc: 0.00310680,
        profitUsd: 88.00,
        profitInr: 7568.00,
        profitMargin: 31.88,
        paymentMethod: 'USDT',
        paymentUrl: undefined,
        tracking17: '17TRACK123456',
        sentToSupplier: true,
        sentAt: new Date('2025-01-08T16:00:00'),
        status: 'delivered' as const,
        items: [
          {
            code: 'RT5',
            dose: '5 mg',
            qty: 1,
            buyingPrice: 70,
            sellingPrice: 119,
          },
          {
            code: 'BA10',
            dose: '10 ml × 10 vials',
            qty: 4,
            buyingPrice: 16,
            sellingPrice: 49,
          }
        ],
      }
    ];
  },
  
  getById: async (id: string): Promise<Order> => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));
    
    // Get all orders and find by ID
    const orders = await ordersApi.getAll();
    const order = orders.find(o => o.id === id);
    
    if (!order) {
      throw new Error('Order not found');
    }
    
    return order;
  },
  
  updateTracking: async (id: string, tracking: string): Promise<Order> => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Get the order and update it
    const orders = await ordersApi.getAll();
    const order = orders.find(o => o.id === id);
    
    if (!order) {
      throw new Error('Order not found');
    }
    
    // Return updated order
    return {
      ...order,
      tracking17: tracking
    };
  },
  
  sendToSupplier: async (id: string): Promise<{ success: boolean; order: Order; message: string }> => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Get the order
    const orders = await ordersApi.getAll();
    const order = orders.find(o => o.id === id);
    
    if (!order) {
      throw new Error('Order not found');
    }
    
    if (order.sentToSupplier) {
      throw new Error('Order has already been sent to supplier');
    }
    
    // Return success response
    return {
      success: true,
      order: {
        ...order,
        sentToSupplier: true,
        sentAt: new Date()
      },
      message: 'Order successfully sent to supplier'
    };
  },
};

export const exportsApi = {
  downloadPdf: async (_orderId: string): Promise<Blob> => {
    throw new Error('Not implemented');
  },
  
  downloadExcel: async (_orderId: string): Promise<Blob> => {
    throw new Error('Not implemented');
  },
};