/* React Day Picker Custom Styles */
.rdp-custom {
  --rdp-cell-size: 36px;
  --rdp-accent-color: #3b82f6;
  --rdp-background-color: #ffffff;
  --rdp-outline: 2px solid var(--rdp-accent-color);
  font-family: inherit;
}

.dark .rdp-custom {
  --rdp-background-color: #374151;
  color: #ffffff;
}

.rdp-custom .rdp-months {
  display: flex;
  flex-direction: column;
}

.rdp-custom .rdp-month {
  margin: 0;
}

.rdp-custom .rdp-caption {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem;
  margin-bottom: 0.5rem;
}

.rdp-custom .rdp-caption_label {
  font-weight: 600;
  font-size: 0.875rem;
  color: inherit;
}

.rdp-custom .rdp-nav {
  display: flex;
  gap: 0.25rem;
}

.rdp-custom .rdp-nav_button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border: none;
  border-radius: 0.375rem;
  background-color: transparent;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
}

.rdp-custom .rdp-nav_button:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.dark .rdp-custom .rdp-nav_button {
  color: #9ca3af;
}

.dark .rdp-custom .rdp-nav_button:hover {
  background-color: #4b5563;
  color: #ffffff;
}

.rdp-custom .rdp-nav_button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.rdp-custom .rdp-table {
  width: 100%;
  border-collapse: collapse;
}

.rdp-custom .rdp-head_cell {
  font-weight: 500;
  font-size: 0.75rem;
  color: #6b7280;
  text-align: center;
  padding: 0.5rem 0.25rem;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.dark .rdp-custom .rdp-head_cell {
  color: #9ca3af;
}

.rdp-custom .rdp-cell {
  text-align: center;
  padding: 0.125rem;
}

.rdp-custom .rdp-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: var(--rdp-cell-size);
  height: var(--rdp-cell-size);
  border: none;
  border-radius: 0.375rem;
  background-color: transparent;
  color: inherit;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
}

.rdp-custom .rdp-button:hover {
  background-color: #f3f4f6;
}

.dark .rdp-custom .rdp-button:hover {
  background-color: #4b5563;
}

.rdp-custom .rdp-button_reset {
  border: none;
  background: none;
  font: inherit;
  cursor: pointer;
}

.rdp-custom .rdp-day_today {
  font-weight: 600;
  color: var(--rdp-accent-color);
}

.rdp-custom .rdp-day_selected {
  background-color: var(--rdp-accent-color);
  color: white;
}

.rdp-custom .rdp-day_selected:hover {
  background-color: var(--rdp-accent-color);
  color: white;
}

.rdp-custom .rdp-day_outside {
  opacity: 0.5;
}

.rdp-custom .rdp-day_disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.rdp-custom .rdp-day_disabled:hover {
  background-color: transparent;
}

.rdp-custom .rdp-day_range_start,
.rdp-custom .rdp-day_range_end {
  background-color: var(--rdp-accent-color);
  color: white;
}

.rdp-custom .rdp-day_range_middle {
  background-color: rgba(59, 130, 246, 0.1);
  border-radius: 0;
}

.dark .rdp-custom .rdp-day_range_middle {
  background-color: rgba(59, 130, 246, 0.2);
}

.rdp-custom .rdp-day_range_start {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.rdp-custom .rdp-day_range_end {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.rdp-custom .rdp-day_range_middle:not(.rdp-day_range_start):not(.rdp-day_range_end) {
  border-radius: 0;
}