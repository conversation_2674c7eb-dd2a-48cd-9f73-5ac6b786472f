/* Mobile-specific styles for address forms */

/* Ensure touch targets are at least 44x44px for accessibility */
.touch-target {
  min-width: 44px;
  min-height: 44px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Improve select dropdowns on mobile */
@media (max-width: 640px) {
  /* Make optgroup labels more visible */
  optgroup {
    font-weight: 600;
    font-size: 0.875rem;
  }
  
  /* Improve option spacing */
  option {
    padding: 0.5rem 0;
  }
  
  /* Ensure tooltips don't go off-screen */
  .relative {
    position: relative;
  }
  
  /* Adjust tooltip positioning for mobile */
  .tooltip-mobile {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 50;
    max-width: 90vw;
  }
}

/* Improve form field focus states on mobile */
@media (hover: none) and (pointer: coarse) {
  input:focus,
  select:focus,
  textarea:focus {
    outline: 3px solid rgba(99, 102, 241, 0.5);
    outline-offset: 2px;
  }
}

/* Prevent viewport zoom on input focus for mobile devices */
@media (max-width: 768px) {
  /* Ensure all inputs in modals have at least 16px font size to prevent iOS auto-zoom */
  .fixed input[type="text"],
  .fixed input[type="number"],
  .fixed input[type="email"],
  .fixed input[type="tel"],
  .fixed input[type="password"],
  .fixed input[type="search"],
  .fixed input[type="url"],
  .fixed textarea,
  .fixed select {
    font-size: 16px !important;
  }
  
  /* Maintain visual consistency by adjusting line height */
  .fixed input.text-sm,
  .fixed textarea.text-sm,
  .fixed select.text-sm {
    line-height: 1.25;
  }
}

/* Screen reader only text */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}