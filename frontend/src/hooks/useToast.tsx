import { useState, useCallback } from 'react';

export interface ToastType {
  id: string;
  message: string;
  type: 'success' | 'error';
  duration?: number;
}

export const useToast = () => {
  const [toasts, setToasts] = useState<ToastType[]>([]);

  const showToast = useCallback((message: string, type: 'success' | 'error' = 'success', duration?: number) => {
    const id = Date.now().toString();
    const newToast: ToastType = { id, message, type, duration };
    
    setToasts(prev => [...prev, newToast]);
  }, []);

  const dismissToast = useCallback((id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  }, []);

  const showSuccess = useCallback((message: string, duration?: number) => {
    showToast(message, 'success', duration);
  }, [showToast]);

  const showError = useCallback((message: string, duration?: number) => {
    showToast(message, 'error', duration);
  }, [showToast]);

  return {
    toasts,
    showToast,
    showSuccess,
    showError,
    dismissToast
  };
};