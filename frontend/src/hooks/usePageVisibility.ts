import { useState, useEffect } from 'react';

/**
 * Custom hook to detect page visibility changes
 * Returns true when the page is visible, false when hidden
 */
export function usePageVisibility() {
  const [isVisible, setIsVisible] = useState(!document.hidden);

  useEffect(() => {
    const handleVisibilityChange = () => {
      setIsVisible(!document.hidden);
    };

    // Listen for visibility changes
    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    // Also listen for window focus/blur as fallback
    const handleFocus = () => setIsVisible(true);
    const handleBlur = () => {
      // Small delay to check if it's just a focus change within the page
      setTimeout(() => {
        if (document.hidden) {
          setIsVisible(false);
        }
      }, 100);
    };
    
    window.addEventListener('focus', handleFocus);
    window.addEventListener('blur', handleBlur);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
      window.removeEventListener('blur', handleBlur);
    };
  }, []);

  return isVisible;
}