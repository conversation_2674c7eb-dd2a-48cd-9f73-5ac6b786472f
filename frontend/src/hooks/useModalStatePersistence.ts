import { useState, useEffect, useCallback } from 'react';
import { usePageVisibility } from './usePageVisibility';

interface ModalState {
  isOpen: boolean;
  formData?: any;
  timestamp?: number;
}

const STORAGE_KEY_PREFIX = 'modal_state_';
const STATE_EXPIRY_MS = 30 * 60 * 1000; // 30 minutes

/**
 * Custom hook to persist modal state across app switches
 * Prevents modal from closing when user switches between apps
 */
export function useModalStatePersistence(modalId: string) {
  const storageKey = `${STORAGE_KEY_PREFIX}${modalId}`;
  const isPageVisible = usePageVisibility();
  
  // Load initial state from localStorage
  const loadStoredState = (): ModalState => {
    try {
      const stored = localStorage.getItem(storageKey);
      if (stored) {
        const state = JSON.parse(stored) as ModalState;
        // Check if state is not expired
        if (state.timestamp && Date.now() - state.timestamp < STATE_EXPIRY_MS) {
          return state;
        }
        // Clear expired state
        localStorage.removeItem(storageKey);
      }
    } catch (error) {
      console.error('Error loading modal state:', error);
    }
    return { isOpen: false };
  };

  const [modalState, setModalState] = useState<ModalState>(loadStoredState);
  const [shouldPreventClose, setShouldPreventClose] = useState(false);

  // Save state to localStorage
  const saveState = useCallback((state: ModalState) => {
    try {
      const stateWithTimestamp = {
        ...state,
        timestamp: Date.now()
      };
      localStorage.setItem(storageKey, JSON.stringify(stateWithTimestamp));
    } catch (error) {
      console.error('Error saving modal state:', error);
    }
  }, [storageKey]);

  // Clear stored state
  const clearStoredState = useCallback(() => {
    localStorage.removeItem(storageKey);
  }, [storageKey]);

  // Handle visibility changes
  useEffect(() => {
    if (!isPageVisible && modalState.isOpen) {
      // Page became hidden while modal is open - prevent close
      setShouldPreventClose(true);
      saveState(modalState);
    } else if (isPageVisible && shouldPreventClose) {
      // Page became visible again - allow normal closing
      setShouldPreventClose(false);
    }
  }, [isPageVisible, modalState, shouldPreventClose, saveState]);

  // Enhanced setIsOpen that respects visibility state
  const setIsOpen = useCallback((isOpen: boolean | ((prev: boolean) => boolean), formData?: any) => {
    setModalState(prev => {
      const newIsOpen = typeof isOpen === 'function' ? isOpen(prev.isOpen) : isOpen;
      const newState = {
        isOpen: newIsOpen,
        formData: formData || prev.formData,
        timestamp: Date.now()
      };
      
      if (newIsOpen) {
        saveState(newState);
      } else {
        clearStoredState();
      }
      
      return newState;
    });
  }, [saveState, clearStoredState]);

  // Enhanced close handler that respects visibility state
  const handleClose = useCallback((forceClose = false) => {
    if (forceClose || (!shouldPreventClose && isPageVisible)) {
      setIsOpen(false);
      clearStoredState();
    }
  }, [shouldPreventClose, isPageVisible, setIsOpen, clearStoredState]);

  // Update form data
  const updateFormData = useCallback((data: any) => {
    setModalState(prev => {
      const newState = {
        ...prev,
        formData: data,
        timestamp: Date.now()
      };
      if (prev.isOpen) {
        saveState(newState);
      }
      return newState;
    });
  }, [saveState]);

  return {
    isOpen: modalState.isOpen,
    formData: modalState.formData,
    setIsOpen,
    handleClose,
    updateFormData,
    shouldPreventClose
  };
}