import { useState, useEffect, useCallback } from 'react';

/**
 * Custom hook for persisting form data to localStorage
 * @param key - Unique storage key for the form data
 * @param initialData - Default values for the form
 * @param isActive - Optional flag to enable/disable persistence (e.g., when modal is closed)
 * @returns [data, setData, clearData] - Current data, setter function, and clear function
 */
export function useFormPersistence<T>(
  key: string,
  initialData: T,
  isActive: boolean = true
): [T, React.Dispatch<React.SetStateAction<T>>, () => void] {
  // Initialize state with saved data or defaults
  const [data, setData] = useState<T>(() => {
    if (!isActive) return initialData;
    
    const savedData = localStorage.getItem(key);
    if (savedData) {
      try {
        return JSON.parse(savedData);
      } catch (e) {
        console.error(`Failed to parse saved form data for key "${key}":`, e);
      }
    }
    return initialData;
  });

  // Save data to localStorage whenever it changes
  useEffect(() => {
    if (!isActive) return;
    
    // Debounce the save operation to avoid excessive writes
    const timeoutId = setTimeout(() => {
      try {
        localStorage.setItem(key, JSON.stringify(data));
      } catch (e) {
        console.error(`Failed to save form data for key "${key}":`, e);
      }
    }, 300); // 300ms debounce

    return () => clearTimeout(timeoutId);
  }, [key, data, isActive]);

  // Function to clear persisted data
  const clearData = useCallback(() => {
    localStorage.removeItem(key);
    setData(initialData);
  }, [key, initialData]);

  return [data, setData, clearData];
}

/**
 * Hook to manage multiple form fields with persistence
 */
export function useFormFieldsPersistence<T extends Record<string, any>>(
  key: string,
  initialData: T,
  isActive: boolean = true
) {
  const [formData, setFormData, clearFormData] = useFormPersistence(key, initialData, isActive);

  const updateField = useCallback((field: keyof T, value: T[keyof T]) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  }, [setFormData]);

  const updateFields = useCallback((updates: Partial<T>) => {
    setFormData(prev => ({ ...prev, ...updates }));
  }, [setFormData]);

  return {
    formData,
    setFormData,
    updateField,
    updateFields,
    clearFormData
  };
}