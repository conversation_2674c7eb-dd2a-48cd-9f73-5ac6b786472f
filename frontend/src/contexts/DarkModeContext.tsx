import React, { createContext, useContext, useState, useEffect } from 'react';

interface DarkModeContextType {
  isDarkMode: boolean;
  toggleDarkMode: () => void;
  autoDarkMode: boolean;
  darkModeStartTime: string;
  darkModeEndTime: string;
  setAutoDarkMode: (enabled: boolean) => void;
  setDarkModeStartTime: (time: string) => void;
  setDarkModeEndTime: (time: string) => void;
}

const DarkModeContext = createContext<DarkModeContextType | undefined>(undefined);

export const DarkModeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isDarkMode, setIsDarkMode] = useState(() => {
    // Check if we're in a browser environment
    if (typeof window === 'undefined') {
      return false;
    }
    
    const saved = localStorage.getItem('darkMode');
    if (saved !== null) {
      try {
        return JSON.parse(saved) as boolean;
      } catch (error) {
        console.warn('Failed to parse darkMode from localStorage:', error);
        localStorage.removeItem('darkMode');
      }
    }
    
    // Auto-detect system preference if no saved preference exists
    if (window.matchMedia) {
      return window.matchMedia('(prefers-color-scheme: dark)').matches;
    }
    
    return false;
  });

  const [autoDarkMode, setAutoDarkMode] = useState<boolean>(() => {
    if (typeof window === 'undefined') return false;
    return localStorage.getItem('autoDarkMode') === 'true';
  });

  const [darkModeStartTime, setDarkModeStartTime] = useState<string>(() => {
    if (typeof window === 'undefined') return '18:00';
    return localStorage.getItem('darkModeStartTime') || '18:00';
  });

  const [darkModeEndTime, setDarkModeEndTime] = useState<string>(() => {
    if (typeof window === 'undefined') return '06:00';
    return localStorage.getItem('darkModeEndTime') || '06:00';
  });

  const [manualOverride, setManualOverride] = useState<boolean>(() => {
    if (typeof window === 'undefined') return false;
    return localStorage.getItem('manualDarkModeOverride') === 'true';
  });

  // Check if current time is within dark mode range
  const isWithinDarkModeTime = (): boolean => {
    const now = new Date();
    const currentTime: number = now.getHours() * 60 + now.getMinutes();
    
    const [startHour, startMinute] = darkModeStartTime.split(':').map(Number);
    const [endHour, endMinute] = darkModeEndTime.split(':').map(Number);
    
    const startTime: number = startHour * 60 + startMinute;
    const endTime: number = endHour * 60 + endMinute;
    
    // Handle overnight range (e.g., 18:00 to 06:00)
    if (startTime > endTime) {
      return currentTime >= startTime || currentTime <= endTime;
    }
    
    // Normal range (e.g., 22:00 to 23:00)
    return currentTime >= startTime && currentTime <= endTime;
  };

  // Auto-switch dark mode based on time
  useEffect(() => {
    if (!autoDarkMode || manualOverride) return;
    
    const shouldBeDarkMode = isWithinDarkModeTime();
    setIsDarkMode(shouldBeDarkMode);
  }, [autoDarkMode, darkModeStartTime, darkModeEndTime, manualOverride]);

  // Check time every minute
  useEffect(() => {
    if (!autoDarkMode || manualOverride) return;
    
    const interval = setInterval(() => {
      const shouldBeDarkMode = isWithinDarkModeTime();
      setIsDarkMode(shouldBeDarkMode);
    }, 60000); // Check every minute

    return () => clearInterval(interval);
  }, [autoDarkMode, darkModeStartTime, darkModeEndTime, manualOverride]);

  // Update localStorage for auto mode settings
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('autoDarkMode', JSON.stringify(autoDarkMode));
    }
  }, [autoDarkMode]);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('darkModeStartTime', darkModeStartTime);
    }
  }, [darkModeStartTime]);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('darkModeEndTime', darkModeEndTime);
    }
  }, [darkModeEndTime]);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('manualDarkModeOverride', JSON.stringify(manualOverride));
    }
  }, [manualOverride]);

  // Initialize DOM class immediately and on state changes
  useEffect(() => {
    const root = window.document.documentElement;
    if (isDarkMode) {
      root.classList.add('dark');
    } else {
      root.classList.remove('dark');
    }
    localStorage.setItem('darkMode', JSON.stringify(isDarkMode));
  }, [isDarkMode]);

  // Ensure DOM is synchronized on mount
  useEffect(() => {
    const root = window.document.documentElement;
    if (isDarkMode) {
      root.classList.add('dark');
    } else {
      root.classList.remove('dark');
    }
  }, []);

  // Listen for system preference changes
  useEffect(() => {
    if (typeof window !== 'undefined' && window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      
      const handleChange = (e: MediaQueryListEvent) => {
        const saved = localStorage.getItem('darkMode');
        // Only update if user hasn't manually set a preference
        if (saved === null && !autoDarkMode && !manualOverride) {
          setIsDarkMode(e.matches);
        }
      };

      mediaQuery.addEventListener('change', handleChange);
      return () => mediaQuery.removeEventListener('change', handleChange);
    }
  }, [autoDarkMode, manualOverride]);

  const toggleDarkMode = () => {
    setIsDarkMode((prev: boolean) => !prev);
    setManualOverride(true);
    
    // Clear manual override after 1 hour
    setTimeout(() => {
      setManualOverride(false);
    }, 3600000);
  };

  const contextValue: DarkModeContextType = {
    isDarkMode,
    toggleDarkMode,
    autoDarkMode,
    darkModeStartTime,
    darkModeEndTime,
    setAutoDarkMode,
    setDarkModeStartTime,
    setDarkModeEndTime
  };

  return (
    <DarkModeContext.Provider value={contextValue}>
      {children}
    </DarkModeContext.Provider>
  );
};

export const useDarkMode = () => {
  const context = useContext(DarkModeContext);
  if (context === undefined) {
    throw new Error('useDarkMode must be used within a DarkModeProvider');
  }
  return context;
};