export interface CountryConfig {
  code: string;
  name: string;
  flag: string;
  postalCodeFormat: RegExp | null;
  postalCodeLabel: string;
  postalCodePlaceholder: string;
  postalCodeExample: string;
  hasStates: boolean;
  stateLabel?: string;
  states?: { code: string; name: string }[];
}

// Comprehensive country configurations
export const COUNTRY_CONFIGS: Record<string, CountryConfig> = {
  'United States': {
    code: 'US',
    name: 'United States',
    flag: '🇺🇸',
    postalCodeFormat: /^\d{5}(-\d{4})?$/,
    postalCodeLabel: 'ZIP Code',
    postalCodePlaceholder: '10001',
    postalCodeExample: 'Format: 12345 or 12345-6789',
    hasStates: true,
    stateLabel: 'State',
    states: [
      { code: 'AL', name: 'Alabama' },
      { code: 'AK', name: 'Alaska' },
      { code: 'AZ', name: 'Arizona' },
      { code: 'AR', name: 'Arkansas' },
      { code: 'CA', name: 'California' },
      { code: 'CO', name: 'Colorado' },
      { code: 'CT', name: 'Connecticut' },
      { code: 'DE', name: 'Delaware' },
      { code: 'FL', name: 'Florida' },
      { code: 'GA', name: 'Georgia' },
      { code: 'HI', name: 'Hawaii' },
      { code: 'ID', name: 'Idaho' },
      { code: 'IL', name: 'Illinois' },
      { code: 'IN', name: 'Indiana' },
      { code: 'IA', name: 'Iowa' },
      { code: 'KS', name: 'Kansas' },
      { code: 'KY', name: 'Kentucky' },
      { code: 'LA', name: 'Louisiana' },
      { code: 'ME', name: 'Maine' },
      { code: 'MD', name: 'Maryland' },
      { code: 'MA', name: 'Massachusetts' },
      { code: 'MI', name: 'Michigan' },
      { code: 'MN', name: 'Minnesota' },
      { code: 'MS', name: 'Mississippi' },
      { code: 'MO', name: 'Missouri' },
      { code: 'MT', name: 'Montana' },
      { code: 'NE', name: 'Nebraska' },
      { code: 'NV', name: 'Nevada' },
      { code: 'NH', name: 'New Hampshire' },
      { code: 'NJ', name: 'New Jersey' },
      { code: 'NM', name: 'New Mexico' },
      { code: 'NY', name: 'New York' },
      { code: 'NC', name: 'North Carolina' },
      { code: 'ND', name: 'North Dakota' },
      { code: 'OH', name: 'Ohio' },
      { code: 'OK', name: 'Oklahoma' },
      { code: 'OR', name: 'Oregon' },
      { code: 'PA', name: 'Pennsylvania' },
      { code: 'RI', name: 'Rhode Island' },
      { code: 'SC', name: 'South Carolina' },
      { code: 'SD', name: 'South Dakota' },
      { code: 'TN', name: 'Tennessee' },
      { code: 'TX', name: 'Texas' },
      { code: 'UT', name: 'Utah' },
      { code: 'VT', name: 'Vermont' },
      { code: 'VA', name: 'Virginia' },
      { code: 'WA', name: 'Washington' },
      { code: 'WV', name: 'West Virginia' },
      { code: 'WI', name: 'Wisconsin' },
      { code: 'WY', name: 'Wyoming' },
    ]
  },
  'Canada': {
    code: 'CA',
    name: 'Canada',
    flag: '🇨🇦',
    postalCodeFormat: /^[A-Za-z]\d[A-Za-z] ?\d[A-Za-z]\d$/,
    postalCodeLabel: 'Postal Code',
    postalCodePlaceholder: 'K1A 0B1',
    postalCodeExample: 'Format: A1A 1A1',
    hasStates: true,
    stateLabel: 'Province',
    states: [
      { code: 'AB', name: 'Alberta' },
      { code: 'BC', name: 'British Columbia' },
      { code: 'MB', name: 'Manitoba' },
      { code: 'NB', name: 'New Brunswick' },
      { code: 'NL', name: 'Newfoundland and Labrador' },
      { code: 'NT', name: 'Northwest Territories' },
      { code: 'NS', name: 'Nova Scotia' },
      { code: 'NU', name: 'Nunavut' },
      { code: 'ON', name: 'Ontario' },
      { code: 'PE', name: 'Prince Edward Island' },
      { code: 'QC', name: 'Quebec' },
      { code: 'SK', name: 'Saskatchewan' },
      { code: 'YT', name: 'Yukon' },
    ]
  },
  'United Kingdom': {
    code: 'GB',
    name: 'United Kingdom',
    flag: '🇬🇧',
    postalCodeFormat: /^[A-Z]{1,2}\d{1,2}[A-Z]?\s?\d[A-Z]{2}$/i,
    postalCodeLabel: 'Postcode',
    postalCodePlaceholder: 'SW1A 1AA',
    postalCodeExample: 'Format: SW1A 1AA',
    hasStates: false,
  },
  'France': {
    code: 'FR',
    name: 'France',
    flag: '🇫🇷',
    postalCodeFormat: /^\d{5}$/,
    postalCodeLabel: 'Code Postal',
    postalCodePlaceholder: '75001',
    postalCodeExample: 'Format: 12345',
    hasStates: false,
  },
  'Germany': {
    code: 'DE',
    name: 'Germany',
    flag: '🇩🇪',
    postalCodeFormat: /^\d{5}$/,
    postalCodeLabel: 'Postleitzahl',
    postalCodePlaceholder: '10115',
    postalCodeExample: 'Format: 12345',
    hasStates: true,
    stateLabel: 'State',
    states: [
      { code: 'BW', name: 'Baden-Württemberg' },
      { code: 'BY', name: 'Bavaria' },
      { code: 'BE', name: 'Berlin' },
      { code: 'BB', name: 'Brandenburg' },
      { code: 'HB', name: 'Bremen' },
      { code: 'HH', name: 'Hamburg' },
      { code: 'HE', name: 'Hesse' },
      { code: 'MV', name: 'Mecklenburg-Vorpommern' },
      { code: 'NI', name: 'Lower Saxony' },
      { code: 'NW', name: 'North Rhine-Westphalia' },
      { code: 'RP', name: 'Rhineland-Palatinate' },
      { code: 'SL', name: 'Saarland' },
      { code: 'SN', name: 'Saxony' },
      { code: 'ST', name: 'Saxony-Anhalt' },
      { code: 'SH', name: 'Schleswig-Holstein' },
      { code: 'TH', name: 'Thuringia' },
    ]
  },
  'Australia': {
    code: 'AU',
    name: 'Australia',
    flag: '🇦🇺',
    postalCodeFormat: /^\d{4}$/,
    postalCodeLabel: 'Postcode',
    postalCodePlaceholder: '2000',
    postalCodeExample: 'Format: 1234',
    hasStates: true,
    stateLabel: 'State/Territory',
    states: [
      { code: 'ACT', name: 'Australian Capital Territory' },
      { code: 'NSW', name: 'New South Wales' },
      { code: 'NT', name: 'Northern Territory' },
      { code: 'QLD', name: 'Queensland' },
      { code: 'SA', name: 'South Australia' },
      { code: 'TAS', name: 'Tasmania' },
      { code: 'VIC', name: 'Victoria' },
      { code: 'WA', name: 'Western Australia' },
    ]
  },
  'Japan': {
    code: 'JP',
    name: 'Japan',
    flag: '🇯🇵',
    postalCodeFormat: /^\d{3}-?\d{4}$/,
    postalCodeLabel: '郵便番号',
    postalCodePlaceholder: '100-0001',
    postalCodeExample: 'Format: 123-4567',
    hasStates: true,
    stateLabel: 'Prefecture',
    states: [
      { code: 'Hokkaido', name: 'Hokkaido' },
      { code: 'Aomori', name: 'Aomori' },
      { code: 'Iwate', name: 'Iwate' },
      { code: 'Miyagi', name: 'Miyagi' },
      { code: 'Akita', name: 'Akita' },
      { code: 'Yamagata', name: 'Yamagata' },
      { code: 'Fukushima', name: 'Fukushima' },
      { code: 'Ibaraki', name: 'Ibaraki' },
      { code: 'Tochigi', name: 'Tochigi' },
      { code: 'Gunma', name: 'Gunma' },
      { code: 'Saitama', name: 'Saitama' },
      { code: 'Chiba', name: 'Chiba' },
      { code: 'Tokyo', name: 'Tokyo' },
      { code: 'Kanagawa', name: 'Kanagawa' },
      { code: 'Niigata', name: 'Niigata' },
      { code: 'Toyama', name: 'Toyama' },
      { code: 'Ishikawa', name: 'Ishikawa' },
      { code: 'Fukui', name: 'Fukui' },
      { code: 'Yamanashi', name: 'Yamanashi' },
      { code: 'Nagano', name: 'Nagano' },
      { code: 'Gifu', name: 'Gifu' },
      { code: 'Shizuoka', name: 'Shizuoka' },
      { code: 'Aichi', name: 'Aichi' },
      { code: 'Mie', name: 'Mie' },
      { code: 'Shiga', name: 'Shiga' },
      { code: 'Kyoto', name: 'Kyoto' },
      { code: 'Osaka', name: 'Osaka' },
      { code: 'Hyogo', name: 'Hyogo' },
      { code: 'Nara', name: 'Nara' },
      { code: 'Wakayama', name: 'Wakayama' },
      { code: 'Tottori', name: 'Tottori' },
      { code: 'Shimane', name: 'Shimane' },
      { code: 'Okayama', name: 'Okayama' },
      { code: 'Hiroshima', name: 'Hiroshima' },
      { code: 'Yamaguchi', name: 'Yamaguchi' },
      { code: 'Tokushima', name: 'Tokushima' },
      { code: 'Kagawa', name: 'Kagawa' },
      { code: 'Ehime', name: 'Ehime' },
      { code: 'Kochi', name: 'Kochi' },
      { code: 'Fukuoka', name: 'Fukuoka' },
      { code: 'Saga', name: 'Saga' },
      { code: 'Nagasaki', name: 'Nagasaki' },
      { code: 'Kumamoto', name: 'Kumamoto' },
      { code: 'Oita', name: 'Oita' },
      { code: 'Miyazaki', name: 'Miyazaki' },
      { code: 'Kagoshima', name: 'Kagoshima' },
      { code: 'Okinawa', name: 'Okinawa' },
    ]
  },
  'Singapore': {
    code: 'SG',
    name: 'Singapore',
    flag: '🇸🇬',
    postalCodeFormat: /^\d{6}$/,
    postalCodeLabel: 'Postal Code',
    postalCodePlaceholder: '238823',
    postalCodeExample: 'Format: 123456',
    hasStates: false,
  },
  'India': {
    code: 'IN',
    name: 'India',
    flag: '🇮🇳',
    postalCodeFormat: /^\d{6}$/,
    postalCodeLabel: 'PIN Code',
    postalCodePlaceholder: '110001',
    postalCodeExample: 'Format: 123456',
    hasStates: true,
    stateLabel: 'State',
    states: [
      { code: 'AN', name: 'Andaman and Nicobar Islands' },
      { code: 'AP', name: 'Andhra Pradesh' },
      { code: 'AR', name: 'Arunachal Pradesh' },
      { code: 'AS', name: 'Assam' },
      { code: 'BR', name: 'Bihar' },
      { code: 'CH', name: 'Chandigarh' },
      { code: 'CT', name: 'Chhattisgarh' },
      { code: 'DN', name: 'Dadra and Nagar Haveli' },
      { code: 'DD', name: 'Daman and Diu' },
      { code: 'DL', name: 'Delhi' },
      { code: 'GA', name: 'Goa' },
      { code: 'GJ', name: 'Gujarat' },
      { code: 'HR', name: 'Haryana' },
      { code: 'HP', name: 'Himachal Pradesh' },
      { code: 'JK', name: 'Jammu and Kashmir' },
      { code: 'JH', name: 'Jharkhand' },
      { code: 'KA', name: 'Karnataka' },
      { code: 'KL', name: 'Kerala' },
      { code: 'LD', name: 'Lakshadweep' },
      { code: 'MP', name: 'Madhya Pradesh' },
      { code: 'MH', name: 'Maharashtra' },
      { code: 'MN', name: 'Manipur' },
      { code: 'ML', name: 'Meghalaya' },
      { code: 'MZ', name: 'Mizoram' },
      { code: 'NL', name: 'Nagaland' },
      { code: 'OR', name: 'Odisha' },
      { code: 'PY', name: 'Puducherry' },
      { code: 'PB', name: 'Punjab' },
      { code: 'RJ', name: 'Rajasthan' },
      { code: 'SK', name: 'Sikkim' },
      { code: 'TN', name: 'Tamil Nadu' },
      { code: 'TG', name: 'Telangana' },
      { code: 'TR', name: 'Tripura' },
      { code: 'UP', name: 'Uttar Pradesh' },
      { code: 'UT', name: 'Uttarakhand' },
      { code: 'WB', name: 'West Bengal' },
    ]
  },
  'Mexico': {
    code: 'MX',
    name: 'Mexico',
    flag: '🇲🇽',
    postalCodeFormat: /^\d{5}$/,
    postalCodeLabel: 'Código Postal',
    postalCodePlaceholder: '01000',
    postalCodeExample: 'Format: 12345',
    hasStates: true,
    stateLabel: 'State',
    states: [
      { code: 'AG', name: 'Aguascalientes' },
      { code: 'BC', name: 'Baja California' },
      { code: 'BS', name: 'Baja California Sur' },
      { code: 'CM', name: 'Campeche' },
      { code: 'CS', name: 'Chiapas' },
      { code: 'CH', name: 'Chihuahua' },
      { code: 'CO', name: 'Coahuila' },
      { code: 'CL', name: 'Colima' },
      { code: 'DF', name: 'Ciudad de México' },
      { code: 'DG', name: 'Durango' },
      { code: 'GT', name: 'Guanajuato' },
      { code: 'GR', name: 'Guerrero' },
      { code: 'HG', name: 'Hidalgo' },
      { code: 'JC', name: 'Jalisco' },
      { code: 'EM', name: 'México' },
      { code: 'MI', name: 'Michoacán' },
      { code: 'MO', name: 'Morelos' },
      { code: 'NA', name: 'Nayarit' },
      { code: 'NL', name: 'Nuevo León' },
      { code: 'OA', name: 'Oaxaca' },
      { code: 'PU', name: 'Puebla' },
      { code: 'QT', name: 'Querétaro' },
      { code: 'QR', name: 'Quintana Roo' },
      { code: 'SL', name: 'San Luis Potosí' },
      { code: 'SI', name: 'Sinaloa' },
      { code: 'SO', name: 'Sonora' },
      { code: 'TB', name: 'Tabasco' },
      { code: 'TM', name: 'Tamaulipas' },
      { code: 'TL', name: 'Tlaxcala' },
      { code: 'VZ', name: 'Veracruz' },
      { code: 'YN', name: 'Yucatán' },
      { code: 'ZA', name: 'Zacatecas' },
    ]
  },
  'Brazil': {
    code: 'BR',
    name: 'Brazil',
    flag: '🇧🇷',
    postalCodeFormat: /^\d{5}-?\d{3}$/,
    postalCodeLabel: 'CEP',
    postalCodePlaceholder: '01310-100',
    postalCodeExample: 'Format: 12345-678',
    hasStates: true,
    stateLabel: 'State',
    states: [
      { code: 'AC', name: 'Acre' },
      { code: 'AL', name: 'Alagoas' },
      { code: 'AP', name: 'Amapá' },
      { code: 'AM', name: 'Amazonas' },
      { code: 'BA', name: 'Bahia' },
      { code: 'CE', name: 'Ceará' },
      { code: 'DF', name: 'Distrito Federal' },
      { code: 'ES', name: 'Espírito Santo' },
      { code: 'GO', name: 'Goiás' },
      { code: 'MA', name: 'Maranhão' },
      { code: 'MT', name: 'Mato Grosso' },
      { code: 'MS', name: 'Mato Grosso do Sul' },
      { code: 'MG', name: 'Minas Gerais' },
      { code: 'PA', name: 'Pará' },
      { code: 'PB', name: 'Paraíba' },
      { code: 'PR', name: 'Paraná' },
      { code: 'PE', name: 'Pernambuco' },
      { code: 'PI', name: 'Piauí' },
      { code: 'RJ', name: 'Rio de Janeiro' },
      { code: 'RN', name: 'Rio Grande do Norte' },
      { code: 'RS', name: 'Rio Grande do Sul' },
      { code: 'RO', name: 'Rondônia' },
      { code: 'RR', name: 'Roraima' },
      { code: 'SC', name: 'Santa Catarina' },
      { code: 'SP', name: 'São Paulo' },
      { code: 'SE', name: 'Sergipe' },
      { code: 'TO', name: 'Tocantins' },
    ]
  },
  'China': {
    code: 'CN',
    name: 'China',
    flag: '🇨🇳',
    postalCodeFormat: /^\d{6}$/,
    postalCodeLabel: '邮政编码',
    postalCodePlaceholder: '100000',
    postalCodeExample: 'Format: 123456',
    hasStates: true,
    stateLabel: 'Province',
    states: [
      { code: 'BJ', name: 'Beijing' },
      { code: 'TJ', name: 'Tianjin' },
      { code: 'HE', name: 'Hebei' },
      { code: 'SX', name: 'Shanxi' },
      { code: 'NM', name: 'Inner Mongolia' },
      { code: 'LN', name: 'Liaoning' },
      { code: 'JL', name: 'Jilin' },
      { code: 'HL', name: 'Heilongjiang' },
      { code: 'SH', name: 'Shanghai' },
      { code: 'JS', name: 'Jiangsu' },
      { code: 'ZJ', name: 'Zhejiang' },
      { code: 'AH', name: 'Anhui' },
      { code: 'FJ', name: 'Fujian' },
      { code: 'JX', name: 'Jiangxi' },
      { code: 'SD', name: 'Shandong' },
      { code: 'HA', name: 'Henan' },
      { code: 'HB', name: 'Hubei' },
      { code: 'HN', name: 'Hunan' },
      { code: 'GD', name: 'Guangdong' },
      { code: 'GX', name: 'Guangxi' },
      { code: 'HI', name: 'Hainan' },
      { code: 'CQ', name: 'Chongqing' },
      { code: 'SC', name: 'Sichuan' },
      { code: 'GZ', name: 'Guizhou' },
      { code: 'YN', name: 'Yunnan' },
      { code: 'XZ', name: 'Tibet' },
      { code: 'SN', name: 'Shaanxi' },
      { code: 'GS', name: 'Gansu' },
      { code: 'QH', name: 'Qinghai' },
      { code: 'NX', name: 'Ningxia' },
      { code: 'XJ', name: 'Xinjiang' },
    ]
  },
  'South Korea': {
    code: 'KR',
    name: 'South Korea',
    flag: '🇰🇷',
    postalCodeFormat: /^\d{5}$/,
    postalCodeLabel: '우편번호',
    postalCodePlaceholder: '03011',
    postalCodeExample: 'Format: 12345',
    hasStates: true,
    stateLabel: 'Province',
    states: [
      { code: 'Seoul', name: 'Seoul' },
      { code: 'Busan', name: 'Busan' },
      { code: 'Daegu', name: 'Daegu' },
      { code: 'Incheon', name: 'Incheon' },
      { code: 'Gwangju', name: 'Gwangju' },
      { code: 'Daejeon', name: 'Daejeon' },
      { code: 'Ulsan', name: 'Ulsan' },
      { code: 'Sejong', name: 'Sejong' },
      { code: 'Gyeonggi', name: 'Gyeonggi' },
      { code: 'Gangwon', name: 'Gangwon' },
      { code: 'North Chungcheong', name: 'North Chungcheong' },
      { code: 'South Chungcheong', name: 'South Chungcheong' },
      { code: 'North Jeolla', name: 'North Jeolla' },
      { code: 'South Jeolla', name: 'South Jeolla' },
      { code: 'North Gyeongsang', name: 'North Gyeongsang' },
      { code: 'South Gyeongsang', name: 'South Gyeongsang' },
      { code: 'Jeju', name: 'Jeju' },
    ]
  },
  'Netherlands': {
    code: 'NL',
    name: 'Netherlands',
    flag: '🇳🇱',
    postalCodeFormat: /^\d{4}\s?[A-Z]{2}$/,
    postalCodeLabel: 'Postcode',
    postalCodePlaceholder: '1012 AB',
    postalCodeExample: 'Format: 1234 AB',
    hasStates: true,
    stateLabel: 'Province',
    states: [
      { code: 'DR', name: 'Drenthe' },
      { code: 'FL', name: 'Flevoland' },
      { code: 'FR', name: 'Friesland' },
      { code: 'GE', name: 'Gelderland' },
      { code: 'GR', name: 'Groningen' },
      { code: 'LI', name: 'Limburg' },
      { code: 'NB', name: 'North Brabant' },
      { code: 'NH', name: 'North Holland' },
      { code: 'OV', name: 'Overijssel' },
      { code: 'UT', name: 'Utrecht' },
      { code: 'ZE', name: 'Zeeland' },
      { code: 'ZH', name: 'South Holland' },
    ]
  },
  'Ireland': {
    code: 'IE',
    name: 'Ireland',
    flag: '🇮🇪',
    postalCodeFormat: /^[A-Z]\d[\dA-Z]\s?[A-Z0-9]{4}$/,
    postalCodeLabel: 'Eircode',
    postalCodePlaceholder: 'D02 X285',
    postalCodeExample: 'Format: A65 F4E2 or D02X285',
    hasStates: false,
  },
  'Lithuania': {
    code: 'LT',
    name: 'Lithuania',
    flag: '🇱🇹',
    postalCodeFormat: /^(LT-)?\d{5}$/,
    postalCodeLabel: 'Postal Code',
    postalCodePlaceholder: '01001',
    postalCodeExample: 'Format: 12345 or LT-12345',
    hasStates: false,
  },
};

// Helper function to get country config
export function getCountryConfig(countryName: string): CountryConfig {
  return COUNTRY_CONFIGS[countryName] || {
    code: 'UNKNOWN',
    name: countryName,
    flag: '🌍', // Generic globe for unknown countries
    postalCodeFormat: null, // No validation for unknown countries
    postalCodeLabel: 'Postal Code',
    postalCodePlaceholder: '',
    postalCodeExample: '',
    hasStates: false,
  };
}

// Validate postal code for a specific country
export function validatePostalCode(postalCode: string, countryName: string): boolean {
  const config = getCountryConfig(countryName);
  
  // If no format specified, consider it valid
  if (!config.postalCodeFormat) {
    return true;
  }
  
  return config.postalCodeFormat.test(postalCode);
}

// Get postal code error message
export function getPostalCodeError(countryName: string): string {
  const config = getCountryConfig(countryName);
  return `Invalid ${config.postalCodeLabel.toLowerCase()} format. ${config.postalCodeExample}`;
}