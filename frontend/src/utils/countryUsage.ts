// Track country usage for smart sorting
const STORAGE_KEY = 'peptide-order-country-usage';
const MAX_RECENT_COUNTRIES = 5;

interface CountryUsage {
  [country: string]: {
    count: number;
    lastUsed: number;
  };
}

// Get usage data from localStorage
export function getCountryUsage(): CountryUsage {
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    return stored ? JSON.parse(stored) : {};
  } catch {
    return {};
  }
}

// Save usage data to localStorage
function saveCountryUsage(usage: CountryUsage): void {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(usage));
  } catch {
    // Ignore storage errors
  }
}

// Track country usage
export function trackCountryUsage(country: string): void {
  const usage = getCountryUsage();
  
  if (!usage[country]) {
    usage[country] = { count: 0, lastUsed: 0 };
  }
  
  usage[country].count += 1;
  usage[country].lastUsed = Date.now();
  
  saveCountryUsage(usage);
}

// Get countries sorted by usage frequency
export function getCountriesByUsage(countries: { code: string; name: string; flag: string }[]): { code: string; name: string; flag: string }[] {
  const usage = getCountryUsage();
  
  return [...countries].sort((a, b) => {
    const usageA = usage[a.name];
    const usageB = usage[b.name];
    
    // If neither has usage, maintain alphabetical order
    if (!usageA && !usageB) {
      return a.name.localeCompare(b.name);
    }
    
    // Countries with usage come first
    if (usageA && !usageB) return -1;
    if (!usageA && usageB) return 1;
    
    // Sort by count (descending), then by last used (descending)
    if (usageA.count !== usageB.count) {
      return usageB.count - usageA.count;
    }
    
    return usageB.lastUsed - usageA.lastUsed;
  });
}

// Get recently used countries
export function getRecentCountries(countries: { code: string; name: string; flag: string }[]): { code: string; name: string; flag: string }[] {
  const usage = getCountryUsage();
  
  // Get countries with usage data
  const usedCountries = countries.filter(c => usage[c.name]);
  
  // Sort by last used (descending)
  const sorted = usedCountries.sort((a, b) => {
    const usageA = usage[a.name];
    const usageB = usage[b.name];
    return (usageB?.lastUsed || 0) - (usageA?.lastUsed || 0);
  });
  
  // Return up to MAX_RECENT_COUNTRIES
  return sorted.slice(0, MAX_RECENT_COUNTRIES);
}

// Clear usage data (for testing or privacy)
export function clearCountryUsage(): void {
  try {
    localStorage.removeItem(STORAGE_KEY);
  } catch {
    // Ignore storage errors
  }
}