// Helper function to determine discount rate based on order number
export const getDiscountRate = (orderNumber: string | null | undefined): number => {
  if (!orderNumber) return 0.05; // Default to 5% for new orders without number yet
  
  // Extract the order sequence number from format ORD-YYYY-####
  const match = orderNumber.match(/^ORD-\d{4}-(\d{4})$/);
  if (!match) return 0.05; // Invalid format, default to 5%
  
  const orderNum = parseInt(match[1]);
  
  // Orders 1-35 get 3% discount, 36+ get 5% discount
  return orderNum <= 35 ? 0.03 : 0.05;
};

// Helper function to format discount percentage for display
export const getDiscountPercentage = (orderNumber: string | null | undefined): string => {
  const rate = getDiscountRate(orderNumber);
  return `${(rate * 100).toFixed(0)}%`;
};