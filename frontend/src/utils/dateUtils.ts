import { format, formatRelative, isToday, isYesterday, differenceInDays, parseISO } from 'date-fns';

/**
 * Formats a date with both absolute and relative time
 * Examples:
 * - "Jul 11 (today)"
 * - "Jul 10 (yesterday)" 
 * - "Jul 8 (3 days ago)"
 * - "Jun 15 (27 days ago)"
 */
export function formatDateWithRelative(date: Date | string | null | undefined): string {
  if (!date) return 'N/A';
  
  try {
    const d = typeof date === 'string' ? parseISO(date) : date;
    if (isNaN(d.getTime())) return 'Invalid Date';
    
    const absoluteDate = format(d, 'MMM dd');
    
    if (isToday(d)) {
      return `${absoluteDate} (today)`;
    } else if (isYesterday(d)) {
      return `${absoluteDate} (yesterday)`;
    } else {
      const daysDiff = differenceInDays(new Date(), d);
      if (daysDiff < 30) {
        return `${absoluteDate} (${daysDiff} days ago)`;
      } else {
        const monthsDiff = Math.floor(daysDiff / 30);
        return `${absoluteDate} (${monthsDiff} month${monthsDiff > 1 ? 's' : ''} ago)`;
      }
    }
  } catch (error) {
    console.error('Date formatting error:', error);
    return 'Invalid Date';
  }
}

/**
 * Formats a date with time for detailed views
 * Example: "Jul 11, 2025 at 3:45 PM"
 */
export function formatDateWithTime(date: Date | string | null | undefined): string {
  if (!date) return 'N/A';
  
  try {
    const d = typeof date === 'string' ? parseISO(date) : date;
    if (isNaN(d.getTime())) return 'Invalid Date';
    
    return format(d, 'MMM dd, yyyy \'at\' h:mm a');
  } catch (error) {
    console.error('Date formatting error:', error);
    return 'Invalid Date';
  }
}