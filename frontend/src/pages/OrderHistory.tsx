import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { FileText, Table, Mail, Trash2, MoreVertical, ClipboardCheck, AlertTriangle, Check, X, Package, Truck, ChevronLeft, ChevronRight } from 'lucide-react';
import { ordersApi, exportsApi } from '../api/client';
import type { Order } from '../../../shared/types/order.types';
import { formatDateWithRelative, formatDateWithTime } from '../utils/dateUtils';
import { useToastContext } from '../contexts/ToastContext';

export const OrderHistory: React.FC = () => {
  const navigate = useNavigate();
  const { showSuccess, showError } = useToastContext();
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const [filterSupplierStatus, setFilterSupplierStatus] = useState<string>('all');
  const [filterShippingStatus, setFilterShippingStatus] = useState<string>('all');
  const [selectedOrders, setSelectedOrders] = useState<Set<string>>(new Set());
  const [currentPage, setCurrentPage] = useState(1);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalCount: 0,
    limit: 20,
    hasNextPage: false,
    hasPrevPage: false
  });
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [deleteTarget, setDeleteTarget] = useState<{ type: 'single' | 'bulk'; id?: string }>({ type: 'single' });
  const [trackingStatuses, setTrackingStatuses] = useState<Record<string, { status: string; statusCode: string; lastUpdate?: string; trackingUrl: string }>>({});
  const [loadingTrackingStatuses, setLoadingTrackingStatuses] = useState(false);
  const [showMarkAsSentConfirm, setShowMarkAsSentConfirm] = useState(false);
  const [markAsSentOrderId, setMarkAsSentOrderId] = useState<string | null>(null);
  const [openDropdownId, setOpenDropdownId] = useState<string | null>(null);
  const [showTrackingModal, setShowTrackingModal] = useState(false);
  const [currentTrackingOrder, setCurrentTrackingOrder] = useState<Order | null>(null);
  const [trackingNumber, setTrackingNumber] = useState('');
  const [savingTracking, setSavingTracking] = useState(false);
  const dropdownRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Helper function to calculate supplier payment (using buying prices)
  const calculateSupplierPayment = (order: Order): number => {
    if (!order.items || order.items.length === 0) return 0;
    
    // Calculate subtotal using buying prices
    const subtotal = order.items.reduce((sum, item) => {
      const buyingPrice = item.buyingPrice || 0;
      return sum + (buyingPrice * item.qty);
    }, 0);
    
    // Fixed shipping cost
    const shipping = 40;
    
    // Get discount rate based on order number
    const getDiscountRate = (orderNumber: string | null): number => {
      if (!orderNumber) return 0.05;
      const match = orderNumber.match(/^ORD-\d{4}-(\d{4})$/);
      if (!match) return 0.05;
      const orderNum = parseInt(match[1]);
      return orderNum <= 35 ? 0.03 : 0.05;
    };
    
    const discountRate = getDiscountRate(order.orderNumber);
    const discount = subtotal * discountRate;
    
    // Total supplier payment
    return subtotal + shipping - discount;
  };

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Maintain focus on search input during re-renders
  useEffect(() => {
    if (isSearchFocused && searchInputRef.current) {
      // Use requestAnimationFrame to ensure the DOM has updated
      requestAnimationFrame(() => {
        if (searchInputRef.current && document.activeElement !== searchInputRef.current) {
          searchInputRef.current.focus();
        }
      });
    }
  }, [isSearchFocused, orders]); // Run when focus state changes or orders update

  useEffect(() => {
    // Clear selected orders when changing pages
    setSelectedOrders(new Set());
    fetchOrders();
  }, [currentPage, debouncedSearchTerm]);

  // Reset to page 1 when debounced search term changes
  useEffect(() => {
    if (debouncedSearchTerm && currentPage !== 1) {
      setCurrentPage(1);
    }
  }, [debouncedSearchTerm]);

  // Reset to page 1 when filters change
  useEffect(() => {
    if (currentPage !== 1) {
      setCurrentPage(1);
    }
  }, [filterSupplierStatus, filterShippingStatus]);

  useEffect(() => {
    fetchAllTrackingStatuses();
  }, [orders]);

  // Handle clicks outside dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (openDropdownId) {
        const dropdownRef = dropdownRefs.current[openDropdownId];
        if (dropdownRef && !dropdownRef.contains(event.target as Node)) {
          setOpenDropdownId(null);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [openDropdownId]);

  const fetchOrders = async () => {
    try {
      setLoading(true);
      const response = await ordersApi.getAll(currentPage, pagination.limit, debouncedSearchTerm);
      
      // Handle date conversions for orders
      const processedOrders = response.orders.map(order => ({
        ...order,
        placedAt: new Date(order.placedAt),
        sentAt: order.sentAt ? new Date(order.sentAt) : undefined
      }));
      
      setOrders(processedOrders);
      setPagination(response.pagination);
    } catch (error) {
      console.error('Failed to fetch orders:', error);
      setOrders([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchAllTrackingStatuses = async () => {
    const ordersWithTracking = orders.filter(order => order.tracking17);
    if (ordersWithTracking.length === 0) return;

    setLoadingTrackingStatuses(true);
    
    try {
      // Use the new bulk endpoint to fetch all tracking statuses in one request
      const orderIds = ordersWithTracking.map(order => order.id);
      const response = await ordersApi.getBulkTrackingStatus(orderIds);
      setTrackingStatuses(response.trackingStatuses);
    } catch (error) {
      console.error('Failed to fetch tracking statuses:', error);
    } finally {
      setLoadingTrackingStatuses(false);
    }
  };

  const filteredOrders = orders.filter(order => {
    // Apply only status filters client-side (search is handled server-side)
    // Supplier status filter
    if (filterSupplierStatus !== 'all') {
      const supplierStatus = order.supplierStatus || (order.sentToSupplier ? 'sent' : 'not_sent');
      if (supplierStatus !== filterSupplierStatus) return false;
    }

    // Shipping status filter
    if (filterShippingStatus !== 'all') {
      if (!order.tracking17) return false; // No tracking means no shipping status
      const shippingStatus = order.shippingStatus || 'Info Received';
      if (shippingStatus.toLowerCase().replace(' ', '_') !== filterShippingStatus) return false;
    }

    return true;
  });

  const handleSelectOrder = (orderId: string) => {
    setSelectedOrders(prev => {
      const newSet = new Set(prev);
      if (newSet.has(orderId)) {
        newSet.delete(orderId);
      } else {
        newSet.add(orderId);
      }
      return newSet;
    });
  };

  const handleSelectAll = () => {
    if (selectedOrders.size === filteredOrders.length) {
      setSelectedOrders(new Set());
    } else {
      setSelectedOrders(new Set(filteredOrders.map(order => order.id)));
    }
  };

  const isAllSelected = filteredOrders.length > 0 && selectedOrders.size === filteredOrders.length;
  const isPartiallySelected = selectedOrders.size > 0 && selectedOrders.size < filteredOrders.length;

  const toggleDropdown = (orderId: string) => {
    setOpenDropdownId(openDropdownId === orderId ? null : orderId);
  };

  const getDropdownPosition = (buttonElement: HTMLElement | null) => {
    if (!buttonElement) return {};
    
    const rect = buttonElement.getBoundingClientRect();
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
    
    return {
      top: rect.top + scrollTop,
      left: rect.left + scrollLeft,
      width: rect.width,
      height: rect.height
    };
  };

  const getStatusBadgeClasses = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-gray-100 dark:bg-gray-900/20 text-gray-800 dark:text-gray-200';
      case 'sent':
        return 'bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200';
      case 'shipped':
        return 'bg-purple-100 dark:bg-purple-900/20 text-purple-800 dark:text-purple-200';
      case 'in_transit':
        return 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-200';
      case 'delivered':
        return 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-200';
      default:
        return 'bg-gray-100 dark:bg-gray-900/20 text-gray-800 dark:text-gray-200';
    }
  };

  const getShippingStatusColor = (status: string): string => {
    switch (status) {
      case 'Delivered':
        return 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-200';
      case 'In Transit':
        return 'bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200';
      case 'Info Received':
        return 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-200';
      default:
        return 'bg-gray-100 dark:bg-gray-900/20 text-gray-800 dark:text-gray-200';
    }
  };

  const renderSupplierStatus = (order: Order) => {
    if (order.supplierStatus === 'sent' || order.sentToSupplier) {
      return (
        <div className="flex items-center">
          <div className="flex items-center justify-center w-5 h-5 rounded-full bg-green-100 dark:bg-green-900/20">
            <Check className="w-3 h-3 text-green-600 dark:text-green-400" />
          </div>
          <span className="ml-2 text-xs text-gray-600 dark:text-gray-400">Sent</span>
        </div>
      );
    }
    return (
      <div className="flex items-center">
        <div className="flex items-center justify-center w-5 h-5 rounded-full bg-red-100 dark:bg-red-900/20">
          <X className="w-3 h-3 text-red-600 dark:text-red-400" />
        </div>
        <span className="ml-2 text-xs text-gray-600 dark:text-gray-400">Not Sent</span>
      </div>
    );
  };

  const renderShippingStatus = (order: Order) => {
    if (!order.tracking17) {
      return <span className="text-gray-400 dark:text-dark-text-secondary text-sm">-</span>;
    }

    const status = order.shippingStatus || 'Info Received';
    
    return (
      <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getShippingStatusColor(status)}`}>
        {status}
      </span>
    );
  };

  const getStatusLabel = (order: Order): string => {
    if (order.status) {
      switch (order.status) {
        case 'pending':
          return 'Pending';
        case 'sent':
          return 'Sent';
        case 'shipped':
          return 'Shipped';
        case 'in_transit':
          return 'In Transit';
        case 'delivered':
          return 'Delivered';
        default:
          return (order.status as string).charAt(0).toUpperCase() + (order.status as string).slice(1);
      }
    }
    // Fallback for orders without status field
    if (order.tracking17) return 'Shipped';
    if (order.sentToSupplier) return 'Sent';
    return 'Pending';
  };

  const formatDate = (date: Date | string | null | undefined) => {
    if (!date) return 'N/A';
    
    try {
      const d = date instanceof Date ? date : new Date(date);
      if (isNaN(d.getTime())) return 'Invalid Date';
      
      return new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        timeZone: 'Asia/Shanghai'
      }).format(d);
    } catch (error) {
      console.error('Date formatting error:', error);
      return 'Invalid Date';
    }
  };

  const handleDownloadPdf = async (orderId: string) => {
    try {
      const blob = await exportsApi.downloadPdf(orderId);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `order-${orderId}.pdf`;
      a.click();
      window.URL.revokeObjectURL(url);
      showSuccess('PDF downloaded successfully');
    } catch (error) {
      console.error('Failed to download PDF:', error);
      showError('Failed to download PDF. Please try again.');
    }
  };

  const handleDownloadExcel = async (orderId: string) => {
    try {
      const blob = await exportsApi.downloadExcel(orderId);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `order-${orderId}.xlsx`;
      a.click();
      window.URL.revokeObjectURL(url);
      showSuccess('Excel file downloaded successfully');
    } catch (error) {
      console.error('Failed to download Excel:', error);
      showError('Failed to download Excel file. Please try again.');
    }
  };

  const handleSendToSupplier = async (orderId: string) => {
    try {
      const result = await ordersApi.sendToSupplier(orderId);
      
      // Update the order in the local state with date conversions
      setOrders(prevOrders => 
        prevOrders.map(order => 
          order.id === orderId ? {
            ...result.order,
            placedAt: new Date(result.order.placedAt),
            sentAt: result.order.sentAt ? new Date(result.order.sentAt) : undefined
          } : order
        )
      );
      
      showSuccess('Order successfully sent to supplier!');
    } catch (error) {
      console.error('Failed to send order to supplier:', error);
      showError('Failed to send order to supplier. Please try again.');
    }
  };

  const handleMarkAsSent = async (orderId: string) => {
    setMarkAsSentOrderId(orderId);
    setShowMarkAsSentConfirm(true);
  };

  const handleMarkAsSentConfirm = async () => {
    if (!markAsSentOrderId) return;

    try {
      const result = await ordersApi.markAsSent(markAsSentOrderId);
      
      // Update the order in the local state with date conversions
      setOrders(prevOrders => 
        prevOrders.map(order => 
          order.id === markAsSentOrderId ? {
            ...result.order,
            placedAt: new Date(result.order.placedAt),
            sentAt: result.order.sentAt ? new Date(result.order.sentAt) : undefined
          } : order
        )
      );
      
      showSuccess('Order marked as sent to supplier');
      setShowMarkAsSentConfirm(false);
      setMarkAsSentOrderId(null);
    } catch (error) {
      console.error('Failed to mark order as sent:', error);
      showError('Failed to mark order as sent. Please try again.');
    }
  };

  const handleBulkDownloadPdf = async () => {
    if (selectedOrders.size === 0) return;
    try {
      const orderIds = Array.from(selectedOrders);
      const blob = await exportsApi.bulkDownloadPdf(orderIds);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `bulk-orders-${new Date().getTime()}.pdf`;
      a.click();
      window.URL.revokeObjectURL(url);
      showSuccess(`${selectedOrders.size} PDF${selectedOrders.size > 1 ? 's' : ''} downloaded successfully`);
    } catch (error) {
      console.error('Failed to download bulk PDF:', error);
      showError('Failed to download PDF files. Please try again.');
    }
  };

  const handleBulkDownloadExcel = async () => {
    if (selectedOrders.size === 0) return;
    try {
      const orderIds = Array.from(selectedOrders);
      const blob = await exportsApi.bulkDownloadExcel(orderIds);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `bulk-orders-${new Date().getTime()}.xlsx`;
      a.click();
      window.URL.revokeObjectURL(url);
      showSuccess(`${selectedOrders.size} Excel file${selectedOrders.size > 1 ? 's' : ''} downloaded successfully`);
    } catch (error) {
      console.error('Failed to download bulk Excel:', error);
      showError('Failed to download Excel files. Please try again.');
    }
  };

  const handleBulkSendToSupplier = async () => {
    if (selectedOrders.size === 0) return;
    try {
      const orderIds = Array.from(selectedOrders);
      const result = await exportsApi.bulkSendToSupplier(orderIds);
      
      // Update orders in local state with date conversions
      if (result.orders && Array.isArray(result.orders)) {
        setOrders(prevOrders => 
          prevOrders.map(order => {
            const updatedOrder = result.orders.find((o: Order) => o.id === order.id);
            if (updatedOrder) {
              return {
                ...updatedOrder,
                placedAt: new Date(updatedOrder.placedAt),
                sentAt: updatedOrder.sentAt ? new Date(updatedOrder.sentAt) : undefined
              };
            }
            return order;
          })
        );
      }
      
      // Clear selection after successful send
      setSelectedOrders(new Set());
      
      showSuccess(`${orderIds.length} order${orderIds.length > 1 ? 's' : ''} successfully sent to supplier!`);
    } catch (error) {
      console.error('Failed to send bulk orders to supplier:', error);
      showError('Failed to send orders to supplier. Please try again.');
    }
  };

  const handleDeleteClick = (orderId?: string) => {
    if (orderId) {
      setDeleteTarget({ type: 'single', id: orderId });
    } else {
      setDeleteTarget({ type: 'bulk' });
    }
    setShowDeleteConfirm(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      if (deleteTarget.type === 'single' && deleteTarget.id) {
        await ordersApi.delete(deleteTarget.id);
        
        // Check if this was the last item on the current page
        if (orders.length === 1 && currentPage > 1) {
          setCurrentPage(currentPage - 1);
        } else {
          await fetchOrders(); // Refresh the current page
        }
        
        showSuccess('Order deleted successfully');
      } else if (deleteTarget.type === 'bulk') {
        const orderIds = Array.from(selectedOrders);
        const result = await ordersApi.deleteBulk(orderIds);
        
        // Check if all items on the current page were deleted
        if (orders.length === orderIds.length && currentPage > 1) {
          setCurrentPage(currentPage - 1);
        } else {
          await fetchOrders(); // Refresh the current page
        }
        
        setSelectedOrders(new Set());
        showSuccess(`${result.deletedCount} order${result.deletedCount > 1 ? 's' : ''} deleted successfully`);
      }
    } catch (error) {
      console.error('Failed to delete order(s):', error);
      showError('Failed to delete order(s). Please try again.');
    } finally {
      setShowDeleteConfirm(false);
    }
  };

  const handleAddTracking = (order: Order) => {
    setCurrentTrackingOrder(order);
    setTrackingNumber(order.tracking17 || '');
    setShowTrackingModal(true);
    setOpenDropdownId(null);
  };

  const handleSaveTracking = async () => {
    if (!currentTrackingOrder) return;

    try {
      setSavingTracking(true);
      const updatedOrder = await ordersApi.updateTracking(currentTrackingOrder.id, trackingNumber);
      
      // Update the order in the local state with date conversions
      setOrders(prevOrders => 
        prevOrders.map(order => 
          order.id === currentTrackingOrder.id ? {
            ...updatedOrder,
            placedAt: new Date(updatedOrder.placedAt),
            sentAt: updatedOrder.sentAt ? new Date(updatedOrder.sentAt) : undefined
          } : order
        )
      );
      
      // Refresh tracking statuses to show updated tracking info
      await fetchAllTrackingStatuses();
      
      setShowTrackingModal(false);
      setCurrentTrackingOrder(null);
      setTrackingNumber('');
      showSuccess(
        currentTrackingOrder.tracking17 
          ? 'Tracking number updated successfully' 
          : 'Tracking number added successfully'
      );
    } catch (error) {
      console.error('Failed to save tracking:', error);
      showError('Failed to save tracking number. Please try again.');
    } finally {
      setSavingTracking(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-dark-bg-0 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-center h-64">
            <div className="text-gray-500 dark:text-dark-text-secondary">Loading orders...</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-dark-bg-0 py-4 sm:py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="sm:flex sm:items-center">
          <div className="sm:flex-auto">
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-dark-text-primary">Order History</h1>
            <p className="mt-2 text-sm text-gray-700 dark:text-dark-text-primary">
              A list of all orders including customer details, payment status, and tracking information.
            </p>
          </div>
          <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
            <button
              type="button"
              onClick={() => navigate('/new-order')}
              className="w-full sm:w-auto inline-flex items-center justify-center rounded-md border border-transparent bg-dark-accent-blue px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-dark-accent-blue/90 focus:outline-none focus:ring-2 focus:ring-dark-accent-blue focus:ring-offset-2 dark:focus:ring-offset-dark-bg-0"
            >
              Create New Order
            </button>
          </div>
        </div>

        {/* Filters */}
        <div className="mt-6 sm:mt-8 bg-white dark:bg-dark-bg-1 shadow dark:shadow-dark-md rounded-lg p-4 sm:p-6">
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
            <div>
              <label htmlFor="search" className="block text-sm font-medium text-gray-700 dark:text-dark-text-primary">
                Search
              </label>
              <input
                type="text"
                id="search"
                ref={searchInputRef}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onFocus={() => setIsSearchFocused(true)}
                onBlur={() => setIsSearchFocused(false)}
                placeholder="Order number, ID, or customer name..."
                className="mt-1 block w-full rounded-lg border border-gray-300 dark:border-dark-border-default bg-white dark:bg-dark-bg-2 px-3 py-2 text-sm text-gray-900 dark:text-dark-text-primary shadow-sm focus:outline-none focus:border-dark-accent-blue dark:focus:border-indigo-400 focus:ring-2 focus:ring-dark-accent-blue dark:focus:ring-indigo-400 focus:ring-offset-0 transition-colors duration-200 appearance-none"
              />
            </div>
            <div>
              <label htmlFor="filterSupplierStatus" className="block text-sm font-medium text-gray-700 dark:text-dark-text-primary">
                Supplier Status
              </label>
              <select
                id="filterSupplierStatus"
                value={filterSupplierStatus}
                onChange={(e) => setFilterSupplierStatus(e.target.value)}
                className="mt-1 block w-full rounded-lg border border-gray-300 dark:border-dark-border-default bg-white dark:bg-dark-bg-2 px-3 py-2 text-sm text-gray-900 dark:text-dark-text-primary shadow-sm focus:outline-none focus:border-dark-accent-blue dark:focus:border-indigo-400 focus:ring-2 focus:ring-dark-accent-blue dark:focus:ring-indigo-400 focus:ring-offset-0 transition-colors duration-200 appearance-none"
              >
                <option value="all">All</option>
                <option value="sent">Sent</option>
                <option value="not_sent">Not Sent</option>
              </select>
            </div>
            <div>
              <label htmlFor="filterShippingStatus" className="block text-sm font-medium text-gray-700 dark:text-dark-text-primary">
                Shipping Status
              </label>
              <select
                id="filterShippingStatus"
                value={filterShippingStatus}
                onChange={(e) => setFilterShippingStatus(e.target.value)}
                className="mt-1 block w-full rounded-lg border border-gray-300 dark:border-dark-border-default bg-white dark:bg-dark-bg-2 px-3 py-2 text-sm text-gray-900 dark:text-dark-text-primary shadow-sm focus:outline-none focus:border-dark-accent-blue dark:focus:border-indigo-400 focus:ring-2 focus:ring-dark-accent-blue dark:focus:ring-indigo-400 focus:ring-offset-0 transition-colors duration-200 appearance-none"
              >
                <option value="all">All</option>
                <option value="info_received">Info Received</option>
                <option value="in_transit">In Transit</option>
                <option value="delivered">Delivered</option>
              </select>
            </div>
          </div>
        </div>

        {/* Bulk Actions Bar */}
        {selectedOrders.size > 0 && (
          <div className="mt-4 bg-white dark:bg-dark-bg-1 shadow dark:shadow-dark-md rounded-lg p-4">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
              <div className="text-sm text-gray-700 dark:text-dark-text-primary">
                {selectedOrders.size} order{selectedOrders.size > 1 ? 's' : ''} selected
              </div>
              <div className="flex flex-wrap gap-2">
                {/* PDF button - icon only on mobile, with text on desktop */}
                <button
                  onClick={handleBulkDownloadPdf}
                  title="Download PDF"
                  className="inline-flex items-center justify-center p-2 sm:px-3 sm:py-1.5 border border-gray-300 dark:border-dark-border-default shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-dark-text-primary bg-white dark:bg-dark-bg-2 hover:bg-gray-50 dark:hover:bg-dark-bg-hover focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-dark-bg-0 focus:ring-dark-accent-blue transition-colors"
                >
                  <FileText className="h-4 w-4 sm:h-4 sm:w-4" />
                  <span className="hidden sm:inline sm:ml-1.5">PDF</span>
                </button>
                {/* Excel button - icon only on mobile, with text on desktop */}
                <button
                  onClick={handleBulkDownloadExcel}
                  title="Download Excel"
                  className="inline-flex items-center justify-center p-2 sm:px-3 sm:py-1.5 border border-gray-300 dark:border-dark-border-default shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-dark-text-primary bg-white dark:bg-dark-bg-2 hover:bg-gray-50 dark:hover:bg-dark-bg-hover focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-dark-bg-0 focus:ring-dark-accent-blue transition-colors"
                >
                  <Table className="h-4 w-4 sm:h-4 sm:w-4" />
                  <span className="hidden sm:inline sm:ml-1.5">Excel</span>
                </button>
                {/* Send to Supplier button - icon only on mobile, with text on desktop */}
                <button
                  onClick={handleBulkSendToSupplier}
                  title="Send to Supplier"
                  className="inline-flex items-center justify-center p-2 sm:px-3 sm:py-1.5 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-dark-accent-blue hover:bg-dark-accent-blue/90 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-dark-bg-0 focus:ring-dark-accent-blue transition-colors"
                >
                  <Mail className="h-4 w-4 sm:h-4 sm:w-4" />
                  <span className="hidden sm:inline sm:ml-1.5">Send to Supplier</span>
                </button>
                {/* Delete Selected button - icon only on mobile, with text on desktop */}
                <button
                  onClick={() => handleDeleteClick()}
                  title="Delete Selected"
                  className="inline-flex items-center justify-center p-2 sm:px-3 sm:py-1.5 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-dark-bg-0 focus:ring-red-500 transition-colors"
                >
                  <Trash2 className="h-4 w-4 sm:h-4 sm:w-4" />
                  <span className="hidden sm:inline sm:ml-1.5">Delete Selected</span>
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Orders Table - Desktop */}
        <div className="mt-8 hidden md:block overflow-hidden shadow dark:shadow-dark-md ring-1 ring-black ring-opacity-5 dark:ring-0 md:rounded-lg">
          <table className="min-w-full divide-y divide-gray-300 dark:divide-dark-border-default">
            <thead className="bg-gray-50 dark:bg-dark-bg-0">
              <tr>
                <th scope="col" className="relative px-3 py-3">
                  <input
                    type="checkbox"
                    className="h-4 w-4 text-dark-accent-blue focus:ring-dark-accent-blue border-gray-300 dark:border-dark-border-default rounded"
                    checked={isAllSelected}
                    ref={input => {
                      if (input) {
                        input.indeterminate = isPartiallySelected;
                      }
                    }}
                    onChange={handleSelectAll}
                  />
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-dark-text-secondary uppercase tracking-wider">
                  Order #
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-dark-text-secondary uppercase tracking-wider">
                  Customer
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-dark-text-secondary uppercase tracking-wider">
                  Date
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-dark-text-secondary uppercase tracking-wider">
                  Total
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-dark-text-secondary uppercase tracking-wider">
                  Supplier Status
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-dark-text-secondary uppercase tracking-wider">
                  Shipping Status
                </th>
                <th scope="col" className="relative px-6 py-3">
                  <span className="sr-only">Actions</span>
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-dark-bg-1 divide-y divide-gray-200 dark:divide-dark-border-subtle">
              {filteredOrders.length === 0 ? (
                <tr>
                  <td colSpan={8} className="px-6 py-12 text-center text-sm text-gray-500 dark:text-dark-text-secondary">
                    No orders found
                  </td>
                </tr>
              ) : (
                filteredOrders.map((order) => (
                  <tr key={order.id} className={`hover:bg-gray-50 dark:hover:bg-dark-bg-hover ${selectedOrders.has(order.id) ? 'bg-indigo-50 dark:bg-indigo-900/20' : ''}`}>
                    <td className="relative px-3 py-4">
                      <input
                        type="checkbox"
                        className="h-4 w-4 text-dark-accent-blue focus:ring-dark-accent-blue border-gray-300 dark:border-dark-border-default rounded"
                        checked={selectedOrders.has(order.id)}
                        onChange={() => handleSelectOrder(order.id)}
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-dark-text-primary">
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => navigate(`/orders/${order.id}`)}
                          className="text-dark-accent-blue dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 hover:underline"
                        >
                          {order.orderNumber || String(order.id).slice(0, 8) + '...'}
                        </button>
                        {order.isEdited && (
                          <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-200">
                            Edited
                          </span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text-primary">
                      {order.customerName || 'N/A'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-dark-text-secondary">
                      {formatDateWithRelative(order.placedAt)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text-primary">
                      ${calculateSupplierPayment(order).toFixed(2)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {renderSupplierStatus(order)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {renderShippingStatus(order)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end space-x-2">
                        <button
                          onClick={() => navigate(`/orders/${order.id}`)}
                          className="text-dark-accent-blue dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300"
                        >
                          View
                        </button>
                        <div 
                          className="relative"
                          ref={(el) => { 
                            dropdownRefs.current[order.id] = el;
                            if (el && openDropdownId === order.id) {
                              const button = el.querySelector('button');
                              if (button) {
                                const rect = button.getBoundingClientRect();
                                const dropdown = el.querySelector('.dropdown-menu');
                                if (dropdown) {
                                  (dropdown as HTMLElement).style.top = `${rect.top + window.scrollY - 8}px`;
                                  (dropdown as HTMLElement).style.left = `${rect.right + window.scrollX - 192}px`;
                                }
                              }
                            }
                          }}
                        >
                          <button 
                            onClick={() => toggleDropdown(order.id)}
                            className="text-gray-400 dark:text-dark-text-secondary hover:text-gray-600 dark:hover:text-dark-text-primary p-1 rounded hover:bg-gray-100 dark:hover:bg-dark-bg-hover transition-colors"
                          >
                            <MoreVertical className="w-5 h-5" />
                          </button>
                          <div className={`dropdown-menu fixed z-[9999] w-48 rounded-md shadow-lg bg-white dark:bg-dark-bg-1 ring-1 ring-black ring-opacity-5 dark:ring-0 transition-all duration-200 dark:shadow-dark-md ${
                            openDropdownId === order.id ? 'opacity-100 visible' : 'opacity-0 invisible pointer-events-none'
                          }`} style={{
                            top: 'auto',
                            left: 'auto'
                          }}>
                            <div className="py-1">
                              <button
                                onClick={() => {
                                  handleDownloadPdf(order.id);
                                  setOpenDropdownId(null);
                                }}
                                className="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-dark-text-primary hover:bg-gray-100 dark:hover:bg-dark-bg-hover"
                              >
                                Download PDF
                              </button>
                              <button
                                onClick={() => {
                                  handleDownloadExcel(order.id);
                                  setOpenDropdownId(null);
                                }}
                                className="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-dark-text-primary hover:bg-gray-100 dark:hover:bg-dark-bg-hover"
                              >
                                Download Excel
                              </button>
                              {!order.sentToSupplier && (
                                <>
                                  <button
                                    onClick={() => {
                                      handleSendToSupplier(order.id);
                                      setOpenDropdownId(null);
                                    }}
                                    className="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-dark-text-primary hover:bg-gray-100 dark:hover:bg-dark-bg-hover"
                                  >
                                    Send to Supplier
                                  </button>
                                  <button
                                    onClick={() => {
                                      handleMarkAsSent(order.id);
                                      setOpenDropdownId(null);
                                    }}
                                    className="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-dark-text-primary hover:bg-gray-100 dark:hover:bg-dark-bg-hover"
                                  >
                                    Mark as Sent
                                  </button>
                                </>
                              )}
                              <>
                              <button
                                onClick={() => {
                                  handleAddTracking(order);
                                  setOpenDropdownId(null);
                                }}
                                className="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-dark-text-primary hover:bg-gray-100 dark:hover:bg-dark-bg-hover"
                              >
                                Add Tracking
                              </button>
                              <button
                                onClick={() => {
                                  navigate(`/orders/${order.id}/edit`);
                                  setOpenDropdownId(null);
                                }}
                                className="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-dark-text-primary hover:bg-gray-100 dark:hover:bg-dark-bg-hover"
                              >
                                Edit Order
                              </button>
                            </>
                              <div className="border-t border-gray-100 dark:border-dark-border-subtle"></div>
                              <button
                                onClick={() => {
                                  handleDeleteClick(order.id);
                                  setOpenDropdownId(null);
                                }}
                                className="block w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700"
                              >
                                Delete Order
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* Orders Cards - Mobile */}
        <div className="mt-8 md:hidden space-y-4">
          {filteredOrders.length === 0 ? (
            <div className="bg-white dark:bg-dark-bg-1 rounded-lg shadow dark:shadow-dark-md p-6 text-center">
              <p className="text-sm text-gray-500 dark:text-dark-text-secondary">No orders found</p>
            </div>
          ) : (
            filteredOrders.map((order) => (
              <div 
                key={order.id}
                onClick={() => navigate(`/orders/${order.id}`)}
                className={`bg-white dark:bg-dark-bg-1 rounded-lg shadow dark:shadow-dark-md p-4 cursor-pointer hover:shadow-lg dark:hover:shadow-dark-md transition-shadow ${
                  selectedOrders.has(order.id) ? 'ring-2 ring-indigo-500 dark:ring-indigo-400' : ''
                }`}
              >
                {/* Card Header */}
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-start space-x-3 flex-1">
                    <input
                      type="checkbox"
                      className="h-5 w-5 mt-0.5 text-dark-accent-blue focus:ring-dark-accent-blue border-gray-300 dark:border-dark-border-default rounded"
                      checked={selectedOrders.has(order.id)}
                      onChange={(e) => {
                        e.stopPropagation();
                        handleSelectOrder(order.id);
                      }}
                      onClick={(e) => e.stopPropagation()}
                    />
                    <div className="flex-1 min-w-0">
                      <p className="text-base font-semibold text-gray-900 dark:text-dark-text-primary truncate">
                        {order.customerName || 'N/A'}
                      </p>
                      <div className="flex items-center space-x-2">
                        <p className="text-sm text-gray-500 dark:text-dark-text-secondary">
                          {order.orderNumber || `#${String(order.id).slice(0, 8)}`}
                        </p>
                        {order.isEdited && (
                          <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-200">
                            Edited
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                  <div 
                    className="relative" 
                    onClick={(e) => e.stopPropagation()}
                    ref={(el) => { 
                      dropdownRefs.current[`mobile-${order.id}`] = el;
                      if (el && openDropdownId === `mobile-${order.id}`) {
                        const button = el.querySelector('button');
                        if (button) {
                          const rect = button.getBoundingClientRect();
                          const dropdown = el.querySelector('.mobile-dropdown-menu');
                          if (dropdown) {
                            (dropdown as HTMLElement).style.top = `${rect.top + window.scrollY - 8}px`;
                            (dropdown as HTMLElement).style.left = `${rect.right + window.scrollX - 192}px`;
                          }
                        }
                      }
                    }}
                  >
                    <button 
                      className="p-1 text-gray-400 dark:text-dark-text-secondary hover:text-gray-600 dark:hover:text-dark-text-primary rounded hover:bg-gray-100 dark:hover:bg-dark-bg-hover transition-colors"
                      onClick={() => toggleDropdown(`mobile-${order.id}`)}
                    >
                      <MoreVertical className="w-5 h-5" />
                    </button>
                    <div className={`mobile-dropdown-menu fixed z-[9999] w-48 rounded-md shadow-lg bg-white dark:bg-dark-bg-1 ring-1 ring-black ring-opacity-5 dark:ring-0 transition-all duration-200 dark:shadow-dark-md ${
                      openDropdownId === `mobile-${order.id}` ? 'opacity-100 visible' : 'opacity-0 invisible pointer-events-none'
                    }`} style={{
                      top: 'auto',
                      left: 'auto'
                    }}>
                      <div className="py-1">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDownloadPdf(order.id);
                            setOpenDropdownId(null);
                          }}
                          className="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-dark-text-primary hover:bg-gray-100 dark:hover:bg-dark-bg-hover"
                        >
                          Download PDF
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDownloadExcel(order.id);
                            setOpenDropdownId(null);
                          }}
                          className="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-dark-text-primary hover:bg-gray-100 dark:hover:bg-dark-bg-hover"
                        >
                          Download Excel
                        </button>
                        {!order.sentToSupplier && (
                          <>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleSendToSupplier(order.id);
                                setOpenDropdownId(null);
                              }}
                              className="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-dark-text-primary hover:bg-gray-100 dark:hover:bg-dark-bg-hover"
                            >
                              Send to Supplier
                            </button>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleMarkAsSent(order.id);
                                setOpenDropdownId(null);
                              }}
                              className="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-dark-text-primary hover:bg-gray-100 dark:hover:bg-dark-bg-hover"
                            >
                              Mark as Sent
                            </button>
                          </>
                        )}
<>
<button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleAddTracking(order);
                              setOpenDropdownId(null);
                            }}
                            className="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-dark-text-primary hover:bg-gray-100 dark:hover:bg-dark-bg-hover"
                          >
                            Add Tracking
                          </button>                         <button
                           onClick={(e) => {
                             e.stopPropagation();
                             navigate(`/orders/${order.id}/edit`);
                             setOpenDropdownId(null);
                           }}
                           className="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-dark-text-primary hover:bg-gray-100 dark:hover:bg-dark-bg-hover"
                         >
                           Edit Order
                         </button>
                       </>                        <div className="border-t border-gray-100 dark:border-dark-border-subtle"></div>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteClick(order.id);
                            setOpenDropdownId(null);
                          }}
                          className="block w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700"
                        >
                          Delete Order
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Card Body - Compact Info */}
                <div className="space-y-3">
                  <div className="flex justify-between items-start">
                    <div className="flex flex-col space-y-2">
                      <span className="text-sm text-gray-500 dark:text-dark-text-secondary">
                        {formatDateWithRelative(order.placedAt)}
                      </span>
                      {renderSupplierStatus(order)}
                    </div>
                    <div className="flex flex-col items-end space-y-2">
                      <p className="text-lg font-semibold text-gray-900 dark:text-dark-text-primary">
                        ${calculateSupplierPayment(order).toFixed(2)}
                      </p>
                      {order.tracking17 && renderShippingStatus(order)}
                    </div>
                  </div>
                  
                  {order.tracking17 && (
                    <div className="flex items-center text-xs text-gray-600 dark:text-dark-text-secondary pt-2 border-t border-gray-100 dark:border-dark-border-subtle">
                      <ClipboardCheck className="w-4 h-4 mr-1 text-green-600 dark:text-green-400" />
                      <a 
                        href={`https://parcelsapp.com/tracking/${order.tracking17}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        onClick={(e) => e.stopPropagation()}
                        className="hover:text-dark-accent-blue dark:hover:text-indigo-400 hover:underline"
                      >
                        Tracking: {order.tracking17}
                      </a>
                    </div>
                  )}
                </div>
              </div>
            ))
          )}
        </div>

        {/* Pagination Controls */}
        {pagination.totalPages > 1 && (
          <div className="mt-6 flex items-center justify-between bg-white dark:bg-dark-bg-1 px-4 py-3 sm:px-6 rounded-lg shadow dark:shadow-dark-md">
            <div className="flex flex-1 justify-between sm:hidden">
              <button
                onClick={() => setCurrentPage(currentPage - 1)}
                disabled={!pagination.hasPrevPage}
                className="relative inline-flex items-center rounded-md border border-gray-300 dark:border-dark-border-default bg-white dark:bg-dark-bg-2 px-4 py-2 text-sm font-medium text-gray-700 dark:text-dark-text-primary hover:bg-gray-50 dark:hover:bg-dark-bg-hover disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
              <div className="flex items-center text-sm text-gray-700 dark:text-dark-text-primary">
                Page {pagination.currentPage} of {pagination.totalPages}
              </div>
              <button
                onClick={() => setCurrentPage(currentPage + 1)}
                disabled={!pagination.hasNextPage}
                className="relative ml-3 inline-flex items-center rounded-md border border-gray-300 dark:border-dark-border-default bg-white dark:bg-dark-bg-2 px-4 py-2 text-sm font-medium text-gray-700 dark:text-dark-text-primary hover:bg-gray-50 dark:hover:bg-dark-bg-hover disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            </div>
            <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700 dark:text-dark-text-primary">
                  Showing{' '}
                  <span className="font-medium">
                    {(pagination.currentPage - 1) * pagination.limit + 1}
                  </span>{' '}
                  to{' '}
                  <span className="font-medium">
                    {Math.min(pagination.currentPage * pagination.limit, pagination.totalCount)}
                  </span>{' '}
                  of{' '}
                  <span className="font-medium">{pagination.totalCount}</span>{' '}
                  orders
                </p>
              </div>
              <div>
                <nav className="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                  <button
                    onClick={() => setCurrentPage(currentPage - 1)}
                    disabled={!pagination.hasPrevPage}
                    className="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 dark:text-dark-text-secondary ring-1 ring-inset ring-gray-300 dark:ring-dark-border-default hover:bg-gray-50 dark:hover:bg-dark-bg-hover focus:z-20 focus:outline-offset-0 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <span className="sr-only">Previous</span>
                    <ChevronLeft className="h-5 w-5" aria-hidden="true" />
                  </button>
                  
                  {/* Generate page numbers */}
                  {Array.from({ length: Math.min(7, pagination.totalPages) }, (_, i) => {
                    let pageNum;
                    if (pagination.totalPages <= 7) {
                      pageNum = i + 1;
                    } else if (pagination.currentPage <= 4) {
                      if (i < 5) pageNum = i + 1;
                      else if (i === 5) return <span key={i} className="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-700 dark:text-dark-text-primary ring-1 ring-inset ring-gray-300 dark:ring-dark-border-default focus:outline-offset-0">...</span>;
                      else pageNum = pagination.totalPages;
                    } else if (pagination.currentPage >= pagination.totalPages - 3) {
                      if (i === 0) pageNum = 1;
                      else if (i === 1) return <span key={i} className="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-700 dark:text-dark-text-primary ring-1 ring-inset ring-gray-300 dark:ring-dark-border-default focus:outline-offset-0">...</span>;
                      else pageNum = pagination.totalPages - (6 - i);
                    } else {
                      if (i === 0) pageNum = 1;
                      else if (i === 1) return <span key={i} className="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-700 dark:text-dark-text-primary ring-1 ring-inset ring-gray-300 dark:ring-dark-border-default focus:outline-offset-0">...</span>;
                      else if (i >= 2 && i <= 4) pageNum = pagination.currentPage - (3 - i);
                      else if (i === 5) return <span key={i} className="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-700 dark:text-dark-text-primary ring-1 ring-inset ring-gray-300 dark:ring-dark-border-default focus:outline-offset-0">...</span>;
                      else pageNum = pagination.totalPages;
                    }
                    
                    if (!pageNum) return null;
                    
                    return (
                      <button
                        key={pageNum}
                        onClick={() => setCurrentPage(pageNum)}
                        aria-current={pagination.currentPage === pageNum ? 'page' : undefined}
                        className={`relative inline-flex items-center px-4 py-2 text-sm font-semibold ${
                          pagination.currentPage === pageNum
                            ? 'z-10 bg-dark-accent-blue text-white focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-dark-accent-blue'
                            : 'text-gray-900 dark:text-dark-text-primary ring-1 ring-inset ring-gray-300 dark:ring-dark-border-default hover:bg-gray-50 dark:hover:bg-dark-bg-hover focus:z-20 focus:outline-offset-0'
                        }`}
                      >
                        {pageNum}
                      </button>
                    );
                  })}
                  
                  <button
                    onClick={() => setCurrentPage(currentPage + 1)}
                    disabled={!pagination.hasNextPage}
                    className="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 dark:text-dark-text-secondary ring-1 ring-inset ring-gray-300 dark:ring-dark-border-default hover:bg-gray-50 dark:hover:bg-dark-bg-hover focus:z-20 focus:outline-offset-0 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <span className="sr-only">Next</span>
                    <ChevronRight className="h-5 w-5" aria-hidden="true" />
                  </button>
                </nav>
              </div>
            </div>
          </div>
        )}

        {/* Delete Confirmation Dialog */}
        {showDeleteConfirm && (
          <div className="fixed inset-0 z-50 overflow-y-auto">
            <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
              <div className="fixed inset-0 bg-gray-500 dark:bg-dark-bg-0 bg-opacity-75 dark:bg-opacity-75 transition-opacity" onClick={() => setShowDeleteConfirm(false)} />
              
              <div className="relative transform overflow-hidden rounded-lg bg-white dark:bg-dark-bg-1 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg dark:shadow-dark-md">
                <div className="bg-white dark:bg-dark-bg-1 px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
                  <div className="sm:flex sm:items-start">
                    <div className="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20 sm:mx-0 sm:h-10 sm:w-10">
                      <AlertTriangle className="h-6 w-6 text-red-600 dark:text-red-400" />
                    </div>
                    <div className="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left">
                      <h3 className="text-base font-semibold leading-6 text-gray-900 dark:text-dark-text-primary">
                        Delete {deleteTarget.type === 'single' ? 'Order' : 'Orders'}
                      </h3>
                      <div className="mt-2">
                        <p className="text-sm text-gray-500 dark:text-dark-text-secondary">
                          {deleteTarget.type === 'single' 
                            ? 'Are you sure you want to delete this order? This action cannot be undone.'
                            : `Are you sure you want to delete ${selectedOrders.size} selected order${selectedOrders.size > 1 ? 's' : ''}? This action cannot be undone.`
                          }
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="bg-gray-50 dark:bg-dark-bg-0 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
                  <button
                    type="button"
                    className="inline-flex w-full justify-center rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500 sm:ml-3 sm:w-auto"
                    onClick={handleDeleteConfirm}
                  >
                    Delete
                  </button>
                  <button
                    type="button"
                    className="mt-3 inline-flex w-full justify-center rounded-md bg-white dark:bg-dark-bg-2 px-3 py-2 text-sm font-semibold text-gray-900 dark:text-dark-text-primary shadow-sm ring-1 ring-inset ring-gray-300 dark:ring-dark-border-default hover:bg-gray-50 dark:hover:bg-dark-bg-hover sm:mt-0 sm:w-auto"
                    onClick={() => setShowDeleteConfirm(false)}
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Mark as Sent Confirmation Dialog */}
        {showMarkAsSentConfirm && (
          <div className="fixed inset-0 z-50 overflow-y-auto">
            <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
              <div className="fixed inset-0 bg-gray-500 dark:bg-dark-bg-0 bg-opacity-75 dark:bg-opacity-75 transition-opacity" onClick={() => {
                setShowMarkAsSentConfirm(false);
                setMarkAsSentOrderId(null);
              }} />
              
              <div className="relative transform overflow-hidden rounded-lg bg-white dark:bg-dark-bg-1 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg dark:shadow-dark-md">
                <div className="bg-white dark:bg-dark-bg-1 px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
                  <div className="sm:flex sm:items-start">
                    <div className="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/20 sm:mx-0 sm:h-10 sm:w-10">
                      <Mail className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div className="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left">
                      <h3 className="text-base font-semibold leading-6 text-gray-900 dark:text-dark-text-primary">
                        Mark Order as Sent
                      </h3>
                      <div className="mt-2">
                        <p className="text-sm text-gray-500 dark:text-dark-text-secondary">
                          Are you sure you want to mark this order as sent to supplier? This will update the status without sending an email to the supplier.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="bg-gray-50 dark:bg-dark-bg-0 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
                  <button
                    type="button"
                    className="inline-flex w-full justify-center rounded-md bg-dark-accent-blue px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 sm:ml-3 sm:w-auto"
                    onClick={handleMarkAsSentConfirm}
                  >
                    Mark as Sent
                  </button>
                  <button
                    type="button"
                    className="mt-3 inline-flex w-full justify-center rounded-md bg-white dark:bg-dark-bg-2 px-3 py-2 text-sm font-semibold text-gray-900 dark:text-dark-text-primary shadow-sm ring-1 ring-inset ring-gray-300 dark:ring-dark-border-default hover:bg-gray-50 dark:hover:bg-dark-bg-hover sm:mt-0 sm:w-auto"
                    onClick={() => {
                      setShowMarkAsSentConfirm(false);
                      setMarkAsSentOrderId(null);
                    }}
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Tracking Modal */}
        {showTrackingModal && currentTrackingOrder && (
          <div className="fixed inset-0 z-50 overflow-y-auto">
            <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
              <div className="fixed inset-0 bg-gray-500 dark:bg-dark-bg-0 bg-opacity-75 dark:bg-opacity-75 transition-opacity" onClick={() => {
                setShowTrackingModal(false);
                setCurrentTrackingOrder(null);
                setTrackingNumber('');
              }} />
              
              <div className="relative transform overflow-hidden rounded-lg bg-white dark:bg-dark-bg-1 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg dark:shadow-dark-md">
                <div className="bg-white dark:bg-dark-bg-1 px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
                  <div className="sm:flex sm:items-start">
                    <div className="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/20 sm:mx-0 sm:h-10 sm:w-10">
                      <Package className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div className="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left w-full">
                      <h3 className="text-base font-semibold leading-6 text-gray-900 dark:text-dark-text-primary">
                        Add Tracking Number
                      </h3>
                      <div className="mt-2">
                        <p className="text-sm text-gray-500 dark:text-dark-text-secondary mb-4">
                          Order: {currentTrackingOrder.orderNumber || `#${String(currentTrackingOrder.id).slice(0, 8)}`} - {currentTrackingOrder.customerName || 'N/A'}
                        </p>
                        <div className="mt-4">
                          <label htmlFor="tracking-number" className="block text-sm font-medium text-gray-700 dark:text-dark-text-primary mb-2">
                            Tracking Number
                          </label>
                          <input
                            type="text"
                            id="tracking-number"
                            value={trackingNumber}
                            onChange={(e) => setTrackingNumber(e.target.value)}
                            placeholder="Enter tracking number"
                            className="w-full rounded-lg border border-gray-300 dark:border-dark-border-default bg-white dark:bg-dark-bg-2 px-3 py-2 text-sm text-gray-900 dark:text-dark-text-primary shadow-sm focus:outline-none focus:border-dark-accent-blue dark:focus:border-indigo-400 focus:ring-2 focus:ring-dark-accent-blue dark:focus:ring-indigo-400 focus:ring-offset-0 transition-colors duration-200"
                            autoFocus
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="bg-gray-50 dark:bg-dark-bg-0 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
                  <button
                    type="button"
                    className="inline-flex w-full justify-center rounded-md bg-dark-accent-blue px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed sm:ml-3 sm:w-auto"
                    onClick={handleSaveTracking}
                    disabled={savingTracking}
                  >
                    {savingTracking ? 'Saving...' : 'Save'}
                  </button>
                  <button
                    type="button"
                    className="mt-3 inline-flex w-full justify-center rounded-md bg-white dark:bg-dark-bg-2 px-3 py-2 text-sm font-semibold text-gray-900 dark:text-dark-text-primary shadow-sm ring-1 ring-inset ring-gray-300 dark:ring-dark-border-default hover:bg-gray-50 dark:hover:bg-dark-bg-hover sm:mt-0 sm:w-auto"
                    onClick={() => {
                      setShowTrackingModal(false);
                      setCurrentTrackingOrder(null);
                      setTrackingNumber('');
                    }}
                    disabled={savingTracking}
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};