import { useState, useEffect } from 'react';
import { api } from '../api/client';

export default function ApiTest() {
  const [status, setStatus] = useState<string>('Testing...');
  const [details, setDetails] = useState<any>({});

  useEffect(() => {
    const testApi = async () => {
      try {
        // Test basic connection
        setStatus('Testing API connection...');
        const healthResponse = await api.get('/health').catch(e => ({ error: e.message }));
        
        // Test pricing endpoint
        setStatus('Testing pricing endpoint...');
        const pricingResponse = await api.get('/pricing');
        
        setDetails({
          apiBase: api.defaults.baseURL,
          healthCheck: 'error' in healthResponse ? healthResponse : healthResponse.data,
          pricingCount: pricingResponse.data?.pricing?.length || 0,
          firstProduct: pricingResponse.data?.pricing?.[0] || null
        });
        
        setStatus('✅ API is working!');
      } catch (error: any) {
        setStatus('❌ API connection failed');
        setDetails({
          error: error.message,
          stack: error.stack,
          apiBase: api.defaults.baseURL,
          suggestion: 'Make sure backend is running on port 3000'
        });
      }
    };

    testApi();
  }, []);

  return (
    <div style={{ padding: '20px', fontFamily: 'monospace' }}>
      <h1>API Connection Test</h1>
      <h2>{status}</h2>
      <pre>{JSON.stringify(details, null, 2)}</pre>
      <button onClick={() => window.location.reload()}>Retry</button>
    </div>
  );
}