import React, { useState, useEffect } from 'react';
import { Plus, Edit2, Trash2, GripVertical, X, Check } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { productsApi } from '../api/productsApi';
import type { Product, ProductVariant } from '../../../shared/types/product.types';
import { useToastContext } from '../contexts/ToastContext';
import { ProductModalV2 } from '../components/ProductModalV2';
import { useModalStatePersistence } from '../hooks/useModalStatePersistence';

export const ProductManagementV2: React.FC = () => {
  const { showSuccess, showError } = useToastContext();
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const modalState = useModalStatePersistence('product-modal');
  const [editingProductName, setEditingProductName] = useState<string | null>(null);
  const [newProductName, setNewProductName] = useState('');
  
  // Restore selected product from modal state if available
  useEffect(() => {
    if (modalState.formData?.selectedProduct && modalState.isOpen) {
      setSelectedProduct(modalState.formData.selectedProduct);
    }
  }, [modalState.formData, modalState.isOpen]);

  useEffect(() => {
    loadProducts();
  }, []);

  const loadProducts = async () => {
    try {
      setLoading(true);
      const data = await productsApi.getAll();
      setProducts(data);
    } catch (error) {
      console.error('Failed to load products:', error);
      showError('Failed to load products');
    } finally {
      setLoading(false);
    }
  };

  const handleAddProduct = () => {
    setSelectedProduct(null);
    modalState.setIsOpen(true, { selectedProduct: null });
  };

  const handleEditProduct = (product: Product) => {
    setSelectedProduct(product);
    modalState.setIsOpen(true, { selectedProduct: product });
  };

  const handleDeleteProduct = async (productId: string, productName: string) => {
    if (!confirm(`Are you sure you want to delete ${productName} and all its variants?`)) {
      return;
    }

    try {
      await productsApi.delete(productId);
      showSuccess(`${productName} deleted successfully`);
      loadProducts();
    } catch (error: any) {
      showError(error.response?.data?.error || 'Failed to delete product');
    }
  };

  const handleDeleteVariant = async (variantId: string, variantName: string) => {
    if (!confirm(`Are you sure you want to delete ${variantName}?`)) {
      return;
    }

    try {
      await productsApi.deleteVariant(variantId);
      showSuccess('Variant deleted successfully');
      loadProducts();
    } catch (error: any) {
      showError(error.response?.data?.error || 'Failed to delete variant');
    }
  };

  const handleEditProductName = async (productId: string) => {
    if (!newProductName.trim()) {
      setEditingProductName(null);
      return;
    }

    try {
      await productsApi.updateName(productId, newProductName);
      showSuccess('Product name updated');
      loadProducts();
      setEditingProductName(null);
    } catch (error: any) {
      showError(error.response?.data?.error || 'Failed to update product name');
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-dark-bg-0 py-8">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-center h-64">
            <p className="text-gray-500">Loading products...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-dark-bg-0 py-8">
      <div className="container mx-auto px-4">
        <div className="mb-8 flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Products</h1>
            <p className="mt-2 text-gray-600 dark:text-gray-400">
              Manage your product catalog with variants
            </p>
          </div>
          <button
            onClick={handleAddProduct}
            className="flex items-center justify-center px-4 py-2 bg-dark-accent-blue text-white rounded-lg
                     hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 
                     focus:ring-dark-accent-blue dark:focus:ring-offset-dark-bg-3"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Product
          </button>
        </div>

        {/* Products List */}
        <div className="space-y-6">
          {products.map((product) => (
            <Card key={product.id} className="overflow-hidden">
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  {editingProductName === product.id ? (
                    <div className="flex items-center gap-2 flex-1">
                      <input
                        type="text"
                        value={newProductName}
                        onChange={(e) => setNewProductName(e.target.value)}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') handleEditProductName(product.id);
                          if (e.key === 'Escape') setEditingProductName(null);
                        }}
                        className="px-3 py-1 border border-gray-300 dark:border-dark-border-default rounded
                                 bg-white dark:bg-dark-bg-2 text-gray-900 dark:text-dark-text-primary
                                 focus:outline-none focus:ring-2 focus:ring-dark-accent-blue"
                        autoFocus
                      />
                      <button
                        onClick={() => handleEditProductName(product.id)}
                        className="p-1 text-green-600 hover:text-green-700"
                      >
                        <Check className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => setEditingProductName(null)}
                        className="p-1 text-gray-500 hover:text-gray-700"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    </div>
                  ) : (
                    <CardTitle className="text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-2">
                      {product.name}
                      <button
                        onClick={() => {
                          setEditingProductName(product.id);
                          setNewProductName(product.name);
                        }}
                        className="opacity-0 group-hover:opacity-100 transition-opacity p-1 
                                 text-gray-500 hover:text-gray-700"
                        title="Edit name"
                      >
                        <Edit2 className="h-3 w-3" />
                      </button>
                    </CardTitle>
                  )}
                  <div className="flex items-center gap-2">
                    <button
                      onClick={() => handleEditProduct(product)}
                      className="p-2 text-gray-600 dark:text-gray-400 hover:text-dark-accent-blue 
                               dark:hover:text-dark-accent-blue transition-colors"
                      title="Edit product"
                    >
                      <Edit2 className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleDeleteProduct(product.id, product.name)}
                      className="p-2 text-gray-600 dark:text-gray-400 hover:text-red-600 
                               dark:hover:text-red-400 transition-colors"
                      title="Delete product"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-gray-200 dark:border-dark-border-subtle">
                        <th className="text-left py-2 px-3 text-sm font-medium text-gray-700 dark:text-gray-300">
                          Variant
                        </th>
                        <th className="text-left py-2 px-3 text-sm font-medium text-gray-700 dark:text-gray-300">
                          Code
                        </th>
                        <th className="text-right py-2 px-3 text-sm font-medium text-gray-700 dark:text-gray-300">
                          Buying Price
                        </th>
                        <th className="text-right py-2 px-3 text-sm font-medium text-gray-700 dark:text-gray-300">
                          Selling Price
                        </th>
                        <th className="text-right py-2 px-3 text-sm font-medium text-gray-700 dark:text-gray-300">
                          Margin
                        </th>
                        <th className="w-10"></th>
                      </tr>
                    </thead>
                    <tbody>
                      {product.variants
                        .sort((a, b) => a.sellingPrice - b.sellingPrice)
                        .map((variant) => (
                        <VariantRow
                          key={variant.id}
                          variant={variant}
                          onUpdate={loadProducts}
                          onDelete={() => handleDeleteVariant(variant.id, `${product.name} ${variant.dose}`)}
                        />
                      ))}
                    </tbody>
                  </table>
                </div>
                
                {/* Add Variant Button */}
                <div className="mt-4 pt-4 border-t border-gray-200 dark:border-dark-border-subtle">
                  <button
                    onClick={() => handleEditProduct(product)}
                    className="text-sm text-dark-accent-blue hover:text-blue-700 font-medium"
                  >
                    + Add Variant
                  </button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Empty State */}
        {products.length === 0 && (
          <Card className="mt-8">
            <CardContent className="text-center py-12">
              <p className="text-gray-500 dark:text-gray-400 mb-4">
                No products found. Add your first product to get started.
              </p>
              <button
                onClick={handleAddProduct}
                className="inline-flex items-center px-4 py-2 bg-dark-accent-blue text-white rounded-lg
                         hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 
                         focus:ring-dark-accent-blue"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add First Product
              </button>
            </CardContent>
          </Card>
        )}

        {/* Product Modal */}
        <ProductModalV2
          isOpen={modalState.isOpen}
          onClose={() => modalState.handleClose()}
          onSave={loadProducts}
          product={selectedProduct}
        />
      </div>
    </div>
  );
};

// Variant Row Component
interface VariantRowProps {
  variant: ProductVariant;
  onUpdate: () => void;
  onDelete: () => void;
}

const VariantRow: React.FC<VariantRowProps> = ({ variant, onUpdate, onDelete }) => {
  const { showSuccess, showError } = useToastContext();
  const [isEditing, setIsEditing] = useState(false);
  const [editData, setEditData] = useState({
    dose: variant.dose,
    buyingPrice: variant.buyingPrice.toString(),
    sellingPrice: variant.sellingPrice.toString()
  });

  const handleSave = async () => {
    try {
      const updates = {
        dose: editData.dose,
        buyingPrice: parseFloat(editData.buyingPrice),
        sellingPrice: parseFloat(editData.sellingPrice)
      };

      if (updates.sellingPrice <= updates.buyingPrice) {
        showError('Selling price must be greater than buying price');
        return;
      }

      await productsApi.updateVariant(variant.id, updates);
      showSuccess('Variant updated successfully');
      setIsEditing(false);
      onUpdate();
    } catch (error: any) {
      showError(error.response?.data?.error || 'Failed to update variant');
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price);
  };

  if (isEditing) {
    return (
      <tr className="border-b border-gray-100 dark:border-dark-border-subtle">
        <td className="py-2 px-3">
          <input
            type="text"
            value={editData.dose}
            onChange={(e) => setEditData({ ...editData, dose: e.target.value })}
            className="w-full px-2 py-1 border border-gray-300 dark:border-dark-border-default rounded
                     bg-white dark:bg-dark-bg-2 text-gray-900 dark:text-dark-text-primary
                     focus:outline-none focus:ring-2 focus:ring-dark-accent-blue"
          />
        </td>
        <td className="py-2 px-3">
          <span className="font-mono text-sm bg-gray-100 dark:bg-dark-bg-hover px-2 py-1 rounded">
            {variant.code}
          </span>
        </td>
        <td className="py-2 px-3">
          <input
            type="number"
            value={editData.buyingPrice}
            onChange={(e) => setEditData({ ...editData, buyingPrice: e.target.value })}
            className="w-24 text-right px-2 py-1 border border-gray-300 dark:border-dark-border-default rounded
                     bg-white dark:bg-dark-bg-2 text-gray-900 dark:text-dark-text-primary
                     focus:outline-none focus:ring-2 focus:ring-dark-accent-blue"
          />
        </td>
        <td className="py-2 px-3">
          <input
            type="number"
            value={editData.sellingPrice}
            onChange={(e) => setEditData({ ...editData, sellingPrice: e.target.value })}
            className="w-24 text-right px-2 py-1 border border-gray-300 dark:border-dark-border-default rounded
                     bg-white dark:bg-dark-bg-2 text-gray-900 dark:text-dark-text-primary
                     focus:outline-none focus:ring-2 focus:ring-dark-accent-blue"
          />
        </td>
        <td className="py-2 px-3 text-right">
          <span className="text-green-600 dark:text-green-400 font-medium">
            {variant.profitMargin?.toFixed(1)}%
          </span>
        </td>
        <td className="py-2 px-3">
          <div className="flex items-center gap-1">
            <button
              onClick={handleSave}
              className="p-1 text-green-600 hover:text-green-700"
              title="Save"
            >
              <Check className="h-4 w-4" />
            </button>
            <button
              onClick={() => setIsEditing(false)}
              className="p-1 text-gray-500 hover:text-gray-700"
              title="Cancel"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </td>
      </tr>
    );
  }

  return (
    <tr className="border-b border-gray-100 dark:border-dark-border-subtle hover:bg-gray-50 dark:hover:bg-dark-bg-hover">
      <td className="py-3 px-3 text-gray-900 dark:text-dark-text-primary">
        {variant.dose}
      </td>
      <td className="py-3 px-3">
        <span className="font-mono text-sm bg-gray-100 dark:bg-dark-bg-hover px-2 py-1 rounded">
          {variant.code}
        </span>
      </td>
      <td className="py-3 px-3 text-right text-gray-900 dark:text-dark-text-primary">
        {formatPrice(variant.buyingPrice)}
      </td>
      <td className="py-3 px-3 text-right text-gray-900 dark:text-dark-text-primary">
        {formatPrice(variant.sellingPrice)}
      </td>
      <td className="py-3 px-3 text-right">
        <span className="text-green-600 dark:text-green-400 font-medium">
          {variant.profitMargin?.toFixed(1)}%
        </span>
      </td>
      <td className="py-3 px-3">
        <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
          <button
            onClick={() => setIsEditing(true)}
            className="p-1 text-gray-500 hover:text-gray-700"
            title="Edit"
          >
            <Edit2 className="h-3 w-3" />
          </button>
          <button
            onClick={onDelete}
            className="p-1 text-gray-500 hover:text-red-600"
            title="Delete"
          >
            <Trash2 className="h-3 w-3" />
          </button>
        </div>
      </td>
    </tr>
  );
};