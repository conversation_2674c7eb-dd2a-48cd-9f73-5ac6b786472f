import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useSearchParams } from 'react-router-dom';
import { ordersApi, exportsApi, pricingApi, type PricingItem } from '../api/client';
import type { Order } from '../../../shared/types/order.types';
import { useToastContext } from '../contexts/ToastContext';
import { Package, Edit2, ArrowLeft, ArrowRight, FileText, Table, Mail, CheckCircle, Check, X } from 'lucide-react';

import { PaymentCostCards } from '../components/PaymentCostCards';

export const OrderDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { showSuccess, showError } = useToastContext();
  const [order, setOrder] = useState<Order | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showTrackingModal, setShowTrackingModal] = useState(false);
  const [trackingNumber, setTrackingNumber] = useState('');
  const [savingTracking, setSavingTracking] = useState(false);
  const [sendingToSupplier, setSendingToSupplier] = useState(false);
  const [trackingStatus, setTrackingStatus] = useState<{
    status: string;
    statusCode: string;
    lastUpdate?: string;
    lastEventDescription?: string;
    trackingUrl: string;
  } | null>(null);
  const [loadingStatus, setLoadingStatus] = useState(false);
  const [showMarkAsSentConfirm, setShowMarkAsSentConfirm] = useState(false);
  const [navigation, setNavigation] = useState<{ prevId: string | null; nextId: string | null }>({ prevId: null, nextId: null });
  const [loadingNavigation, setLoadingNavigation] = useState(false);
  const [pricing, setPricing] = useState<PricingItem[]>([]);
  const [btcRate, setBtcRate] = useState<number | null>(null);

  useEffect(() => {
    if (id) {
      fetchOrder();
      fetchNavigation();
      fetchPricingData();
    }
  }, [id]);

  useEffect(() => {
    if (searchParams.get('edit') === 'tracking') {
      setShowTrackingModal(true);
    }
  }, [searchParams]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Don't navigate if user is typing in an input field
      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
        return;
      }

      if (e.key === 'ArrowLeft' && navigation.prevId) {
        navigate(`/orders/${navigation.prevId}`);
      } else if (e.key === 'ArrowRight' && navigation.nextId) {
        navigate(`/orders/${navigation.nextId}`);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [navigation, navigate]);

  const fetchOrder = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('Fetching order with ID:', id);
      const data = await ordersApi.getById(id!);
      console.log('Order data received:', data);
      setOrder(data);
      if (data.tracking17) {
        setTrackingNumber(data.tracking17);
        // Fetch tracking status if tracking number exists
        fetchTrackingStatus();
      }
    } catch (error: any) {
      console.error('Failed to fetch order:', error);
      console.error('Error details:', {
        message: error?.message,
        response: error?.response,
        status: error?.response?.status,
        data: error?.response?.data
      });
      
      // More specific error messages
      if (error?.response?.status === 404) {
        setError('Order not found');
      } else if (error?.response?.status >= 500) {
        setError('Server error. Please check if the backend is running.');
      } else if (error?.code === 'ERR_NETWORK') {
        setError('Network error. Please check your connection and ensure the backend server is running.');
      } else {
        setError(error?.response?.data?.error || error?.message || 'Failed to load order');
      }
    } finally {
      setLoading(false);
    }
  };

  const fetchTrackingStatus = async () => {
    if (!id) return;
    
    try {
      setLoadingStatus(true);
      const status = await ordersApi.getTrackingStatus(id);
      setTrackingStatus(status);
    } catch (error) {
      console.error('Failed to fetch tracking status:', error);
      // Don't show error to user - tracking status is optional
    } finally {
      setLoadingStatus(false);
    }
  };

  const fetchNavigation = async () => {
    if (!id) return;
    
    try {
      setLoadingNavigation(true);
      const nav = await ordersApi.getNavigation(id);
      setNavigation(nav);
    } catch (error) {
      console.error('Failed to fetch navigation:', error);
      // Don't show error to user - navigation is optional
    } finally {
      setLoadingNavigation(false);
    }
  };

  const fetchPricingData = async () => {
    try {
      const pricingData = await pricingApi.getAll();
      setPricing(pricingData);
      
      // Fetch BTC rate from backend
      try {
        const rate = await pricingApi.getBtcRate();
        setBtcRate(rate);
      } catch (error) {
        console.error('Failed to fetch BTC rate:', error);
        setBtcRate(100000); // Fallback rate
      }
    } catch (error) {
      console.error('Failed to fetch pricing:', error);
    }
  };

  const formatDate = (date: Date | string) => {
    const d = new Date(date);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZone: 'Asia/Shanghai',
      timeZoneName: 'short'
    }).format(d);
  };

  const formatShortDate = (date: Date | string) => {
    const d = new Date(date);
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit'
    }).format(d);
  };

  const handleDownloadPdf = async () => {
    try {
      const blob = await exportsApi.downloadPdf(id!);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `order-${id}.pdf`;
      a.click();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Failed to download PDF:', error);
      showError('Failed to download PDF. Please try again.');
    }
  };

  const handleDownloadExcel = async () => {
    try {
      const blob = await exportsApi.downloadExcel(id!);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `order-${id}.xlsx`;
      a.click();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Failed to download Excel:', error);
      showError('Failed to download Excel file. Please try again.');
    }
  };

  const handleSaveTracking = async () => {
    try {
      setSavingTracking(true);
      const updatedOrder = await ordersApi.updateTracking(id!, trackingNumber);
      setOrder(updatedOrder);
      setShowTrackingModal(false);
      showSuccess(
        order?.tracking17 
          ? 'Tracking number updated successfully' 
          : 'Tracking number added successfully'
      );
      // Fetch tracking status after saving
      fetchTrackingStatus();
    } catch (error) {
      console.error('Failed to save tracking:', error);
      showError('Failed to save tracking number. Please try again.');
    } finally {
      setSavingTracking(false);
    }
  };

  const getShippingStatusLabel = (order: Order): string | null => {
    if (!order.tracking17) return null;
    if (order.shippingStatus) return order.shippingStatus;
    
    // Fallback to API status if available
    if (trackingStatus?.status) {
      if (trackingStatus.status === 'Delivered') return 'Delivered';
      if (['In Transit', 'Out for Delivery', 'Pick Up'].includes(trackingStatus.status)) return 'In Transit';
      return 'Info Received';
    }
    
    return 'Info Received';
  };

  const getShippingStatusColor = (status: string): string => {
    switch (status) {
      case 'Delivered':
        return 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-200';
      case 'In Transit':
        return 'bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200';
      case 'Info Received':
        return 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-200';
      default:
        return 'bg-gray-100 dark:bg-gray-900/20 text-gray-800 dark:text-gray-200';
    }
  };

  const handleSendToSupplier = async () => {
    try {
      setSendingToSupplier(true);
      const result = await ordersApi.sendToSupplier(id!);
      setOrder(result.order);
      showSuccess('Order successfully sent to supplier!');
    } catch (error) {
      console.error('Failed to send order to supplier:', error);
      showError('Failed to send order to supplier. Please try again.');
    } finally {
      setSendingToSupplier(false);
    }
  };

  const handleMarkAsSent = () => {
    setShowMarkAsSentConfirm(true);
  };

  const handleMarkAsSentConfirm = async () => {
    try {
      const result = await ordersApi.markAsSent(id!);
      setOrder(result.order);
      showSuccess('Order marked as sent to supplier');
      setShowMarkAsSentConfirm(false);
    } catch (error) {
      console.error('Failed to mark order as sent:', error);
      showError('Failed to mark order as sent. Please try again.');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-dark-bg-0 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-center h-64">
            <div className="text-gray-500 dark:text-dark-text-secondary">Loading order details...</div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !order) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-dark-bg-0 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-dark-text-primary mb-4">
              {error?.includes('Network') || error?.includes('Server') ? 'Connection Error' : 'Order Not Found'}
            </h2>
            <p className="text-gray-600 dark:text-dark-text-secondary mb-4">{error || 'The requested order could not be found.'}</p>
            
            {/* Show troubleshooting tips for connection errors */}
            {(error?.includes('Network') || error?.includes('Server')) && (
              <div className="mt-6 text-left max-w-md mx-auto bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-2">Troubleshooting Tips:</h3>
                <ul className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1 list-disc list-inside">
                  <li>Ensure the backend server is running (npm run dev:api)</li>
                  <li>Check if you can access: <code className="text-xs bg-yellow-100 dark:bg-yellow-800/50 px-1 rounded">http://{window.location.hostname}:3000/health</code></li>
                  <li>Open browser console (F12) for detailed error messages</li>
                  <li>Verify PostgreSQL database is running</li>
                </ul>
              </div>
            )}
            
            <button
              onClick={() => navigate('/orders')}
              className="mt-8 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-dark-accent-blue hover:bg-dark-accent-blue/90"
            >
              Back to Orders
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-dark-bg-0 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8 flex items-center justify-between">
          <button
            onClick={() => navigate('/orders')}
            className="text-gray-500 dark:text-dark-text-secondary hover:text-gray-700 dark:hover:text-dark-text-primary flex items-center"
          >
            <ArrowLeft className="w-5 h-5 mr-1" />
            Back to Orders
          </button>
          
          {/* Navigation buttons */}
          <div className="flex items-center space-x-2">
            <button
              onClick={() => navigation.prevId && navigate(`/orders/${navigation.prevId}`)}
              disabled={!navigation.prevId || loadingNavigation}
              className={`inline-flex items-center p-2 border border-gray-300 dark:border-dark-border-default rounded-md text-sm font-medium ${
                navigation.prevId && !loadingNavigation
                  ? 'text-gray-700 dark:text-dark-text-primary bg-white dark:bg-dark-bg-2 hover:bg-gray-50 dark:hover:bg-dark-bg-hover cursor-pointer'
                  : 'text-gray-300 dark:text-dark-text-disabled bg-gray-50 dark:bg-dark-bg-0 cursor-not-allowed'
              } focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors`}
              title={navigation.prevId ? 'Previous order (←)' : 'No previous order'}
            >
              <ArrowLeft className="w-4 h-4" />
              <span className="sr-only">Previous</span>
            </button>
            
            <button
              onClick={() => navigation.nextId && navigate(`/orders/${navigation.nextId}`)}
              disabled={!navigation.nextId || loadingNavigation}
              className={`inline-flex items-center p-2 border border-gray-300 dark:border-dark-border-default rounded-md text-sm font-medium ${
                navigation.nextId && !loadingNavigation
                  ? 'text-gray-700 dark:text-dark-text-primary bg-white dark:bg-dark-bg-2 hover:bg-gray-50 dark:hover:bg-dark-bg-hover cursor-pointer'
                  : 'text-gray-300 dark:text-dark-text-disabled bg-gray-50 dark:bg-dark-bg-0 cursor-not-allowed'
              } focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors`}
              title={navigation.nextId ? 'Next order (→)' : 'No next order'}
            >
              <ArrowRight className="w-4 h-4" />
              <span className="sr-only">Next</span>
            </button>
          </div>
        </div>

        <div className="bg-white dark:bg-dark-bg-1 shadow dark:shadow-dark-md overflow-hidden sm:rounded-lg">
          <div className="px-4 py-5 sm:px-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-dark-text-primary">{order.orderNumber || `Order #${order.id}`}</h1>
              <p className="mt-1 text-sm text-gray-500 dark:text-dark-text-secondary">
                Placed on {formatDate(order.placedAt)}
              </p>
            </div>
            
            {/* Action buttons positioned below order info */}
            <div className="mt-4 flex flex-wrap gap-2">
              {/* PDF button - icon only on mobile, with text on desktop */}
              <button
                onClick={handleDownloadPdf}
                title="Download PDF"
                className="inline-flex items-center justify-center p-2 sm:px-3 sm:py-2 border border-gray-300 dark:border-dark-border-default shadow-sm dark:shadow-dark-sm text-sm font-medium rounded-md text-gray-700 dark:text-dark-text-primary bg-white dark:bg-dark-bg-2 hover:bg-gray-50 dark:hover:bg-dark-bg-hover focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-dark-accent-blue transition-colors"
              >
                <FileText className="w-5 h-5" />
                <span className="hidden sm:inline ml-2">PDF</span>
              </button>
              
              {/* Excel button - icon only on mobile, with text on desktop */}
              <button
                onClick={handleDownloadExcel}
                title="Download Excel"
                className="inline-flex items-center justify-center p-2 sm:px-3 sm:py-2 border border-gray-300 dark:border-dark-border-default shadow-sm dark:shadow-dark-sm text-sm font-medium rounded-md text-gray-700 dark:text-dark-text-primary bg-white dark:bg-dark-bg-2 hover:bg-gray-50 dark:hover:bg-dark-bg-hover focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-dark-accent-blue transition-colors"
              >
                <Table className="w-5 h-5" />
                <span className="hidden sm:inline ml-2">Excel</span>
              </button>
              
              {/* Edit button - only if no tracking number */}
              {!order.tracking17 && (
                <button
                  onClick={() => navigate(`/orders/${order.id}/edit`)}
                  title="Edit Order"
                  className="inline-flex items-center justify-center p-2 sm:px-3 sm:py-2 border border-gray-300 dark:border-dark-border-default shadow-sm dark:shadow-dark-sm text-sm font-medium rounded-md text-gray-700 dark:text-dark-text-primary bg-white dark:bg-dark-bg-2 hover:bg-gray-50 dark:hover:bg-dark-bg-hover focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-dark-accent-blue transition-colors"
                >
                  <Edit2 className="w-5 h-5" />
                  <span className="hidden sm:inline ml-2">Edit</span>
                </button>
              )}
              
              {/* Send to Supplier button - primary action, slightly larger */}
              {!order.sentToSupplier && (
                <button
                  onClick={handleSendToSupplier}
                  disabled={sendingToSupplier}
                  className="inline-flex items-center px-4 py-2 sm:px-5 sm:py-2.5 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-dark-accent-blue hover:bg-dark-accent-blue/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-dark-accent-blue disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  <Mail className="w-4 h-4 mr-2" />
                  {sendingToSupplier ? 'Sending...' : 'Send to Supplier'}
                </button>
              )}
              
              {/* Sent status - displayed when already sent */}
              {order.sentToSupplier && (
                <div className="inline-flex items-center px-3 py-2 text-sm text-green-700 dark:text-green-400 bg-green-50 dark:bg-green-900/20 rounded-md">
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Sent to Supplier
                </div>
              )}
            </div>
          </div>

          <div className="border-t border-gray-200 dark:border-dark-border-subtle">
            <dl>
              <div className="bg-gray-50 dark:bg-dark-bg-0 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500 dark:text-dark-text-secondary">Customer Name</dt>
                <dd className="mt-1 text-sm text-gray-900 dark:text-dark-text-primary sm:mt-0 sm:col-span-2">{order.customerName}</dd>
              </div>
              {order.email && (
                <div className="bg-white dark:bg-dark-bg-1 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-medium text-gray-500 dark:text-dark-text-secondary">Email</dt>
                  <dd className="mt-1 text-sm text-gray-900 dark:text-dark-text-primary sm:mt-0 sm:col-span-2">
                    <a href={`mailto:${order.email}`} className="text-indigo-600 dark:text-indigo-400 hover:text-indigo-500 dark:hover:text-indigo-300">
                      {order.email}
                    </a>
                  </dd>
                </div>
              )}
              <div className={order.email ? "bg-gray-50 dark:bg-dark-bg-0 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6" : "bg-white dark:bg-dark-bg-1 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6"}>
                <dt className="text-sm font-medium text-gray-500 dark:text-dark-text-secondary">Shipping Address</dt>
                <dd className="mt-1 text-sm text-gray-900 dark:text-dark-text-primary sm:mt-0 sm:col-span-2">
                  {order.street1}<br />
                  {order.street2 && <>{order.street2}<br /></>}
                  {order.city}, {order.state} {order.postalCode}<br />
                  {order.country}
                </dd>
              </div>
              <div className={order.email ? "bg-white dark:bg-dark-bg-1 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6" : "bg-gray-50 dark:bg-dark-bg-0 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6"}>
                <dt className="text-sm font-medium text-gray-500 dark:text-dark-text-secondary">Payment Method</dt>
                <dd className="mt-1 text-sm text-gray-900 dark:text-dark-text-primary sm:mt-0 sm:col-span-2">
                  <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                    order.paymentMethod === 'BTC' ? 'bg-orange-100 dark:bg-orange-900/20 text-orange-800 dark:text-orange-200' :
                    order.paymentMethod === 'USDT' ? 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-200' :
                    order.paymentMethod === 'USDC' ? 'bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200' :
                    'bg-gray-100 dark:bg-dark-bg-2 text-gray-800 dark:text-dark-text-primary'
                  }`}>
                    {order.paymentMethod}
                  </span>
                  {order.paymentUrl && (
                    <a 
                      href={order.paymentUrl} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="ml-3 text-indigo-600 dark:text-indigo-400 hover:text-indigo-500 dark:hover:text-indigo-300"
                    >
                      View Transaction
                    </a>
                  )}
                </dd>
              </div>
              <div className={order.email ? "bg-gray-50 dark:bg-dark-bg-0 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6" : "bg-white dark:bg-dark-bg-1 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6"}>
                <dt className="text-sm font-medium text-gray-500 dark:text-dark-text-secondary">Shipping Status</dt>
                <dd className="mt-1 text-sm text-gray-900 dark:text-dark-text-primary sm:mt-0 sm:col-span-2">
                  {order.tracking17 ? (
                    <div className="space-y-2">
                      <div className="flex items-center flex-wrap gap-2">
                        <span className="font-mono">{order.tracking17}</span>
                        {trackingStatus?.status !== 'Delivered' && (
                          <button
                            onClick={() => setShowTrackingModal(true)}
                            className="inline-flex items-center text-indigo-600 dark:text-indigo-400 hover:text-indigo-500 dark:hover:text-indigo-300"
                            title="Edit tracking number"
                          >
                            <Edit2 className="w-4 h-4" />
                          </button>
                        )}
                        {trackingStatus && (
                          <a
                            href={trackingStatus.trackingUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="inline-flex items-center text-indigo-600 dark:text-indigo-400 hover:text-indigo-500 dark:hover:text-indigo-300"
                            title="Track on parcelsapp.com"
                          >
                            <Package className="w-4 h-4" />
                          </a>
                        )}
                      </div>
                      {loadingStatus && (
                        <div className="text-xs text-gray-500 dark:text-dark-text-secondary">
                          Checking status...
                        </div>
                      )}
                      {trackingStatus && !loadingStatus && (
                        <div className="space-y-1">
                          <div className="flex items-center gap-2">
                            <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getShippingStatusColor(getShippingStatusLabel(order) || 'Info Received')}`}>
                              {getShippingStatusLabel(order)}
                            </span>
                            {trackingStatus.lastUpdate && (
                              <span 
                                className="text-xs text-gray-500 dark:text-dark-text-secondary cursor-help relative group"
                                title={`Last updated: ${formatDate(trackingStatus.lastUpdate)}`}
                              >
                                Updated: {formatShortDate(trackingStatus.lastUpdate)}
                                <span className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 px-2 py-1 text-xs text-white bg-gray-800 dark:bg-gray-700 rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
                                  {formatDate(trackingStatus.lastUpdate)}
                                </span>
                              </span>
                            )}
                          </div>
                          {trackingStatus.lastEventDescription && (
                            <div className="text-sm text-gray-600 dark:text-dark-text-secondary italic">
                              "{trackingStatus.lastEventDescription}"
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  ) : (
                    <button
                      onClick={() => setShowTrackingModal(true)}
                      className="inline-flex items-center text-indigo-600 dark:text-indigo-400 hover:text-indigo-500 dark:hover:text-indigo-300"
                    >
                      <Edit2 className="w-4 h-4 mr-1" />
                      Add Tracking Number
                    </button>
                  )}
                </dd>
              </div>
              <div className={order.email ? "bg-white dark:bg-dark-bg-1 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6" : "bg-gray-50 dark:bg-dark-bg-0 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6"}>
                <dt className="text-sm font-medium text-gray-500 dark:text-dark-text-secondary">Supplier Status</dt>
                <dd className="mt-1 text-sm text-gray-900 dark:text-dark-text-primary sm:mt-0 sm:col-span-2">
                  {order.sentToSupplier ? (
                    <div className="flex items-center">
                      <div className="flex items-center">
                        <div className="flex items-center justify-center w-6 h-6 rounded-full bg-green-100 dark:bg-green-900/20 mr-2">
                          <Check className="w-4 h-4 text-green-600 dark:text-green-400" />
                        </div>
                        <span className="text-green-700 dark:text-dark-accent-green">
                          Sent to supplier on {order.sentAt ? formatDate(order.sentAt) : 'N/A'}
                        </span>
                      </div>
                    </div>
                  ) : (
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="flex items-center justify-center w-6 h-6 rounded-full bg-red-100 dark:bg-red-900/20 mr-2">
                          <X className="w-4 h-4 text-red-600 dark:text-red-400" />
                        </div>
                        <span className="text-gray-700 dark:text-dark-text-primary">Not yet sent to supplier</span>
                      </div>
                      <button
                        onClick={handleMarkAsSent}
                        className="ml-4 text-sm text-indigo-600 dark:text-indigo-400 hover:text-indigo-500 dark:hover:text-indigo-300"
                      >
                        Mark as Sent
                      </button>
                    </div>
                  )}
                </dd>
              </div>
            </dl>
          </div>

          <div className="px-4 py-5 sm:px-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-dark-text-primary mb-4">Order Items</h3>
            
            {/* Mobile view - Cards */}
            <div className="sm:hidden space-y-4">
              {order.items.map((item, index) => (
                <div key={index} className="bg-white dark:bg-dark-bg-1 shadow dark:shadow-dark-md rounded-lg p-4 border border-gray-200 dark:border-dark-border-default">
                  <div className="space-y-3">
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="text-xs font-medium text-gray-500 dark:text-dark-text-secondary uppercase tracking-wider mb-1">Product</p>
                        <p className="text-sm font-medium text-gray-900 dark:text-dark-text-primary">{item.code}</p>
                      </div>
                      <div className="text-right">
                        <p className="text-xs font-medium text-gray-500 dark:text-dark-text-secondary uppercase tracking-wider mb-1">Dose</p>
                        <p className="text-sm text-gray-900 dark:text-dark-text-primary">{item.dose}</p>
                      </div>
                    </div>
                    
                    <div className="flex justify-between items-start pt-3 border-t border-gray-100 dark:border-dark-border-subtle">
                      <div>
                        <p className="text-xs font-medium text-gray-500 dark:text-dark-text-secondary uppercase tracking-wider mb-1">Quantity</p>
                        <p className="text-sm text-gray-900 dark:text-dark-text-primary">{item.qty}</p>
                      </div>
                      <div className="text-right">
                        <p className="text-xs font-medium text-gray-500 dark:text-dark-text-secondary uppercase tracking-wider mb-1">Unit Price</p>
                        <p className="text-sm text-gray-900 dark:text-dark-text-primary">${item.buyingPrice.toFixed(2)}</p>
                      </div>
                    </div>
                    
                    <div className="pt-3 border-t border-gray-100 dark:border-dark-border-subtle">
                      <div className="flex justify-between items-center">
                        <p className="text-xs font-medium text-gray-500 dark:text-dark-text-secondary uppercase tracking-wider">Total</p>
                        <p className="text-base font-medium text-gray-900 dark:text-dark-text-primary">${(item.buyingPrice * item.qty).toFixed(2)}</p>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            
            {/* Desktop view - Table */}
            <div className="hidden sm:block overflow-hidden shadow dark:shadow-dark-md ring-1 ring-black dark:ring-dark-border-default ring-opacity-5 dark:ring-opacity-20 md:rounded-lg">
              <table className="min-w-full divide-y divide-gray-300 dark:divide-dark-border-default">
                <thead className="bg-gray-50 dark:bg-dark-bg-2">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-dark-text-secondary uppercase tracking-wider">
                      Product
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-dark-text-secondary uppercase tracking-wider">
                      Dose
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-dark-text-secondary uppercase tracking-wider">
                      Quantity
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-dark-text-secondary uppercase tracking-wider">
                      Unit Price
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-dark-text-secondary uppercase tracking-wider">
                      Total
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-dark-bg-1 divide-y divide-gray-200 dark:divide-dark-border-subtle">
                  {order.items.map((item, index) => (
                    <tr key={index}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text-primary">
                        {item.code}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text-primary">
                        {item.dose}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text-primary">
                        {item.qty}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text-primary">
                        ${item.buyingPrice.toFixed(2)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text-primary">
                        ${(item.buyingPrice * item.qty).toFixed(2)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          <div className="bg-gray-50 dark:bg-dark-bg-0 px-4 py-5 sm:px-6">
            <PaymentCostCards
              items={order.items}
              pricing={pricing}
              btcRate={btcRate}
              order={order}
            />
          </div>          
          {/* Edit History */}
          {order.isEdited && order.history && order.history.length > 0 && (
            <div className="px-4 py-5 sm:px-6 border-t border-gray-200 dark:border-dark-border-default">
              <h3 className="text-lg font-medium text-gray-900 dark:text-dark-text-primary mb-4">
                Edit History
              </h3>
              
              {/* Group changes by edit time */}
              <div className="space-y-4">
                {(() => {
                  // Group history entries by edit timestamp (within 1 minute)
                  const groupedHistory = order.history.reduce((groups, entry) => {
                    const time = new Date(entry.editedAt).getTime();
                    const groupKey = Math.floor(time / 60000); // Group by minute
                    if (!groups[groupKey]) {
                      groups[groupKey] = {
                        time: entry.editedAt,
                        changes: []
                      };
                    }
                    groups[groupKey].changes.push(entry);
                    return groups;
                  }, {} as Record<number, { time: Date | string, changes: typeof order.history }>);
                  
                  return Object.values(groupedHistory).map((group, groupIndex) => (
                    <div key={groupIndex} className="bg-gray-50 dark:bg-dark-bg-0 rounded-lg p-4">
                      <p className="text-sm text-gray-500 dark:text-dark-text-secondary mb-3">
                        Edited on {formatDate(group.time)}
                      </p>
                      <div className="space-y-2">
                        {group.changes.map((change, index) => (
                          <div 
                            key={index} 
                            className={`flex items-center justify-between p-3 rounded-md ${
                              change.changeType === 'added' 
                                ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800' 
                                : change.changeType === 'removed'
                                ? 'bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800'
                                : 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800'
                            }`}
                          >
                            <div className="flex items-center space-x-3">
                              <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
                                change.changeType === 'added' 
                                  ? 'bg-green-100 dark:bg-green-900/50' 
                                  : change.changeType === 'removed'
                                  ? 'bg-red-100 dark:bg-red-900/50'
                                  : 'bg-blue-100 dark:bg-blue-900/50'
                              }`}>
                                {change.changeType === 'added' ? (
                                  <span className="text-green-600 dark:text-green-400 text-lg font-bold">+</span>
                                ) : change.changeType === 'removed' ? (
                                  <span className="text-red-600 dark:text-red-400 text-lg font-bold">−</span>
                                ) : (
                                  <span className="text-blue-600 dark:text-blue-400 text-sm font-bold">↻</span>
                                )}
                              </div>
                              <div>
                                <p className={`text-sm font-medium ${
                                  change.changeType === 'added' 
                                    ? 'text-green-900 dark:text-green-100' 
                                    : change.changeType === 'removed'
                                    ? 'text-red-900 dark:text-red-100'
                                    : 'text-blue-900 dark:text-blue-100'
                                }`}>
                                  {change.itemCode} - {change.itemDose}
                                </p>
                                <p className={`text-xs ${
                                  change.changeType === 'added' 
                                    ? 'text-green-700 dark:text-green-200' 
                                    : change.changeType === 'removed'
                                    ? 'text-red-700 dark:text-red-200'
                                    : 'text-blue-700 dark:text-blue-200'
                                }`}>
                                  {change.changeType === 'added' && `Added ${change.newQty} units`}
                                  {change.changeType === 'removed' && `Removed ${change.oldQty} units`}
                                  {change.changeType === 'modified' && `Changed quantity from ${change.oldQty} to ${change.newQty}`}
                                </p>
                              </div>
                            </div>
                            {change.newPrice && (
                              <div className="text-right">
                                <p className={`text-sm font-medium ${
                                  change.changeType === 'added' 
                                    ? 'text-green-900 dark:text-green-100' 
                                    : change.changeType === 'removed'
                                    ? 'text-red-900 dark:text-red-100'
                                    : 'text-blue-900 dark:text-blue-100'
                                }`}>
                                  ${change.changeType === 'removed' ? change.oldPrice?.toFixed(2) : change.newPrice.toFixed(2)}
                                </p>
                                <p className={`text-xs ${
                                  change.changeType === 'added' 
                                    ? 'text-green-700 dark:text-green-200' 
                                    : change.changeType === 'removed'
                                    ? 'text-red-700 dark:text-red-200'
                                    : 'text-blue-700 dark:text-blue-200'
                                }`}>
                                  per unit
                                </p>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  ));
                })()}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Tracking Modal */}
      {showTrackingModal && (
        <div className="fixed inset-0 bg-gray-500 dark:bg-dark-bg-0/80 bg-opacity-75 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-dark-bg-1 rounded-lg shadow-xl dark:shadow-dark-md p-6 max-w-md w-full">
            <h3 className="text-lg font-medium text-gray-900 dark:text-dark-text-primary mb-4">
              {order.tracking17 ? 'Edit' : 'Add'} Tracking Number
            </h3>
            <input
              type="text"
              value={trackingNumber}
              onChange={(e) => setTrackingNumber(e.target.value)}
              placeholder="Enter tracking number"
              className="w-full rounded-lg border border-gray-300 dark:border-dark-border-default bg-white dark:bg-dark-bg-2 px-3 py-2.5 text-sm text-gray-900 dark:text-dark-text-primary placeholder-gray-400 dark:placeholder-dark-text-disabled focus:border-dark-accent-blue focus:ring-2 focus:ring-dark-accent-blue focus:ring-opacity-20 transition-colors duration-200"
            />
            <div className="mt-6 flex justify-end space-x-3">
              <button
                onClick={() => {
                  setShowTrackingModal(false);
                  setTrackingNumber(order.tracking17 || '');
                }}
                className="px-4 py-2 border border-gray-300 dark:border-dark-border-default rounded-md text-sm font-medium text-gray-700 dark:text-dark-text-primary hover:bg-gray-50 dark:hover:bg-dark-bg-hover"
              >
                Cancel
              </button>
              <button
                onClick={handleSaveTracking}
                disabled={savingTracking || !trackingNumber.trim()}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-dark-accent-blue hover:bg-dark-accent-blue/90 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {savingTracking ? 'Saving...' : 'Save'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Mark as Sent Confirmation Modal */}
      {showMarkAsSentConfirm && (
        <div className="fixed inset-0 bg-gray-500 dark:bg-dark-bg-0/80 bg-opacity-75 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-dark-bg-1 rounded-lg shadow-xl dark:shadow-dark-md p-6 max-w-md w-full">
            <div className="flex items-start">
              <div className="flex items-center justify-center w-12 h-12 rounded-full bg-blue-100 dark:bg-blue-900/20 mr-4">
                <Mail className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-medium text-gray-900 dark:text-dark-text-primary mb-2">
                  Mark Order as Sent
                </h3>
                <p className="text-sm text-gray-500 dark:text-dark-text-secondary">
                  Are you sure you want to mark this order as sent to supplier? This will update the status without sending an email to the supplier.
                </p>
              </div>
            </div>
            <div className="mt-6 flex justify-end space-x-3">
              <button
                onClick={() => setShowMarkAsSentConfirm(false)}
                className="px-4 py-2 border border-gray-300 dark:border-dark-border-default rounded-md text-sm font-medium text-gray-700 dark:text-dark-text-primary hover:bg-gray-50 dark:hover:bg-dark-bg-hover"
              >
                Cancel
              </button>
              <button
                onClick={handleMarkAsSentConfirm}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-dark-accent-blue hover:bg-dark-accent-blue/90"
              >
                Mark as Sent
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};