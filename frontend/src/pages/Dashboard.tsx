import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { fetchDashboardMetrics, fetchRevenueProfitTrend, fetchDailyOrderCount, fetchShippingStatusDistribution } from '../api/dashboard';
import type { DateFilter } from '../api/dashboard';
import type { DashboardMetrics, DailyRevenueProfitData, DailyOrderCountData, ShippingStatusDistribution } from '../types/dashboard';
import RevenueProfitChart from '../components/dashboard/RevenueProfitChart';
import DailyOrdersChart from '../components/dashboard/DailyOrdersChart';
import ShippingStatusCard from '../components/dashboard/ShippingStatusCard';
import DateRangeFilter from '../components/dashboard/DateRangeFilter';

const Dashboard = () => {
  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null);
  const [revenueProfitData, setRevenueProfitData] = useState<DailyRevenueProfitData[]>([]);
  const [dailyOrderData, setDailyOrderData] = useState<DailyOrderCountData[]>([]);
  const [shippingStatusData, setShippingStatusData] = useState<ShippingStatusDistribution[]>([]);
  const [loading, setLoading] = useState(true);
  const [chartLoading, setChartLoading] = useState(true);
  const [selectedDateFilter, setSelectedDateFilter] = useState<DateFilter>({ type: 'today' });

  useEffect(() => {
    loadDashboardData();
  }, [selectedDateFilter]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setChartLoading(true);
      
      // Load metrics and chart data in parallel
      const [metricsData, revenueProfitData, orderData, shippingData] = await Promise.all([
        fetchDashboardMetrics(selectedDateFilter),
        fetchRevenueProfitTrend(selectedDateFilter),
        fetchDailyOrderCount(selectedDateFilter),
        fetchShippingStatusDistribution()
      ]);
      
      setMetrics(metricsData);
      setRevenueProfitData(revenueProfitData);
      setDailyOrderData(orderData);
      setShippingStatusData(shippingData);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    } finally {
      setLoading(false);
      setChartLoading(false);
    }
  };


  const formatUSD = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatINR = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount);
  };


  if (loading) {
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="flex items-center justify-center h-64">
          <p className="text-gray-500">Loading...</p>
        </div>
      </div>
    );
  }

  if (!metrics) {
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-dark-text-primary">Dashboard</h1>
          <DateRangeFilter
            value={selectedDateFilter}
            onChange={setSelectedDateFilter}
          />
        </div>
        <div className="flex items-center justify-center h-64">
          <p className="text-red-500">Failed to load dashboard data</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-dark-text-primary">Dashboard</h1>
        <DateRangeFilter
          value={selectedDateFilter}
          onChange={setSelectedDateFilter}
        />
      </div>

      {/* Profit Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600 dark:text-white">Total Profit (USD)</CardTitle>
          </CardHeader>
          <CardContent>
            <div>
              <p className="text-2xl font-bold">{formatUSD(metrics.profit.totalUSD)}</p>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{formatINR(metrics.profit.totalINR)}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600 dark:text-white">Profit Margin</CardTitle>
          </CardHeader>
          <CardContent>
            <div>
              <p className="text-2xl font-bold">{metrics.profitMargin.percentage.toFixed(1)}%</p>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">Margin rate</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600 dark:text-white">Total Orders</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold">{metrics.orders.total}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600 dark:text-white">Total Revenue (USD)</CardTitle>
          </CardHeader>
          <CardContent>
            <div>
              <p className="text-2xl font-bold">{formatUSD(metrics.revenue.totalUSD)}</p>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{formatINR(metrics.revenue.totalINR)}</p>
            </div>
          </CardContent>
        </Card>

      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <RevenueProfitChart data={revenueProfitData} loading={chartLoading} />
        <DailyOrdersChart data={dailyOrderData} loading={chartLoading} />
      </div>

      {/* Shipping Status Section */}
      <div className="mt-6">
        <ShippingStatusCard data={shippingStatusData} loading={chartLoading} />
      </div>
    </div>
  );
};

export default Dashboard;