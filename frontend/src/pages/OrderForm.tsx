import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Plus, CheckCircle, Loader2 } from 'lucide-react';
import { AddressInput } from '../components/AddressInput';
import { ItemRow } from '../components/ItemRow';
import { PaymentCostCards } from '../components/PaymentCostCards';
import DatePickerField from '../components/DatePicker';
// Use real API client
import { pricingApi, ordersApi, addressApi } from '../api/client';
import type { PricingItem } from '../api/client';
import type { CreateOrderRequest } from '../../../shared/types/order.types';
import { useToastContext } from '../contexts/ToastContext';
import { getCountryConfig, validatePostalCode, getPostalCodeError } from '../utils/countryConfig';

interface OrderItem {
  code: string;
  dose: string;
  qty: number;
}

interface FormData {
  customerName: string;
  email: string;
  street1: string;
  street2: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  paymentMethod: 'BTC' | 'USDT' | 'USDC' | 'PayPal';
  paymentUrl: string;
  orderDate: Date | null;
}

export const OrderForm: React.FC = () => {
  const navigate = useNavigate();
  const { showSuccess, showError } = useToastContext();
  const [loading, setLoading] = useState(false);
  const [pricing, setPricing] = useState<PricingItem[]>([]);
  const [btcRate, setBtcRate] = useState<number | null>(null);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [submitError, setSubmitError] = useState<string | null>(null);
  
  
  // Load saved form data from localStorage or use defaults
  const [formData, setFormData] = useState<FormData>(() => {
    const savedData = localStorage.getItem('orderFormData');
    if (savedData) {
      try {
        const parsed = JSON.parse(savedData);
        // Convert orderDate back to Date object if it exists
        if (parsed.orderDate) {
          parsed.orderDate = new Date(parsed.orderDate);
        }
        return parsed;
      } catch (e) {
        console.error('Failed to parse saved form data:', e);
      }
    }
    return {
      customerName: '',
      email: '',
      street1: '',
      street2: '',
      city: '',
      state: '',
      postalCode: '',
      country: 'United States',
      paymentMethod: 'BTC',
      paymentUrl: '',
      orderDate: null,
    };
  });

  // Load saved items from localStorage or use defaults
  const [items, setItems] = useState<OrderItem[]>(() => {
    const savedItems = localStorage.getItem('orderFormItems');
    if (savedItems) {
      try {
        const parsedItems = JSON.parse(savedItems);
        return parsedItems.length > 0 ? parsedItems : [{ code: '', dose: '', qty: 1 }];
      } catch (e) {
        console.error('Failed to parse saved items:', e);
      }
    }
    return [{ code: '', dose: '', qty: 1 }];
  });

  // Fetch pricing data on mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        console.log('Fetching pricing data...');
        const pricingData = await pricingApi.getAll();
        console.log('Pricing data received:', pricingData);
        setPricing(pricingData);
        
        // Fetch BTC rate from backend
        try {
          const rate = await pricingApi.getBtcRate();
          setBtcRate(rate);
        } catch (error) {
          console.error('Failed to fetch BTC rate:', error);
          setBtcRate(100000); // Fallback rate
        }
      } catch (error) {
        console.error('Failed to fetch pricing:', error);
        console.error('Error details:', {
          message: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : undefined,
          error
        });
        setSubmitError('Failed to load pricing data. Please refresh the page.');
      }
    };
    
    fetchData();
  }, []);

  // Save form data to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('orderFormData', JSON.stringify(formData));
  }, [formData]);

  // Save items to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem('orderFormItems', JSON.stringify(items));
  }, [items]);

  const handleAddressChange = (field: string, value: string) => {
    setFormData(prev => {
      const updated = { ...prev, [field]: value };
      
      // When country changes, handle state field appropriately
      if (field === 'country') {
        const countryConfig = getCountryConfig(value);
        if (!countryConfig.hasStates) {
          // Clear state field for countries without states
          updated.state = '';
        } else if (prev.country !== value) {
          // Clear state when switching between countries with states
          // to avoid invalid state selections
          updated.state = '';
        }
      }
      
      return updated;
    });
    setErrors(prev => ({ ...prev, [field]: '' }));
  };

  const handleOrderDateChange = (date: Date | null) => {
    setFormData(prev => ({ ...prev, orderDate: date }));
    setErrors(prev => ({ ...prev, orderDate: '' }));
  };

  const handleOrderDateClear = () => {
    setFormData(prev => ({ ...prev, orderDate: null }));
    setErrors(prev => ({ ...prev, orderDate: '' }));
  };

  const handleItemChange = (index: number, field: string, value: string | number) => {
    setItems(prev => {
      const updated = [...prev];
      updated[index] = { ...updated[index], [field]: value };
      return updated;
    });
  };

  const handleRemoveItem = (index: number) => {
    setItems(prev => prev.filter((_, i) => i !== index));
  };

  const handleAddItem = () => {
    setItems(prev => [...prev, { code: '', dose: '', qty: 1 }]);
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};
    const countryConfig = getCountryConfig(formData.country);

    if (!formData.customerName.trim()) {
      newErrors.customerName = 'Customer name is required';
    }
    if (!formData.street1.trim()) {
      newErrors.street1 = 'Street address is required';
    }
    if (!formData.city.trim()) {
      newErrors.city = 'City is required';
    }
    
    // Only require state if the country has states
    if (countryConfig.hasStates && !formData.state) {
      newErrors.state = `${countryConfig.stateLabel || 'State'} is required`;
    }
    
    if (!formData.postalCode.trim()) {
      newErrors.postalCode = `${countryConfig.postalCodeLabel} is required`;
    } else if (!validatePostalCode(formData.postalCode, formData.country)) {
      newErrors.postalCode = getPostalCodeError(formData.country);
    }

    // Validate items
    const validItems = items.filter(item => item.code && item.dose);
    if (validItems.length === 0) {
      newErrors.items = 'At least one item is required';
    }

    // Validate payment URL if provided
    if (formData.paymentUrl && !isValidUrl(formData.paymentUrl)) {
      newErrors.paymentUrl = 'Invalid URL format';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const isValidUrl = (string: string): boolean => {
    try {
      new URL(string);
      return true;
    } catch (_) {
      return false;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    setSubmitError(null);

    try {
      // Start with current form data
      let addressToUse = {
        street1: formData.street1,
        street2: formData.street2,
        city: formData.city,
        state: formData.state,
        postalCode: formData.postalCode,
        country: formData.country,
      };

      // Check if address validation is disabled via localStorage setting
      const skipAddressValidation = localStorage.getItem('skipAddressValidation') === 'true';
      
      if (!skipAddressValidation) {
        try {
          const validationRequest = {
            street1: formData.street1,
            street2: formData.street2 || undefined,
            city: formData.city,
            state: formData.state || undefined,
            postalCode: formData.postalCode,
            country: formData.country,
          };

          const validation = await addressApi.validate(validationRequest);

          // Handle validation results
          if (!validation.isValid) {
            // Address is invalid
            const issuesText = validation.issues?.join(', ') || 'Invalid address';
            showError(`Address validation failed: ${issuesText}`);
            setErrors({ ...errors, address: issuesText });
            return;
          }

          // If confidence is low, show warning but allow proceeding
          if (validation.confidence === 'LOW') {
            const proceedWithLowConfidence = window.confirm(
              'Address validation confidence is low. ' +
              (validation.issues?.join(' ') || 'The address may be incomplete or incorrect.') +
              '\n\nDo you want to proceed anyway?'
            );
            
            if (!proceedWithLowConfidence) {
              return;
            }
          }

          // If we have a standardized address and confidence is not HIGH, ask user
          if (validation.standardizedAddress && validation.confidence !== 'HIGH') {
            const useStandardized = window.confirm(
              'We found a standardized version of your address:\n\n' +
              `${validation.standardizedAddress.street1}\n` +
              `${validation.standardizedAddress.city}, ${validation.standardizedAddress.state || ''} ${validation.standardizedAddress.postalCode}\n` +
              `${validation.standardizedAddress.country}\n\n` +
              'Would you like to use the standardized address?'
            );

            if (useStandardized) {
              // Use the standardized address directly
              addressToUse = {
                street1: validation.standardizedAddress.street1,
                street2: validation.standardizedAddress.street2 || '',
                city: validation.standardizedAddress.city,
                state: validation.standardizedAddress.state || '',
                postalCode: validation.standardizedAddress.postalCode,
                country: formData.country, // Keep the original country
              };
              
              // Also update the form for UI consistency
              setFormData(prev => ({
                ...prev,
                ...addressToUse,
              }));
            }
          }
        } catch (validationError: any) {
          // If validation service fails, ask user if they want to proceed
          console.error('Address validation service error:', validationError);
          
          const proceedWithoutValidation = window.confirm(
            'Address validation service is temporarily unavailable. ' +
            'Do you want to proceed without address validation?\n\n' +
            'You can disable address validation permanently in the settings.'
          );
          
          if (!proceedWithoutValidation) {
            return;
          }
          
          // Ask if they want to disable validation permanently
          const disablePermanently = window.confirm(
            'Would you like to disable address validation for future orders?\n\n' +
            'You can re-enable it later from the settings.'
          );
          
          if (disablePermanently) {
            localStorage.setItem('skipAddressValidation', 'true');
            showSuccess('Address validation has been disabled.');
          }
        }
      }

      // Filter out empty items
      const validItems = items.filter(item => item.code && item.dose);

      const orderRequest: CreateOrderRequest = {
        customerName: formData.customerName,
        email: formData.email,
        ...addressToUse, // Use the potentially standardized address
        paymentMethod: formData.paymentMethod,
        items: validItems,
        paymentUrl: formData.paymentUrl || undefined,
        orderDate: formData.orderDate && typeof formData.orderDate.toISOString === 'function' ? formData.orderDate.toISOString() : undefined,
      };

      console.log('Creating order with request:', orderRequest);
      console.log('Items:', validItems);
      await ordersApi.create(orderRequest);
      
      // Clear localStorage after successful order creation
      localStorage.removeItem('orderFormData');
      localStorage.removeItem('orderFormItems');
      
      // Show success message and navigate
      showSuccess('Order created successfully!');
      navigate('/orders');
    } catch (error) {
      console.error('Failed to create order:', error);
      setSubmitError('Failed to create order. Please try again.');
      showError('Failed to create order. Please check your information and try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-dark-bg-0 py-4 sm:py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-dark-text-primary mb-6 sm:mb-8">Create New Order</h1>
        
        <form onSubmit={handleSubmit} className="space-y-6 sm:space-y-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 sm:gap-8">
            <div className="lg:col-span-2 space-y-6 sm:space-y-8">
              {/* Order Information Section */}
              <div className="bg-white dark:bg-dark-bg-1 shadow dark:shadow-dark-md rounded-lg p-4 sm:p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-dark-text-primary mb-4">Order Information</h3>
                
                <div className="space-y-4">
                  <DatePickerField
                    label="Order Date"
                    selected={formData.orderDate}
                    onChange={handleOrderDateChange}
                    onClear={handleOrderDateClear}
                    placeholderText="Select order date or leave blank for today"
                    helperText="You can backdate orders to a past date if needed"
                    error={errors.orderDate}
                    showClearButton={true}
                  />
                </div>
              </div>

              {/* Address Section */}
              <div className="bg-white dark:bg-dark-bg-1 shadow dark:shadow-dark-md rounded-lg p-4 sm:p-6">
                <AddressInput
                  customerName={formData.customerName}
                  email={formData.email}
                  street1={formData.street1}
                  street2={formData.street2}
                  city={formData.city}
                  state={formData.state}
                  postalCode={formData.postalCode}
                  country={formData.country}
                  onChange={handleAddressChange}
                  errors={errors}
                />
              </div>

              {/* Items Section */}
              <div className="bg-white dark:bg-dark-bg-1 shadow dark:shadow-dark-md rounded-lg p-4 sm:p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-dark-text-primary mb-4">Order Items</h3>
                
                <div className="space-y-4">
                  {items.map((item, index) => (
                    <ItemRow
                      key={index}
                      index={index}
                      item={item}
                      pricing={pricing}
                      onChange={handleItemChange}
                      onRemove={handleRemoveItem}
                    />
                  ))}
                </div>
                
                {errors.items && (
                  <p className="mt-2 text-sm text-red-600">{errors.items}</p>
                )}
                
                <button
                  type="button"
                  onClick={handleAddItem}
                  className="mt-4 inline-flex items-center px-4 py-2 border border-gray-300 dark:border-dark-border-default shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-dark-text-primary bg-white dark:bg-dark-bg-1 hover:bg-gray-50 dark:hover:bg-dark-bg-hover focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-dark-accent-blue"
                >
                  <Plus className="h-5 w-5 mr-2 -ml-1" />
                  Add Item
                </button>
              </div>

              {/* Payment Section */}
              <div className="bg-white dark:bg-dark-bg-1 shadow dark:shadow-dark-md rounded-lg p-4 sm:p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-dark-text-primary mb-4">Payment Information</h3>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-dark-text-primary mb-2">
                      Payment Method
                    </label>
                    <div className="grid grid-cols-2 sm:grid-cols-4 gap-3">
                      {(['BTC', 'USDT', 'USDC', 'PayPal'] as const).map((method) => (
                        <label key={method} className="relative flex cursor-pointer rounded-lg border dark:border-dark-border-default bg-white dark:bg-dark-bg-2 p-4 shadow-sm focus:outline-none">
                          <input
                            type="radio"
                            name="paymentMethod"
                            value={method}
                            checked={formData.paymentMethod === method}
                            onChange={(e) => setFormData(prev => ({ 
                              ...prev, 
                              paymentMethod: e.target.value as typeof method 
                            }))}
                            className="sr-only"
                          />
                          <span className="flex flex-1">
                            <span className="flex flex-col">
                              <span className={`block text-sm font-medium ${
                                formData.paymentMethod === method ? 'text-indigo-900 dark:text-indigo-100' : 'text-gray-900 dark:text-dark-text-primary'
                              }`}>
                                {method}
                              </span>
                            </span>
                          </span>
                          <CheckCircle
                            className={`h-5 w-5 text-indigo-600 dark:text-indigo-400 ${
                              formData.paymentMethod === method ? '' : 'invisible'
                            }`}
                          />
                          <span
                            className={`pointer-events-none absolute -inset-px rounded-lg border-2 ${
                              formData.paymentMethod === method ? 'border-indigo-500' : 'border-transparent'
                            }`}
                            aria-hidden="true"
                          />
                        </label>
                      ))}
                    </div>
                  </div>

                  <div>
                    <label htmlFor="paymentUrl" className="block text-sm font-medium text-gray-700 dark:text-dark-text-primary">
                      Payment Proof URL (Optional)
                    </label>
                    <input
                      type="url"
                      id="paymentUrl"
                      value={formData.paymentUrl}
                      onChange={(e) => setFormData(prev => ({ ...prev, paymentUrl: e.target.value }))}
                      className={`mt-1 block w-full rounded-lg border px-3 py-2.5 text-sm focus:ring-2 focus:ring-opacity-20 transition-colors duration-200 ${
                        errors.paymentUrl
                          ? 'border-red-300 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 bg-white dark:bg-dark-bg-2'
                          : 'border-gray-300 dark:border-dark-border-default text-gray-900 dark:text-dark-text-primary focus:border-dark-accent-blue focus:ring-dark-accent-blue bg-white dark:bg-dark-bg-2 placeholder-gray-400 dark:placeholder-dark-text-disabled'
                      }`}
                      placeholder="https://blockchain.info/tx/..."
                    />
                    {errors.paymentUrl && (
                      <p className="mt-1 text-sm text-red-600">{errors.paymentUrl}</p>
                    )}
                  </div>
                </div>
              </div>
            </div>

            <div className="lg:col-span-1 space-y-6 sm:space-y-8">
              {/* Summary Panel */}
              <div className="sticky top-8">
                <PaymentCostCards
                  items={items}
                  pricing={pricing}
                  btcRate={btcRate}
                />

                {submitError && (
                  <div className="mt-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
                    <p className="text-sm text-red-800 dark:text-red-200">{submitError}</p>
                  </div>
                )}

                <button
                  type="submit"
                  disabled={loading}
                  className="mt-6 w-full inline-flex justify-center items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-dark-accent-blue hover:bg-dark-accent-blue/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-dark-accent-blue disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? (
                    <>
                      <Loader2 className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" />
                      Creating Order...
                    </>
                  ) : (
                    'Create Order'
                  )}
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};