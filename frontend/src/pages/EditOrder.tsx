import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ordersApi, pricingApi } from '../api/client';
import type { Order, UpdateOrderRequest } from '../../../shared/types/order.types';
import type { PricingItem } from '../api/client';
import { useToastContext } from '../contexts/ToastContext';
import { Package, AlertTriangle, Plus, Minus, Trash2 } from 'lucide-react';
import { getDiscountRate, getDiscountPercentage } from '../utils/discount';
import { useFormPersistence } from '../hooks/useFormPersistence';
import DatePickerField from '../components/DatePicker';

interface OrderItem {
  code: string;
  dose: string;
  qty: number;
  product?: string;
}

export const EditOrder: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { showSuccess, showError } = useToastContext();
  
  const [order, setOrder] = useState<Order | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [pricingData, setPricingData] = useState<PricingItem[]>([]);
  
  // Form state with persistence
  const persistenceKey = `editOrder-${id || 'unknown'}`;
  const [customerName, setCustomerName, clearCustomerName] = useFormPersistence(`${persistenceKey}-customerName`, '');
  const [email, setEmail, clearEmail] = useFormPersistence(`${persistenceKey}-email`, '');
  const [street1, setStreet1, clearStreet1] = useFormPersistence(`${persistenceKey}-street1`, '');
  const [street2, setStreet2, clearStreet2] = useFormPersistence(`${persistenceKey}-street2`, '');
  const [city, setCity, clearCity] = useFormPersistence(`${persistenceKey}-city`, '');
  const [state, setState, clearState] = useFormPersistence(`${persistenceKey}-state`, '');
  const [postalCode, setPostalCode, clearPostalCode] = useFormPersistence(`${persistenceKey}-postalCode`, '');
  const [country, setCountry, clearCountry] = useFormPersistence(`${persistenceKey}-country`, 'United States');
  const [paymentMethod, setPaymentMethod, clearPaymentMethod] = useFormPersistence(`${persistenceKey}-paymentMethod`, 'BTC');
  const [paymentUrl, setPaymentUrl, clearPaymentUrl] = useFormPersistence(`${persistenceKey}-paymentUrl`, '');
  const [orderDate, setOrderDate, clearOrderDate] = useFormPersistence<Date | null>(`${persistenceKey}-orderDate`, null);
  const [items, setItems, clearItems] = useFormPersistence<OrderItem[]>(`${persistenceKey}-items`, []);
  
  // Calculations
  const [subtotal, setSubtotal] = useState(0);
  const [shipping] = useState(40);
  const [discount, setDiscount] = useState(0);
  const [totalUsd, setTotalUsd] = useState(0);
  const [btcRate, setBtcRate] = useState<number | null>(null);
  const [totalBtc, setTotalBtc] = useState(0);

  useEffect(() => {
    if (id) {
      fetchOrderAndPricing();
    }
  }, [id]);

  useEffect(() => {
    calculateTotals();
  }, [items, btcRate]);

  const fetchOrderAndPricing = async () => {
    try {
      setLoading(true);
      const [orderData, pricing, rate] = await Promise.all([
        ordersApi.getById(id!),
        pricingApi.getAll(),
        pricingApi.getBtcRate()
      ]);
      
      // Check if order can be edited
      if (orderData.tracking17) {
        showError('This order cannot be edited because it has a tracking number');
        navigate(`/orders/${id}`);
        return;
      }
      
      setOrder(orderData);
      setPricingData(pricing);
      setBtcRate(rate);
      
      // Populate form fields
      setCustomerName(orderData.customerName);
      setEmail(orderData.email || '');
      setStreet1(orderData.street1);
      setStreet2(orderData.street2 || '');
      setCity(orderData.city);
      setState(orderData.state || '');
      setPostalCode(orderData.postalCode);
      setCountry(orderData.country || 'United States');
      setPaymentMethod(orderData.paymentMethod);
      setPaymentUrl(orderData.paymentUrl || '');
      setOrderDate(new Date(orderData.placedAt));
      
      // Populate items
      const orderItems: OrderItem[] = orderData.items.map(item => ({
        code: item.code,
        dose: item.dose,
        qty: item.qty,
        product: pricing.find(p => p.code === item.code)?.product
      }));
      setItems(orderItems);
    } catch (error) {
      console.error('Failed to fetch order:', error);
      showError('Failed to load order');
      navigate('/orders');
    } finally {
      setLoading(false);
    }
  };

  const calculateTotals = () => {
    let sub = 0;
    items.forEach(item => {
      const pricing = pricingData.find(p => p.code === item.code && p.dose === item.dose);
      if (pricing) {
        sub += pricing.buyingPrice * item.qty;
      }
    });
    setSubtotal(sub);
    
    const discountRate = getDiscountRate(order?.orderNumber);
    const disc = sub * discountRate;
    setDiscount(disc);
    
    const total = sub + shipping - disc;
    setTotalUsd(total);
    
    if (btcRate) {
      setTotalBtc(total / btcRate);
    }
  };

  const handleAddItem = () => {
    setItems([...items, { code: '', dose: '', qty: 1 }]);
  };

  const handleRemoveItem = (index: number) => {
    setItems(items.filter((_, i) => i !== index));
  };

  const handleItemChange = (index: number, field: keyof OrderItem, value: any) => {
    const updatedItems = [...items];
    updatedItems[index] = { ...updatedItems[index], [field]: value };
    
    // If product changes, update code and clear dose
    if (field === 'product') {
      const productPricing = pricingData.filter(p => p.product === value);
      if (productPricing.length > 0) {
        updatedItems[index].code = productPricing[0].code;
        updatedItems[index].dose = '';
      }
    }
    
    // If dose changes, update code
    if (field === 'dose' && updatedItems[index].product) {
      const pricing = pricingData.find(
        p => p.product === updatedItems[index].product && p.dose === value
      );
      if (pricing) {
        updatedItems[index].code = pricing.code;
      }
    }
    
    setItems(updatedItems);
  };

  const getAvailableDoses = (product: string) => {
    return [...new Set(pricingData.filter(p => p.product === product).map(p => p.dose))];
  };

  const getAvailableProducts = () => {
    return [...new Set(pricingData.map(p => p.product))];
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (items.length === 0) {
      showError('Please add at least one item');
      return;
    }
    
    // Validate items
    for (const item of items) {
      if (!item.code || !item.dose || item.qty < 1) {
        showError('Please complete all item details');
        return;
      }
    }
    
    setSaving(true);
    try {
      const updateData: UpdateOrderRequest = {
        customerName,
        email: email || undefined,
        street1,
        street2: street2 || undefined,
        city,
        state: state || undefined,
        postalCode,
        country,
        paymentMethod: paymentMethod as 'BTC' | 'USDT' | 'USDC' | 'PayPal',
        paymentUrl: paymentUrl || undefined,
        orderDate: orderDate ? orderDate.toISOString() : undefined,
        items: items.map(item => ({
          code: item.code,
          dose: item.dose,
          qty: item.qty
        }))
      };
      
      await ordersApi.update(id!, updateData);
      
      // Clear persisted form data after successful save
      clearCustomerName();
      clearEmail();
      clearStreet1();
      clearStreet2();
      clearCity();
      clearState();
      clearPostalCode();
      clearCountry();
      clearPaymentMethod();
      clearPaymentUrl();
      clearOrderDate();
      clearItems();
      
      showSuccess('Order updated successfully');
      navigate(`/orders/${id}`);
    } catch (error) {
      console.error('Failed to update order:', error);
      showError('Failed to update order');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-dark-bg-0 py-8">
        <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-center h-64">
            <div className="text-gray-500 dark:text-dark-text-secondary">Loading order...</div>
          </div>
        </div>
      </div>
    );
  }

  if (!order) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-dark-bg-0 py-8">
      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white dark:bg-dark-bg-1 shadow dark:shadow-dark-md rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-dark-text-primary mb-6">
              Edit Order #{order.orderNumber}
            </h2>
            
            {order.sentToSupplier && (
              <div className="mb-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md">
                <div className="flex">
                  <AlertTriangle className="h-5 w-5 text-yellow-400 dark:text-yellow-300 mr-2" />
                  <p className="text-sm text-yellow-800 dark:text-yellow-200">
                    This order has been sent to the supplier. Editing it will reset the supplier status.
                  </p>
                </div>
              </div>
            )}
            
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Customer Information */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-dark-text-primary mb-4">
                  Customer Information
                </h3>
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <div className="sm:col-span-2">
                    <DatePickerField
                      label="Order Date"
                      selected={orderDate}
                      onChange={setOrderDate}
                      onClear={clearOrderDate}
                      placeholderText="Select order date or leave blank for today"
                      helperText="Changing the date will reset supplier status if order was sent"
                      showClearButton={true}
                      maxDate={new Date()}
                    />
                  </div>
                  
                  <div className="sm:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-dark-text-primary">
                      Customer Name
                    </label>
                    <input
                      type="text"
                      value={customerName}
                      onChange={(e) => setCustomerName(e.target.value)}
                      required
                      className="mt-1 block w-full rounded-md border-gray-300 dark:border-dark-border-default shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-dark-bg-2 dark:text-dark-text-primary sm:text-sm"
                    />
                  </div>
                  
                  <div className="sm:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-dark-text-primary">
                      Email (Optional)
                    </label>
                    <input
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="mt-1 block w-full rounded-md border-gray-300 dark:border-dark-border-default shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-dark-bg-2 dark:text-dark-text-primary sm:text-sm"
                      placeholder="<EMAIL>"
                    />
                  </div>
                  
                  <div className="sm:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-dark-text-primary">
                      Street Address
                    </label>
                    <input
                      type="text"
                      value={street1}
                      onChange={(e) => setStreet1(e.target.value)}
                      required
                      className="mt-1 block w-full rounded-md border-gray-300 dark:border-dark-border-default shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-dark-bg-2 dark:text-dark-text-primary sm:text-sm"
                    />
                  </div>
                  
                  <div className="sm:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-dark-text-primary">
                      Apartment, suite, etc. (optional)
                    </label>
                    <input
                      type="text"
                      value={street2}
                      onChange={(e) => setStreet2(e.target.value)}
                      className="mt-1 block w-full rounded-md border-gray-300 dark:border-dark-border-default shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-dark-bg-2 dark:text-dark-text-primary sm:text-sm"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-dark-text-primary">
                      City
                    </label>
                    <input
                      type="text"
                      value={city}
                      onChange={(e) => setCity(e.target.value)}
                      required
                      className="mt-1 block w-full rounded-md border-gray-300 dark:border-dark-border-default shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-dark-bg-2 dark:text-dark-text-primary sm:text-sm"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-dark-text-primary">
                      State
                    </label>
                    <input
                      type="text"
                      value={state}
                      onChange={(e) => setState(e.target.value)}
                      className="mt-1 block w-full rounded-md border-gray-300 dark:border-dark-border-default shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-dark-bg-2 dark:text-dark-text-primary sm:text-sm"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-dark-text-primary">
                      Postal Code
                    </label>
                    <input
                      type="text"
                      value={postalCode}
                      onChange={(e) => setPostalCode(e.target.value)}
                      required
                      className="mt-1 block w-full rounded-md border-gray-300 dark:border-dark-border-default shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-dark-bg-2 dark:text-dark-text-primary sm:text-sm"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-dark-text-primary">
                      Country
                    </label>
                    <input
                      type="text"
                      value={country}
                      onChange={(e) => setCountry(e.target.value)}
                      required
                      className="mt-1 block w-full rounded-md border-gray-300 dark:border-dark-border-default shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-dark-bg-2 dark:text-dark-text-primary sm:text-sm"
                    />
                  </div>
                </div>
              </div>
              
              {/* Payment Information */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-dark-text-primary mb-4">
                  Payment Information
                </h3>
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-dark-text-primary">
                      Payment Method
                    </label>
                    <select
                      value={paymentMethod}
                      onChange={(e) => setPaymentMethod(e.target.value)}
                      className="mt-1 block w-full rounded-md border-gray-300 dark:border-dark-border-default shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-dark-bg-2 dark:text-dark-text-primary sm:text-sm"
                    >
                      <option value="BTC">BTC</option>
                      <option value="USDT">USDT</option>
                      <option value="USDC">USDC</option>
                      <option value="PayPal">PayPal</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-dark-text-primary">
                      Payment URL (optional)
                    </label>
                    <input
                      type="url"
                      value={paymentUrl}
                      onChange={(e) => setPaymentUrl(e.target.value)}
                      className="mt-1 block w-full rounded-md border-gray-300 dark:border-dark-border-default shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-dark-bg-2 dark:text-dark-text-primary sm:text-sm"
                    />
                  </div>
                </div>
              </div>
              
              {/* Order Items */}
              <div>
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-dark-text-primary">
                    Order Items
                  </h3>
                  <button
                    type="button"
                    onClick={handleAddItem}
                    className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Add Item
                  </button>
                </div>
                
                <div className="space-y-4">
                  {items.map((item, index) => (
                    <div key={index} className="bg-gray-50 dark:bg-dark-bg-0 rounded-lg p-4">
                      <div className="grid grid-cols-1 gap-4 sm:grid-cols-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-dark-text-primary">
                            Product
                          </label>
                          <select
                            value={item.product || ''}
                            onChange={(e) => handleItemChange(index, 'product', e.target.value)}
                            required
                            className="mt-1 block w-full rounded-md border-gray-300 dark:border-dark-border-default shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-dark-bg-2 dark:text-dark-text-primary sm:text-sm"
                          >
                            <option value="">Select product</option>
                            {getAvailableProducts().map(product => (
                              <option key={product} value={product}>{product}</option>
                            ))}
                          </select>
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-dark-text-primary">
                            Dose
                          </label>
                          <select
                            value={item.dose}
                            onChange={(e) => handleItemChange(index, 'dose', e.target.value)}
                            required
                            disabled={!item.product}
                            className="mt-1 block w-full rounded-md border-gray-300 dark:border-dark-border-default shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-dark-bg-2 dark:text-dark-text-primary sm:text-sm disabled:opacity-50"
                          >
                            <option value="">Select dose</option>
                            {item.product && getAvailableDoses(item.product).map(dose => (
                              <option key={dose} value={dose}>{dose}</option>
                            ))}
                          </select>
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-dark-text-primary">
                            Quantity
                          </label>
                          <div className="mt-1 flex items-center">
                            <button
                              type="button"
                              onClick={() => handleItemChange(index, 'qty', Math.max(1, item.qty - 1))}
                              className="p-1 rounded-l-md border border-gray-300 dark:border-dark-border-default bg-gray-100 dark:bg-dark-bg-2 hover:bg-gray-200 dark:hover:bg-dark-bg-hover"
                            >
                              <Minus className="h-4 w-4" />
                            </button>
                            <input
                              type="number"
                              value={item.qty}
                              onChange={(e) => handleItemChange(index, 'qty', parseInt(e.target.value) || 1)}
                              min="1"
                              required
                              className="w-16 text-center border-t border-b border-gray-300 dark:border-dark-border-default focus:outline-none focus:ring-1 focus:ring-indigo-500 dark:bg-dark-bg-2 dark:text-dark-text-primary"
                            />
                            <button
                              type="button"
                              onClick={() => handleItemChange(index, 'qty', item.qty + 1)}
                              className="p-1 rounded-r-md border border-gray-300 dark:border-dark-border-default bg-gray-100 dark:bg-dark-bg-2 hover:bg-gray-200 dark:hover:bg-dark-bg-hover"
                            >
                              <Plus className="h-4 w-4" />
                            </button>
                          </div>
                        </div>
                        
                        <div className="flex items-end">
                          <button
                            type="button"
                            onClick={() => handleRemoveItem(index)}
                            className="p-2 text-red-600 hover:text-red-800"
                          >
                            <Trash2 className="h-5 w-5" />
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              
              {/* Order Summary */}
              <div className="border-t border-gray-200 dark:border-dark-border-default pt-6">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600 dark:text-dark-text-secondary">Subtotal</span>
                    <span className="text-gray-900 dark:text-dark-text-primary">${subtotal.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600 dark:text-dark-text-secondary">Shipping</span>
                    <span className="text-gray-900 dark:text-dark-text-primary">${shipping.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600 dark:text-dark-text-secondary">Discount ({getDiscountPercentage(order?.orderNumber)})</span>
                    <span className="text-gray-900 dark:text-dark-text-primary">-${discount.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-base font-medium pt-2 border-t border-gray-200 dark:border-dark-border-default">
                    <span className="text-gray-900 dark:text-dark-text-primary">Total (USD)</span>
                    <span className="text-gray-900 dark:text-dark-text-primary">${totalUsd.toFixed(2)}</span>
                  </div>
                  {btcRate && (
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600 dark:text-dark-text-secondary">Total (BTC)</span>
                      <span className="text-gray-900 dark:text-dark-text-primary">{totalBtc.toFixed(8)} BTC</span>
                    </div>
                  )}
                </div>
              </div>
              
              {/* Action Buttons */}
              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={() => navigate(`/orders/${id}`)}
                  className="px-4 py-2 border border-gray-300 dark:border-dark-border-default rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-dark-text-primary bg-white dark:bg-dark-bg-2 hover:bg-gray-50 dark:hover:bg-dark-bg-hover focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={saving}
                  className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                >
                  {saving ? 'Saving...' : 'Save Changes'}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};