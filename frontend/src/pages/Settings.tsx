import React, { useState, useEffect } from 'react';
import { useToastContext } from '../contexts/ToastContext';
import { useDarkMode } from '../contexts/DarkModeContext';

export const Settings: React.FC = () => {
  const { showSuccess } = useToastContext();
  const { isDarkMode, toggleDarkMode } = useDarkMode();
  const [skipAddressValidation, setSkipAddressValidation] = useState(false);
  const [autoDarkMode, setAutoDarkMode] = useState(false);
  const [darkModeStartTime, setDarkModeStartTime] = useState('18:00');
  const [darkModeEndTime, setDarkModeEndTime] = useState('06:00');

  useEffect(() => {
    // Load current settings from localStorage
    const savedSetting = localStorage.getItem('skipAddressValidation') === 'true';
    setSkipAddressValidation(savedSetting);
    
    // Load dark mode settings
    const savedAutoDarkMode = localStorage.getItem('autoDarkMode') === 'true';
    const savedStartTime = localStorage.getItem('darkModeStartTime') || '18:00';
    const savedEndTime = localStorage.getItem('darkModeEndTime') || '06:00';
    
    setAutoDarkMode(savedAutoDarkMode);
    setDarkModeStartTime(savedStartTime);
    setDarkModeEndTime(savedEndTime);
  }, []);

  const handleToggleValidation = () => {
    const newValue = !skipAddressValidation;
    setSkipAddressValidation(newValue);
    localStorage.setItem('skipAddressValidation', newValue.toString());
    showSuccess(`Address validation ${newValue ? 'disabled' : 'enabled'}`);
  };

  const handleToggleAutoDarkMode = () => {
    const newValue = !autoDarkMode;
    setAutoDarkMode(newValue);
    localStorage.setItem('autoDarkMode', newValue.toString());
    showSuccess(`Automatic dark mode ${newValue ? 'enabled' : 'disabled'}`);
  };

  const handleTimeChange = (type: 'start' | 'end', value: string) => {
    if (type === 'start') {
      setDarkModeStartTime(value);
      localStorage.setItem('darkModeStartTime', value);
    } else {
      setDarkModeEndTime(value);
      localStorage.setItem('darkModeEndTime', value);
    }
    showSuccess(`Dark mode ${type} time updated to ${value}`);
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-dark-bg-0 py-4 sm:py-8">
      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-dark-text-primary mb-6 sm:mb-8">Settings</h1>
        
        <div className="bg-white dark:bg-dark-bg-1 shadow dark:shadow-dark-md rounded-lg p-4 sm:p-6">
          <div className="space-y-6">
            {/* Address Validation Setting */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-dark-text-primary mb-4">
                Order Form Settings
              </h3>
              
              <div className="flex items-center justify-between py-4 border-b border-gray-200 dark:border-dark-border-default">
                <div>
                  <p className="text-sm font-medium text-gray-900 dark:text-dark-text-primary">
                    Skip Address Validation
                  </p>
                  <p className="text-sm text-gray-500 dark:text-dark-text-secondary mt-1">
                    When enabled, orders will be created without validating addresses through Google's API
                  </p>
                </div>
                <button
                  type="button"
                  onClick={handleToggleValidation}
                  className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-dark-accent-blue focus:ring-offset-2 ${
                    skipAddressValidation ? 'bg-dark-accent-blue' : 'bg-gray-200 dark:bg-gray-700'
                  }`}
                  role="switch"
                  aria-checked={skipAddressValidation}
                >
                  <span className="sr-only">Skip address validation</span>
                  <span
                    className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                      skipAddressValidation ? 'translate-x-5' : 'translate-x-0'
                    }`}
                  />
                </button>
              </div>
            </div>

            {/* Dark Mode Settings */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-dark-text-primary mb-4">
                Appearance Settings
              </h3>
              
              <div className="flex items-center justify-between py-4 border-b border-gray-200 dark:border-dark-border-default">
                <div>
                  <p className="text-sm font-medium text-gray-900 dark:text-dark-text-primary">
                    Automatic Dark Mode
                  </p>
                  <p className="text-sm text-gray-500 dark:text-dark-text-secondary mt-1">
                    When enabled, dark mode will automatically turn on and off based on scheduled times
                  </p>
                </div>
                <button
                  type="button"
                  onClick={handleToggleAutoDarkMode}
                  className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-dark-accent-blue focus:ring-offset-2 ${
                    autoDarkMode ? 'bg-dark-accent-blue' : 'bg-gray-200 dark:bg-gray-700'
                  }`}
                  role="switch"
                  aria-checked={autoDarkMode}
                >
                  <span className="sr-only">Toggle automatic dark mode</span>
                  <span
                    className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                      autoDarkMode ? 'translate-x-5' : 'translate-x-0'
                    }`}
                  />
                </button>
              </div>

              {autoDarkMode && (
                <div className="mt-4 space-y-4">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-900 dark:text-dark-text-primary mb-2">
                        Dark Mode Start Time
                      </label>
                      <input
                        type="time"
                        value={darkModeStartTime}
                        onChange={(e) => handleTimeChange('start', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-dark-border-default rounded-md shadow-sm focus:outline-none focus:ring-dark-accent-blue focus:border-dark-accent-blue dark:bg-dark-bg-2 dark:text-dark-text-primary"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-900 dark:text-dark-text-primary mb-2">
                        Dark Mode End Time
                      </label>
                      <input
                        type="time"
                        value={darkModeEndTime}
                        onChange={(e) => handleTimeChange('end', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-dark-border-default rounded-md shadow-sm focus:outline-none focus:ring-dark-accent-blue focus:border-dark-accent-blue dark:bg-dark-bg-2 dark:text-dark-text-primary"
                      />
                    </div>
                  </div>
                  <div className="p-3 bg-gray-50 dark:bg-dark-bg-2 rounded-md">
                    <p className="text-sm text-gray-600 dark:text-dark-text-secondary">
                      Current mode: <span className="font-medium">{isDarkMode ? 'Dark' : 'Light'}</span>
                      {autoDarkMode && (
                        <span className="ml-2">
                          (Auto: {darkModeStartTime} - {darkModeEndTime})
                        </span>
                      )}
                    </p>
                  </div>
                </div>
              )}

              <div className="flex items-center justify-between py-4 border-b border-gray-200 dark:border-dark-border-default">
                <div>
                  <p className="text-sm font-medium text-gray-900 dark:text-dark-text-primary">
                    Manual Dark Mode Toggle
                  </p>
                  <p className="text-sm text-gray-500 dark:text-dark-text-secondary mt-1">
                    Override automatic mode and manually switch between light and dark themes
                  </p>
                </div>
                <button
                  type="button"
                  onClick={toggleDarkMode}
                  className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-dark-accent-blue focus:ring-offset-2 ${
                    isDarkMode ? 'bg-dark-accent-blue' : 'bg-gray-200 dark:bg-gray-700'
                  }`}
                  role="switch"
                  aria-checked={isDarkMode}
                >
                  <span className="sr-only">Toggle dark mode</span>
                  <span
                    className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                      isDarkMode ? 'translate-x-5' : 'translate-x-0'
                    }`}
                  />
                </button>
              </div>
            </div>

            {/* API Status Info */}
            <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">
                Address Validation Status
              </h4>
              <p className="text-sm text-blue-700 dark:text-blue-200">
                {skipAddressValidation 
                  ? "Address validation is currently disabled. Orders will be created without address verification."
                  : "Address validation is enabled. Google's Address Validation API will verify addresses before creating orders."}
              </p>
              {!skipAddressValidation && (
                <p className="text-xs text-blue-600 dark:text-blue-300 mt-2">
                  Note: If the validation service is unavailable, you'll be prompted to proceed without validation.
                </p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};