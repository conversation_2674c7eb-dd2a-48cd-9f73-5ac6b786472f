@tailwind base;
@tailwind components;
@tailwind utilities;

/* Environment indicator animations */
@keyframes productionGlow {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(220, 38, 38, 0.4), 0 1px 2px rgba(0, 0, 0, 0.1);
  }
  50% {
    box-shadow: 0 0 20px 2px rgba(220, 38, 38, 0.3), 0 1px 2px rgba(0, 0, 0, 0.1);
  }
}

@keyframes subtleShake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-1px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(1px);
  }
}

@keyframes borderWarning {
  0%, 100% {
    border-color: rgba(220, 38, 38, 0.5);
  }
  50% {
    border-color: rgba(220, 38, 38, 0.8);
  }
}

@layer base {
  * {
    @apply transition-colors duration-200;
  }
  
  /* Enhanced transition for dark mode toggle */
  html {
    @apply transition-colors duration-300 ease-in-out;
  }
  
  /* Smooth transitions for background elements */
  body, 
  [class*="bg-"],
  [class*="dark:bg-"] {
    @apply transition-colors duration-300 ease-in-out;
  }
  
  /* Smooth transitions for text elements */
  [class*="text-"],
  [class*="dark:text-"] {
    @apply transition-colors duration-200 ease-in-out;
  }
  
  /* Smooth transitions for borders */
  [class*="border-"],
  [class*="dark:border-"] {
    @apply transition-colors duration-200 ease-in-out;
  }
  
  /* Enhanced focus visibility for dark mode */
  .dark *:focus-visible {
    outline: 2px solid theme('colors.dark.accent.blue');
    outline-offset: 2px;
  }

  /* Improved focus ring for form elements */
  .dark input:focus,
  .dark select:focus,
  .dark textarea:focus {
    outline: none;
    border-color: theme('colors.dark.accent.blue');
    box-shadow: 0 0 0 2px rgba(88, 144, 255, 0.2);
  }
  
  /* Custom scrollbar for dark mode */
  .dark::-webkit-scrollbar {
    @apply w-2;
  }
  
  .dark::-webkit-scrollbar-track {
    background-color: theme('colors.dark.bg.0');
  }
  
  .dark::-webkit-scrollbar-thumb {
    background-color: theme('colors.dark.border.default');
    border-radius: 6px;
  }
  
  .dark::-webkit-scrollbar-thumb:hover {
    background-color: theme('colors.dark.border.strong');
  }
  
  /* Better selection colors in dark mode */
  .dark ::selection {
    background-color: theme('colors.dark.accent.blue');
    color: theme('colors.white');
  }
  :root {

    --background: 0 0% 100%;

    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;

    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;

    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;

    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;

    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;

    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;

    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;

    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;

    --input: 214.3 31.8% 91.4%;

    --ring: 222.2 84% 4.9%;

    --chart-1: 12 76% 61%;

    --chart-2: 173 58% 39%;

    --chart-3: 197 37% 24%;

    --chart-4: 43 74% 66%;

    --chart-5: 27 87% 67%;

    --radius: 0.5rem
  }
  .dark {
    /* Main background - dark gray, not black */
    --background: 0 0% 10.6%;

    /* High contrast white text for main content */
    --foreground: 0 0% 98%;

    /* Card background - slightly elevated from main background */
    --card: 0 0% 13.3%;

    /* Card text - high contrast white */
    --card-foreground: 0 0% 98%;

    /* Popover background - same as card */
    --popover: 0 0% 13.3%;

    /* Popover text - high contrast white */
    --popover-foreground: 0 0% 98%;

    /* Primary button - accessible blue */
    --primary: 220 100% 66%;

    /* Primary button text - dark for contrast */
    --primary-foreground: 0 0% 10.6%;

    /* Secondary surfaces - medium elevation */
    --secondary: 0 0% 16.5%;

    /* Secondary text - high contrast white */
    --secondary-foreground: 0 0% 98%;

    /* Muted surfaces - for less important content */
    --muted: 0 0% 16.5%;

    /* Muted text - 60% opacity equivalent */
    --muted-foreground: 0 0% 65%;

    /* Accent surfaces - for highlights */
    --accent: 0 0% 18.8%;

    /* Accent text - high contrast white */
    --accent-foreground: 0 0% 98%;

    /* Destructive/error - accessible red */
    --destructive: 0 72% 51%;

    /* Destructive text - white for contrast */
    --destructive-foreground: 0 0% 98%;

    /* Borders - subtle white with transparency */
    --border: 0 0% 20%;

    /* Input borders - slightly more visible */
    --input: 0 0% 20%;

    /* Focus rings - accessible blue */
    --ring: 220 100% 66%;

    /* Chart colors - accessible and desaturated */
    --chart-1: 220 70% 60%;
    --chart-2: 160 60% 50%;
    --chart-3: 30 80% 60%;
    --chart-4: 280 65% 65%;
    --chart-5: 340 75% 60%
  }
}



@layer base {
  * {
    @apply border-border;
  }
  /* Removed body background/foreground override to allow custom dark mode classes to work */
}
