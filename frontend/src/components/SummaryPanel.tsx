import React, { useMemo } from 'react';
import { ArrowDownCircle, ArrowUpCircle, TrendingUp, TrendingDown } from 'lucide-react';
import type { PricingItem } from '../api/client';
import { getDiscountRate, getDiscountPercentage } from '../utils/discount';

interface SummaryPanelProps {
  items: Array<{
    code: string;
    dose: string;
    qty: number;
  }>;
  pricing: PricingItem[];
  btcRate: number | null;
}

export const SummaryPanel: React.FC<SummaryPanelProps> = ({
  items,
  pricing,
  btcRate,
}) => {
  const calculations = useMemo(() => {
    let supplierSubtotal = 0;
    let customerSubtotal = 0;

    // Calculate subtotals
    items.forEach(item => {
      const priceInfo = pricing.find(
        p => p.code === item.code && p.dose === item.dose
      );
      if (priceInfo) {
        supplierSubtotal += priceInfo.buyingPrice * item.qty;
        customerSubtotal += priceInfo.sellingPrice * item.qty;
      }
    });

    // Only apply shipping and discount when there are items
    const hasItems = items.length > 0 && supplierSubtotal > 0;
    const shipping = hasItems ? 40 : 0;
    
    // Supplier calculations (with 5% discount on products only, not shipping)
    const supplierDiscount = hasItems ? supplierSubtotal * 0.05 : 0;
    const supplierTotalUsd = supplierSubtotal + shipping - supplierDiscount;
    const supplierTotalBtc = btcRate && supplierTotalUsd > 0 ? supplierTotalUsd / btcRate : 0;
    
    // Customer payment calculations (no discount)
    const customerTotalUsd = customerSubtotal + shipping;
    const customerTotalBtc = btcRate && customerTotalUsd > 0 ? customerTotalUsd / btcRate : 0;
    
    // Actual profit calculation: Customer Total Payment - Supplier Total Cost
    const profitUsd = hasItems ? customerTotalUsd - supplierTotalUsd : 0;
    const profitInr = hasItems ? profitUsd * 86 : 0;
    const profitMargin = customerTotalUsd > 0 ? (profitUsd / customerTotalUsd) * 100 : 0;

    return {
      // Supplier values
      supplierSubtotal,
      supplierDiscount,
      supplierTotalUsd,
      supplierTotalBtc,
      // Customer values
      customerSubtotal,
      customerTotalUsd,
      customerTotalBtc,
      // Common values
      shipping,
      // Profit values
      profitUsd,
      profitInr,
      profitMargin,
      hasItems,
    };
  }, [items, pricing, btcRate]);

  return (
    <div className="space-y-6">
      {/* Customer Payment Block - Green Theme */}
      <div className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg shadow-lg dark:shadow-dark-sm p-6 border border-green-200 dark:border-green-800">
        <div className="flex items-center gap-3 mb-4">
          <ArrowDownCircle className="h-6 w-6 text-green-600 dark:text-green-400" />
          <h3 className="text-lg font-semibold text-green-900 dark:text-green-100">
            Customer Payment
          </h3>
        </div>
        
        <div className="space-y-3">
          <div className="flex justify-between text-sm">
            <span className="text-green-700 dark:text-green-300">Products Subtotal:</span>
            <span className="font-medium text-green-900 dark:text-green-100">
              ${calculations.customerSubtotal.toFixed(2)}
            </span>
          </div>
          
          <div className="flex justify-between text-sm">
            <span className="text-green-700 dark:text-green-300">Shipping:</span>
            <span className="font-medium text-green-900 dark:text-green-100">
              ${calculations.shipping.toFixed(2)}
            </span>
          </div>
          
          <div className="flex justify-between text-sm">
            <span className="text-green-700 dark:text-green-300">Discount:</span>
            <span className="font-medium text-green-900 dark:text-green-100">
              $0.00
            </span>
          </div>
          
          <div className="border-t border-green-300 dark:border-green-700 pt-3 mt-3">
            <div className="flex justify-between text-lg font-bold text-green-900 dark:text-green-100">
              <span>Total Received:</span>
              <span>${calculations.customerTotalUsd.toFixed(2)}</span>
            </div>
            
            {btcRate && (
              <div className="flex justify-between text-sm mt-1">
                <span className="text-green-600 dark:text-green-300">BTC Equivalent:</span>
                <span className="font-medium text-green-900 dark:text-green-100">
                  {calculations.customerTotalBtc.toFixed(8)} BTC
                </span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Supplier Cost Block - Red Theme */}
      <div className="bg-gradient-to-br from-red-50 to-rose-50 dark:from-red-900/20 dark:to-rose-900/20 rounded-lg shadow-lg dark:shadow-dark-sm p-6 border border-red-200 dark:border-red-800">
        <div className="flex items-center gap-3 mb-4">
          <ArrowUpCircle className="h-6 w-6 text-red-600 dark:text-red-400" />
          <h3 className="text-lg font-semibold text-red-900 dark:text-red-100">
            Supplier Cost
          </h3>
        </div>
        
        <div className="space-y-3">
          <div className="flex justify-between text-sm">
            <span className="text-red-700 dark:text-red-300">Products Subtotal:</span>
            <span className="font-medium text-red-900 dark:text-red-100">
              ${calculations.supplierSubtotal.toFixed(2)}
            </span>
          </div>
          
          <div className="flex justify-between text-sm">
            <span className="text-red-700 dark:text-red-300">Shipping:</span>
            <span className="font-medium text-red-900 dark:text-red-100">
              ${calculations.shipping.toFixed(2)}
            </span>
          </div>
          
          <div className="flex justify-between text-sm">
            <span className="text-red-700 dark:text-red-300">Discount (5%):</span>
            <span className="font-medium text-green-600 dark:text-green-400">
              -${calculations.supplierDiscount.toFixed(2)}
            </span>
          </div>
          
          <div className="border-t border-red-300 dark:border-red-700 pt-3 mt-3">
            <div className="flex justify-between text-lg font-bold text-red-900 dark:text-red-100">
              <span>Total Cost:</span>
              <span>${calculations.supplierTotalUsd.toFixed(2)}</span>
            </div>
            
            {btcRate && (
              <div className="flex justify-between text-sm mt-1">
                <span className="text-red-600 dark:text-red-300">BTC Equivalent:</span>
                <span className="font-medium text-red-900 dark:text-red-100">
                  {calculations.supplierTotalBtc.toFixed(8)} BTC
                </span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Profit Block - Prominent Display */}
      {calculations.hasItems && (
        <div className={`rounded-lg shadow-xl dark:shadow-dark-sm p-6 border-2 ${
          calculations.profitUsd >= 0 
            ? 'bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/30 dark:to-indigo-900/30 border-blue-300 dark:border-blue-700' 
            : 'bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/30 dark:to-red-900/30 border-orange-300 dark:border-orange-700'
        }`}>
          <div className="flex items-center gap-3 mb-4">
            {calculations.profitUsd >= 0 ? (
              <TrendingUp className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            ) : (
              <TrendingDown className="h-6 w-6 text-orange-600 dark:text-orange-400" />
            )}
            <h3 className={`text-lg font-semibold ${
              calculations.profitUsd >= 0 
                ? 'text-blue-900 dark:text-blue-100' 
                : 'text-orange-900 dark:text-orange-100'
            }`}>
              Profit Analysis
            </h3>
          </div>
          
          <div className="space-y-3">
            <div className="text-center py-4">
              <div className={`text-3xl font-bold ${
                calculations.profitUsd >= 0 
                  ? 'text-blue-900 dark:text-blue-100' 
                  : 'text-orange-900 dark:text-orange-100'
              }`}>
                ${calculations.profitUsd.toFixed(2)}
              </div>
              <div className={`text-sm mt-1 ${
                calculations.profitUsd >= 0 
                  ? 'text-blue-700 dark:text-blue-300' 
                  : 'text-orange-700 dark:text-orange-300'
              }`}>
                USD Profit
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4 pt-3 border-t border-gray-200 dark:border-gray-700">
              <div>
                <div className={`text-sm ${
                  calculations.profitUsd >= 0 
                    ? 'text-blue-700 dark:text-blue-300' 
                    : 'text-orange-700 dark:text-orange-300'
                }`}>
                  INR Equivalent
                </div>
                <div className={`text-lg font-semibold ${
                  calculations.profitUsd >= 0 
                    ? 'text-blue-900 dark:text-blue-100' 
                    : 'text-orange-900 dark:text-orange-100'
                }`}>
                  ₹{calculations.profitInr.toFixed(2)}
                </div>
              </div>
              
              <div>
                <div className={`text-sm ${
                  calculations.profitUsd >= 0 
                    ? 'text-blue-700 dark:text-blue-300' 
                    : 'text-orange-700 dark:text-orange-300'
                }`}>
                  Profit Margin
                </div>
                <div className={`text-lg font-semibold ${
                  calculations.profitUsd >= 0 
                    ? 'text-blue-900 dark:text-blue-100' 
                    : 'text-orange-900 dark:text-orange-100'
                }`}>
                  {calculations.profitMargin.toFixed(1)}%
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Warning for negative profit */}
      {calculations.hasItems && calculations.profitUsd < 0 && (
        <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <p className="text-sm text-red-800 dark:text-red-200 font-medium">
            ⚠️ Warning: This order will result in a loss of ${Math.abs(calculations.profitUsd).toFixed(2)}. 
            Consider adjusting prices or quantities.
          </p>
        </div>
      )}

      {/* BTC Rate Warning */}
      {!btcRate && (
        <div className="p-3 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-md">
          <p className="text-sm text-amber-800 dark:text-amber-200">
            Note: Unable to fetch current BTC rate. Using cached or default rate.
          </p>
        </div>
      )}
    </div>
  );
};