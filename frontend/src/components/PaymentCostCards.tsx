import React, { useMemo } from 'react';
import { ArrowDownCircle, ArrowUpCircle, TrendingUp, TrendingDown } from 'lucide-react';
import type { PricingItem } from '../api/client';
import type { Order } from '../../../shared/types/order.types';

interface PaymentCostCardsProps {
  items: Array<{
    code: string;
    dose: string;
    qty: number;
    buyingPrice?: number;
    sellingPrice?: number;
  }>;
  pricing?: PricingItem[];
  btcRate?: number | null;
  order?: Order;
}

export const PaymentCostCards: React.FC<PaymentCostCardsProps> = ({
  items,
  pricing = [],
  btcRate = null,
  order,
}) => {
  const calculations = useMemo(() => {
    let supplierSubtotal = 0;
    let customerSubtotal = 0;

    // Calculate subtotals based on available data
    items.forEach(item => {
      if (order) {
        // Use order data if available (for OrderDetail page)
        supplierSubtotal += (item.buyingPrice || 0) * item.qty;
        customerSubtotal += (item.sellingPrice || item.buyingPrice || 0) * item.qty;
      } else {
        // Use pricing data if available (for OrderForm page)
        const priceInfo = pricing.find(
          p => p.code === item.code && p.dose === item.dose
        );
        if (priceInfo) {
          supplierSubtotal += priceInfo.buyingPrice * item.qty;
          customerSubtotal += priceInfo.sellingPrice * item.qty;
        }
      }
    });

    // Only apply shipping and discount when there are items
    const hasItems = items.length > 0 && supplierSubtotal > 0;
    const shipping = hasItems ? 40 : 0;
    
    // Supplier calculations (with 5% discount on products only, not shipping)
    const supplierDiscount = hasItems ? supplierSubtotal * 0.05 : 0;
    const supplierTotalUsd = supplierSubtotal + shipping - supplierDiscount;
    const supplierTotalBtc = btcRate && supplierTotalUsd > 0 ? supplierTotalUsd / btcRate : 0;
    
    // Customer payment calculations (no discount)
    const customerTotalUsd = customerSubtotal + shipping;
    const customerTotalBtc = btcRate && customerTotalUsd > 0 ? customerTotalUsd / btcRate : 0;
    
    // Actual profit calculation: Customer Total Payment - Supplier Total Cost
    const profitUsd = hasItems ? customerTotalUsd - supplierTotalUsd : 0;
    const profitInr = hasItems ? profitUsd * 86 : 0;
    const profitMargin = customerTotalUsd > 0 ? (profitUsd / customerTotalUsd) * 100 : 0;

    return {
      // Supplier values
      supplierSubtotal,
      supplierDiscount,
      supplierTotalUsd,
      supplierTotalBtc,
      // Customer values
      customerSubtotal,
      customerTotalUsd,
      customerTotalBtc,
      // Common values
      shipping,
      // Profit values
      profitUsd,
      profitInr,
      profitMargin,
      hasItems,
    };
  }, [items, pricing, btcRate, order]);

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Customer Payment Block - Green Theme */}
      <div className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg shadow-lg dark:shadow-dark-sm p-4 sm:p-6 border border-green-200 dark:border-green-800">
        <div className="flex items-center gap-2 sm:gap-3 mb-3 sm:mb-4">
          <ArrowDownCircle className="h-5 w-5 sm:h-6 sm:w-6 text-green-600 dark:text-green-400 flex-shrink-0" />
          <h3 className="text-base sm:text-lg font-semibold text-green-900 dark:text-green-100">
            Customer Payment
          </h3>
        </div>
        
        <div className="space-y-2 sm:space-y-3">
          <div className="flex justify-between text-sm">
            <span className="text-green-700 dark:text-green-300">Products Subtotal:</span>
            <span className="font-medium text-green-900 dark:text-green-100">
              ${calculations.customerSubtotal.toFixed(2)}
            </span>
          </div>
          
          <div className="flex justify-between text-sm">
            <span className="text-green-700 dark:text-green-300">Shipping:</span>
            <span className="font-medium text-green-900 dark:text-green-100">
              ${calculations.shipping.toFixed(2)}
            </span>
          </div>
          
          <div className="flex justify-between text-sm">
            <span className="text-green-700 dark:text-green-300">Discount:</span>
            <span className="font-medium text-green-900 dark:text-green-100">
              $0.00
            </span>
          </div>
          
          <div className="border-t border-green-300 dark:border-green-700 pt-2 sm:pt-3 mt-2 sm:mt-3">
            <div className="flex justify-between text-base sm:text-lg font-bold text-green-900 dark:text-green-100">
              <span>Total Received:</span>
              <span>${calculations.customerTotalUsd.toFixed(2)}</span>
            </div>
            
            {btcRate && (
              <div className="flex justify-between text-sm mt-1">
                <span className="text-green-600 dark:text-green-300">BTC Equivalent:</span>
                <span className="font-medium text-green-900 dark:text-green-100">
                  {calculations.customerTotalBtc.toFixed(8)} BTC
                </span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Supplier Cost Block - Red Theme */}
      <div className="bg-gradient-to-br from-red-50 to-rose-50 dark:from-red-900/20 dark:to-rose-900/20 rounded-lg shadow-lg dark:shadow-dark-sm p-4 sm:p-6 border border-red-200 dark:border-red-800">
        <div className="flex items-center gap-2 sm:gap-3 mb-3 sm:mb-4">
          <ArrowUpCircle className="h-5 w-5 sm:h-6 sm:w-6 text-red-600 dark:text-red-400 flex-shrink-0" />
          <h3 className="text-base sm:text-lg font-semibold text-red-900 dark:text-red-100">
            Supplier Cost
          </h3>
        </div>
        
        <div className="space-y-2 sm:space-y-3">
          <div className="flex justify-between text-sm">
            <span className="text-red-700 dark:text-red-300">Products Subtotal:</span>
            <span className="font-medium text-red-900 dark:text-red-100">
              ${calculations.supplierSubtotal.toFixed(2)}
            </span>
          </div>
          
          <div className="flex justify-between text-sm">
            <span className="text-red-700 dark:text-red-300">Shipping:</span>
            <span className="font-medium text-red-900 dark:text-red-100">
              ${calculations.shipping.toFixed(2)}
            </span>
          </div>
          
          <div className="flex justify-between text-sm">
            <span className="text-red-700 dark:text-red-300">Discount (5%):</span>
            <span className="font-medium text-green-600 dark:text-green-400">
              -${calculations.supplierDiscount.toFixed(2)}
            </span>
          </div>
          
          <div className="border-t border-red-300 dark:border-red-700 pt-2 sm:pt-3 mt-2 sm:mt-3">
            <div className="flex justify-between text-base sm:text-lg font-bold text-red-900 dark:text-red-100">
              <span>Total Cost:</span>
              <span>${calculations.supplierTotalUsd.toFixed(2)}</span>
            </div>
            
            {btcRate && (
              <div className="flex justify-between text-sm mt-1">
                <span className="text-red-600 dark:text-red-300">BTC Equivalent:</span>
                <span className="font-medium text-red-900 dark:text-red-100">
                  {calculations.supplierTotalBtc.toFixed(8)} BTC
                </span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Profit Block - Prominent Display */}
      {calculations.hasItems && (
        <div className={`rounded-lg shadow-xl dark:shadow-dark-sm p-4 sm:p-6 border-2 ${
          calculations.profitUsd >= 0 
            ? 'bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/30 dark:to-indigo-900/30 border-blue-300 dark:border-blue-700' 
            : 'bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/30 dark:to-red-900/30 border-orange-300 dark:border-orange-700'
        }`}>
          <div className="flex items-center gap-2 sm:gap-3 mb-3 sm:mb-4">
            {calculations.profitUsd >= 0 ? (
              <TrendingUp className="h-5 w-5 sm:h-6 sm:w-6 text-blue-600 dark:text-blue-400 flex-shrink-0" />
            ) : (
              <TrendingDown className="h-5 w-5 sm:h-6 sm:w-6 text-orange-600 dark:text-orange-400 flex-shrink-0" />
            )}
            <h3 className={`text-base sm:text-lg font-semibold ${
              calculations.profitUsd >= 0 
                ? 'text-blue-900 dark:text-blue-100' 
                : 'text-orange-900 dark:text-orange-100'
            }`}>
              Profit Analysis
            </h3>
          </div>
          
          <div className="space-y-2 sm:space-y-3">
            <div className="flex justify-between text-sm">
              <span className={calculations.profitUsd >= 0 
                ? 'text-blue-700 dark:text-blue-300' 
                : 'text-orange-700 dark:text-orange-300'
              }>
                USD Profit:
              </span>
              <span className={`font-medium ${
                calculations.profitUsd >= 0 
                  ? 'text-blue-900 dark:text-blue-100' 
                  : 'text-orange-900 dark:text-orange-100'
              }`}>
                ${calculations.profitUsd.toFixed(2)}
              </span>
            </div>
            
            <div className="flex justify-between text-sm">
              <span className={calculations.profitUsd >= 0 
                ? 'text-blue-700 dark:text-blue-300' 
                : 'text-orange-700 dark:text-orange-300'
              }>
                INR Equivalent:
              </span>
              <span className={`font-medium ${
                calculations.profitUsd >= 0 
                  ? 'text-blue-900 dark:text-blue-100' 
                  : 'text-orange-900 dark:text-orange-100'
              }`}>
                ₹{calculations.profitInr.toFixed(2)}
              </span>
            </div>
            
            <div className="flex justify-between text-sm">
              <span className={calculations.profitUsd >= 0 
                ? 'text-blue-700 dark:text-blue-300' 
                : 'text-orange-700 dark:text-orange-300'
              }>
                Profit Margin:
              </span>
              <span className={`font-medium ${
                calculations.profitUsd >= 0 
                  ? 'text-blue-900 dark:text-blue-100' 
                  : 'text-orange-900 dark:text-orange-100'
              }`}>
                {calculations.profitMargin.toFixed(1)}%
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Warning for negative profit */}
      {calculations.hasItems && calculations.profitUsd < 0 && (
        <div className="p-3 sm:p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <p className="text-sm text-red-800 dark:text-red-200 font-medium">
            ⚠️ Warning: This order will result in a loss of ${Math.abs(calculations.profitUsd).toFixed(2)}. 
            Consider adjusting prices or quantities.
          </p>
        </div>
      )}

      {/* BTC Rate Warning */}
      {!btcRate && (
        <div className="p-3 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-md">
          <p className="text-sm text-amber-800 dark:text-amber-200">
            Note: Unable to fetch current BTC rate. Using cached or default rate.
          </p>
        </div>
      )}
    </div>
  );
};