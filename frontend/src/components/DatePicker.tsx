import React, { useState } from 'react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import '../styles/datepicker.css';
import { format } from 'date-fns';

interface DatePickerFieldProps {
  label?: string;
  selected?: Date | null;
  onChange?: (date: Date | null) => void;
  placeholderText?: string;
  disabled?: boolean;
  required?: boolean;
  error?: string;
  helperText?: string;
  className?: string;
  id?: string;
  maxDate?: Date;
  minDate?: Date;
  showClearButton?: boolean;
  onClear?: () => void;
}

const DatePickerField: React.FC<DatePickerFieldProps> = ({
  label,
  selected = null,
  onChange,
  placeholderText = 'Select a date...',
  disabled = false,
  required = false,
  error,
  helperText,
  className = '',
  id,
  maxDate = new Date(), // Prevent future dates by default
  minDate = new Date(2020, 0, 1), // Reasonable past date limit
  showClearButton = true,
  onClear
}) => {
  const [isFocused, setIsFocused] = useState(false);

  const handleChange = (date: Date | null) => {
    if (onChange) {
      onChange(date);
    }
  };

  const handleClear = () => {
    if (onClear) {
      onClear();
    } else if (onChange) {
      onChange(null);
    }
  };

  const fieldId = id || `datepicker-${Math.random().toString(36).substr(2, 9)}`;

  return (
    <div className={`space-y-1 ${className}`}>
      {label && (
        <label 
          htmlFor={fieldId}
          className={`block text-sm font-medium ${
            error ? 'text-red-600 dark:text-red-400' : 
            'text-gray-700 dark:text-gray-300'
          }`}
        >
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      <div className="relative">
        <div className={`relative flex items-center transition-all duration-200 ${
          isFocused 
            ? 'ring-2 ring-blue-500 dark:ring-blue-400' 
            : error
            ? 'ring-1 ring-red-500 dark:ring-red-400'
            : 'ring-1 ring-gray-300 dark:ring-gray-600'
        } rounded-lg bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600`}>
          <div className="flex-1">
            <DatePicker
              id={fieldId}
              selected={selected}
              onChange={handleChange}
              placeholderText={placeholderText}
              disabled={disabled}
              required={required}
              maxDate={maxDate}
              minDate={minDate}
              dateFormat="MMM dd, yyyy"
              showPopperArrow={false}
              popperClassName="rdp-custom"
              calendarClassName="rdp-custom"
              className={`
                w-full px-3 py-2 pr-10
                bg-transparent border-none outline-none
                text-gray-900 dark:text-white
                placeholder-gray-500 dark:placeholder-gray-400
                disabled:opacity-50 disabled:cursor-not-allowed
                focus:ring-0
                ${selected ? 'text-gray-900 dark:text-white' : 'text-gray-500 dark:text-gray-400'}
              `}
              onFocus={() => setIsFocused(true)}
              onBlur={() => setIsFocused(false)}
            />
          </div>
          
          {/* Calendar icon */}
          <div className="absolute right-2 pointer-events-none text-gray-400 dark:text-gray-500">
            <svg 
              className="w-5 h-5" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" 
              />
            </svg>
          </div>
        </div>

        {/* Clear button */}
        {selected && showClearButton && !disabled && (
          <button
            type="button"
            onClick={handleClear}
            className="absolute -top-2 -right-2 bg-red-500 hover:bg-red-600 text-white rounded-full p-1 shadow-md transition-colors duration-200"
            title="Clear date"
          >
            <svg 
              className="w-3 h-3" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M6 18L18 6M6 6l12 12" 
              />
            </svg>
          </button>
        )}
      </div>

      {/* Error message */}
      {error && (
        <p className="text-sm text-red-600 dark:text-red-400 mt-1">
          {error}
        </p>
      )}

      {/* Helper text */}
      {helperText && !error && (
        <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
          {helperText}
        </p>
      )}
    </div>
  );
};

export default DatePickerField;