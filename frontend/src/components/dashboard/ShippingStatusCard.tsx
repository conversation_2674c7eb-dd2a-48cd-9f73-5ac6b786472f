import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import type { ShippingStatusDistribution } from '../../types/dashboard';

interface ShippingStatusCardProps {
  data: ShippingStatusDistribution[];
  loading: boolean;
}

const ShippingStatusCard = ({ data, loading }: ShippingStatusCardProps) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Delivered':
        return 'bg-green-500';
      case 'Info Received':
        return 'bg-blue-500';
      case 'In Transit':
        return 'bg-orange-500';
      case 'Not Sent':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getStatusTextColor = (status: string) => {
    switch (status) {
      case 'Delivered':
        return 'text-green-600 dark:text-green-400';
      case 'Info Received':
        return 'text-blue-600 dark:text-blue-400';
      case 'In Transit':
        return 'text-orange-600 dark:text-orange-400';
      case 'Not Sent':
        return 'text-red-600 dark:text-red-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Shipping Status Overview</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="animate-pulse">
                <div className="flex justify-between mb-2">
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-24"></div>
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-16"></div>
                </div>
                <div className="h-2 bg-gray-200 dark:bg-gray-700 rounded"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  const totalOrders = data.reduce((sum, item) => sum + item.count, 0);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Shipping Status Overview</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {data.map((item) => (
            <div key={item.status}>
              <div className="flex justify-between items-center mb-2">
                <span className={`font-medium ${getStatusTextColor(item.status)}`}>
                  {item.status}
                </span>
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium">{item.count}</span>
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    ({item.percentage}%)
                  </span>
                </div>
              </div>
              <div className="relative">
                <div className="h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                  <div
                    className={`h-full ${getStatusColor(item.status)} transition-all duration-500 ease-out`}
                    style={{ width: `${item.percentage}%` }}
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
        {totalOrders > 0 && (
          <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600 dark:text-gray-400">Total Orders</span>
              <span className="text-sm font-medium">{totalOrders}</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ShippingStatusCard;