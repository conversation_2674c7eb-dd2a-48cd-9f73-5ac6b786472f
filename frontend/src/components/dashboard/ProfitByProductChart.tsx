import React from 'react';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, <PERSON><PERSON><PERSON>s, CartesianGrid, Tooltip, ResponsiveContainer, Cell } from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';

interface ProfitByProductChartProps {
  data: Record<string, number>;
}

const ProfitByProductChart: React.FC<ProfitByProductChartProps> = ({ data }) => {
  // Transform the object data into array format for Recharts
  const chartData = Object.entries(data).map(([name, profit]) => ({
    name,
    profit
  }));

  // Custom tooltip to format currency
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white dark:bg-gray-800 p-2 border border-gray-200 dark:border-gray-700 rounded shadow-sm">
          <p className="text-sm font-medium">{label}</p>
          <p className="text-sm text-blue-600 dark:text-blue-400">
            ${payload[0].value.toFixed(2)}
          </p>
        </div>
      );
    }
    return null;
  };

  // Format currency on Y-axis
  const formatYAxis = (value: number) => {
    return `$${value}`;
  };

  // Color for bars - responsive to dark mode
  const BAR_COLOR = '#4A9EFF'; // Custom blue that works well in both modes

  return (
    <Card>
      <CardHeader>
        <CardTitle>Profit by Product</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-80 w-full">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={chartData}
              margin={{
                top: 20,
                right: 30,
                left: 40,
                bottom: 5,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" className="stroke-gray-200 dark:stroke-gray-600" />
              <XAxis 
                dataKey="name" 
                className="text-sm text-gray-600 dark:text-gray-400"
                tick={{ fill: 'currentColor', fontSize: 12 }}
                axisLine={{ stroke: 'currentColor', strokeWidth: 1 }}
              />
              <YAxis 
                tickFormatter={formatYAxis}
                className="text-sm text-gray-600 dark:text-gray-400"
                tick={{ fill: 'currentColor', fontSize: 12 }}
                axisLine={{ stroke: 'currentColor', strokeWidth: 1 }}
              />
              <Tooltip content={<CustomTooltip />} />
              <Bar dataKey="profit" radius={[8, 8, 0, 0]}>
                {chartData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={BAR_COLOR} />
                ))}
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
};

export default ProfitByProductChart;