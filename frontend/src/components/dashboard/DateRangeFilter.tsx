import React, { useState, useRef, useEffect } from 'react';
import { DayPicker } from 'react-day-picker';
import type { DateRange as DayPickerDateRange } from 'react-day-picker';
import { format, subDays } from 'date-fns';
import { DateFilter, DateRange } from '../../api/dashboard';
import 'react-day-picker/dist/style.css';
import '../../styles/datepicker.css';

interface DateRangeFilterProps {
  value: DateFilter;
  onChange: (filter: DateFilter) => void;
}

const DateRangeFilter: React.FC<DateRangeFilterProps> = ({ value, onChange }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<'presets' | 'single' | 'range'>('presets');
  const [selectedSingleDate, setSelectedSingleDate] = useState<Date | undefined>();
  const [selectedRange, setSelectedRange] = useState<DayPickerDateRange | undefined>();
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Initialize selected values based on current filter
  useEffect(() => {
    if (value.type === 'custom' && value.startDate) {
      setSelectedSingleDate(value.startDate);
    } else if (value.type === 'range' && value.startDate && value.endDate) {
      setSelectedRange({ from: value.startDate, to: value.endDate });
    }
  }, [value]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const getDisplayText = () => {
    switch (value.type) {
      case 'today':
        return 'Today';
      case 'yesterday':
        return 'Yesterday';
      case 'last7Days':
        return 'Last 7 Days';
      case 'last30Days':
        return 'Last 30 Days';
      case 'lifetime':
        return 'Lifetime';
      case 'custom':
        return value.startDate ? format(value.startDate, 'MMM dd, yyyy') : 'Select Date';
      case 'range':
        if (value.startDate && value.endDate) {
          return `${format(value.startDate, 'MMM dd')} - ${format(value.endDate, 'MMM dd, yyyy')}`;
        }
        return 'Select Range';
      default:
        return 'Today';
    }
  };

  const handlePresetSelect = (type: DateRange) => {
    const today = new Date();
    let filter: DateFilter = { type };

    switch (type) {
      case 'yesterday':
        filter = {
          type,
          startDate: subDays(today, 1),
          endDate: subDays(today, 1)
        };
        break;
      case 'last7Days':
        filter = {
          type,
          startDate: subDays(today, 6),
          endDate: today
        };
        break;
      case 'last30Days':
        filter = {
          type,
          startDate: subDays(today, 29),
          endDate: today
        };
        break;
      case 'lifetime':
        filter = { type };
        break;
      default:
        filter = { type };
    }

    onChange(filter);
    setIsOpen(false);
  };

  const handleSingleDateSelect = (date: Date | undefined) => {
    setSelectedSingleDate(date);
    if (date) {
      onChange({
        type: 'custom',
        startDate: date,
        endDate: date
      });
      setIsOpen(false);
    }
  };

  const handleRangeSelect = (range: DayPickerDateRange | undefined) => {
    setSelectedRange(range);
    if (range?.from && range.to) {
      // Check if this is a real range (different dates) or just the first click
      const fromDate = range.from.toDateString();
      const toDate = range.to.toDateString();
      
      if (fromDate === toDate && !selectedRange?.from) {
        // This is the first click, don't close the picker
        return;
      }
      
      // Both dates selected, apply the filter
      onChange({
        type: 'range',
        startDate: range.from,
        endDate: range.to
      });
      setIsOpen(false);
    }
    // If only one date is selected, keep the picker open
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`
          flex items-center gap-2 px-4 py-2.5 
          bg-white dark:bg-gray-800 
          border border-gray-200 dark:border-gray-700 
          rounded-lg shadow-sm
          hover:bg-gray-50 dark:hover:bg-gray-700 
          focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent
          transition-all duration-200
          text-sm font-medium text-gray-700 dark:text-gray-200
          ${isOpen ? 'ring-2 ring-blue-500 border-transparent' : ''}
        `}
      >
        <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
        {getDisplayText()}
        <svg
          className={`w-4 h-4 text-gray-400 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </button>

      {isOpen && (
        <div className="absolute top-full mt-2 right-0 bg-white dark:bg-gray-800 rounded-xl shadow-xl border border-gray-200 dark:border-gray-700 z-50 min-w-[320px] overflow-hidden">
          {/* Tab Navigation */}
          <div className="flex bg-gray-50 dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
            <button
              onClick={() => setActiveTab('presets')}
              className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
                activeTab === 'presets'
                  ? 'text-blue-600 dark:text-blue-400 bg-white dark:bg-gray-800 border-b-2 border-blue-600'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200'
              }`}
            >
              Quick Select
            </button>
            <button
              onClick={() => setActiveTab('single')}
              className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
                activeTab === 'single'
                  ? 'text-blue-600 dark:text-blue-400 bg-white dark:bg-gray-800 border-b-2 border-blue-600'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200'
              }`}
            >
              Single Date
            </button>
            <button
              onClick={() => setActiveTab('range')}
              className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
                activeTab === 'range'
                  ? 'text-blue-600 dark:text-blue-400 bg-white dark:bg-gray-800 border-b-2 border-blue-600'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200'
              }`}
            >
              Date Range
            </button>
          </div>

          <div className="p-4">
            {activeTab === 'presets' && (
              <div className="space-y-2">
                <button
                  onClick={() => handlePresetSelect('today')}
                  className={`w-full text-left px-4 py-3 rounded-lg text-sm font-medium transition-all ${
                    value.type === 'today' 
                      ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' 
                      : 'hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300'
                  }`}
                >
                  Today
                </button>
                <button
                  onClick={() => handlePresetSelect('yesterday')}
                  className={`w-full text-left px-4 py-3 rounded-lg text-sm font-medium transition-all ${
                    value.type === 'yesterday' 
                      ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' 
                      : 'hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300'
                  }`}
                >
                  Yesterday
                </button>
                <button
                  onClick={() => handlePresetSelect('last7Days')}
                  className={`w-full text-left px-4 py-3 rounded-lg text-sm font-medium transition-all ${
                    value.type === 'last7Days' 
                      ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' 
                      : 'hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300'
                  }`}
                >
                  Last 7 Days
                </button>
                <button
                  onClick={() => handlePresetSelect('last30Days')}
                  className={`w-full text-left px-4 py-3 rounded-lg text-sm font-medium transition-all ${
                    value.type === 'last30Days' 
                      ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' 
                      : 'hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300'
                  }`}
                >
                  Last 30 Days
                </button>
                <button
                  onClick={() => handlePresetSelect('lifetime')}
                  className={`w-full text-left px-4 py-3 rounded-lg text-sm font-medium transition-all ${
                    value.type === 'lifetime' 
                      ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' 
                      : 'hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300'
                  }`}
                >
                  Lifetime
                </button>
              </div>
            )}

            {activeTab === 'single' && (
              <div>
                <DayPicker
                  mode="single"
                  selected={selectedSingleDate}
                  onSelect={handleSingleDateSelect}
                  className="rdp-custom"
                />
              </div>
            )}

            {activeTab === 'range' && (
              <div>
                <DayPicker
                  mode="range"
                  selected={selectedRange}
                  onSelect={handleRangeSelect}
                  className="rdp-custom"
                  footer={
                    selectedRange?.from && !selectedRange?.to && (
                      <p className="text-sm text-gray-500 dark:text-gray-400 mt-3 text-center">
                        Select an end date
                      </p>
                    )
                  }
                />
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default DateRangeFilter;