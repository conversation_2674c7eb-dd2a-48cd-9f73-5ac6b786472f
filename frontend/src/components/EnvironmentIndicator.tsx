import React, { useState } from 'react';

export const EnvironmentIndicator: React.FC = () => {
  const [isMinimized, setIsMinimized] = useState(false);
  
  // Detect environment based on port
  const port = window.location.port;
  const isProduction = port === '5174';
  const isDevelopment = port === '5173';
  
  // Don't show indicator if not on expected ports
  if (!isProduction && !isDevelopment) {
    return null;
  }
  
  const environment = isProduction ? 'production' : 'development';
  
  return (
    <button
      onClick={() => setIsMinimized(!isMinimized)}
      className={`
        px-3 py-1.5 rounded-md
        backdrop-blur-sm shadow-lg
        text-xs font-semibold tracking-wider
        transition-all duration-200
        hover:shadow-xl hover:scale-105
        focus:outline-none focus:ring-2 focus:ring-offset-2
        ${isMinimized ? 'opacity-50 hover:opacity-100' : ''}
        ${isProduction 
          ? 'bg-green-500/20 text-green-600 dark:bg-green-500/20 dark:text-green-400 hover:bg-green-500/30 dark:hover:bg-green-500/30 focus:ring-green-500 border border-green-500/30 dark:border-green-400/30' 
          : 'bg-amber-500/20 text-amber-600 dark:bg-amber-500/20 dark:text-amber-400 hover:bg-amber-500/30 dark:hover:bg-amber-500/30 focus:ring-amber-500 border border-amber-500/30 dark:border-amber-400/30'
        }
      `}
      aria-label={`Environment: ${environment}. Click to ${isMinimized ? 'expand' : 'minimize'}`}
    >
      <div className="flex items-center gap-1.5">
        <span className={`
          inline-block w-2 h-2 rounded-full animate-pulse
          ${isProduction 
            ? 'bg-green-600 dark:bg-green-400' 
            : 'bg-amber-600 dark:bg-amber-400'
          }
        `} />
        <span className={`${isMinimized ? 'hidden' : ''}`}>
          {environment}
        </span>
      </div>
    </button>
  );
};