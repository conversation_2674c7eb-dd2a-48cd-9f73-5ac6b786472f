import React, { useEffect } from 'react';
import { CheckCircle, XCircle, X } from 'lucide-react';

export interface ToastProps {
  id: string;
  message: string;
  type: 'success' | 'error';
  onDismiss: (id: string) => void;
  duration?: number;
}

export const Toast: React.FC<ToastProps> = ({ 
  id, 
  message, 
  type, 
  onDismiss, 
  duration = 4000 
}) => {
  useEffect(() => {
    const timer = setTimeout(() => {
      onDismiss(id);
    }, duration);

    return () => clearTimeout(timer);
  }, [id, duration, onDismiss]);

  return (
    <div
      className={`
        transform transition-all duration-300 ease-in-out
        pointer-events-auto w-full max-w-xs sm:max-w-sm overflow-hidden rounded-lg shadow-lg
        ${type === 'success' 
          ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800' 
          : 'bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800'
        }
      `}
    >
      <div className="p-3 sm:p-4">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            {type === 'success' ? (
              <CheckCircle className="h-5 w-5 text-green-400 dark:text-green-300" />
            ) : (
              <XCircle className="h-5 w-5 text-red-400 dark:text-red-300" />
            )}
          </div>
          <div className="ml-3 flex-1 min-w-0 pt-0.5">
            <p className={`text-sm font-medium break-words ${
              type === 'success' 
                ? 'text-green-800 dark:text-green-200' 
                : 'text-red-800 dark:text-red-200'
            }`}>
              {message}
            </p>
          </div>
          <div className="ml-2 sm:ml-4 flex flex-shrink-0">
            <button
              type="button"
              className={`inline-flex rounded-md p-1 sm:p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                type === 'success' 
                  ? 'text-green-500 hover:bg-green-100 dark:hover:bg-green-900/40 focus:ring-green-600' 
                  : 'text-red-500 hover:bg-red-100 dark:hover:bg-red-900/40 focus:ring-red-600'
              }`}
              onClick={() => onDismiss(id)}
            >
              <span className="sr-only">Dismiss</span>
              <X className="h-4 w-4 sm:h-5 sm:w-5" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};