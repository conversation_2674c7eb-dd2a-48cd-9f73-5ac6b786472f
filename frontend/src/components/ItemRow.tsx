import React, { useMemo } from 'react';
import { Minus, Plus } from 'lucide-react';
import type { PricingItem } from '../api/client';

interface ItemRowProps {
  index: number;
  item: {
    code: string;
    dose: string;
    qty: number;
  };
  pricing: PricingItem[];
  onChange: (index: number, field: string, value: string | number) => void;
  onRemove: (index: number) => void;
}

export const ItemRow: React.FC<ItemRowProps> = ({
  index,
  item,
  pricing,
  onChange,
  onRemove,
}) => {
  // Get unique products
  const products = useMemo(() => {
    const uniqueProducts = [...new Set(pricing.map(p => p.product))];
    return uniqueProducts.sort();
  }, [pricing]);

  // Get doses for selected product
  const availableDoses = useMemo(() => {
    if (!item.code) return [];
    
    const selectedPricing = pricing.find(p => p.code === item.code);
    if (!selectedPricing) return [];
    
    return pricing
      .filter(p => p.product === selectedPricing.product)
      .map(p => ({ code: p.code, dose: p.dose }))
      .sort((a, b) => {
        const aNum = parseInt(a.dose);
        const bNum = parseInt(b.dose);
        return aNum - bNum;
      });
  }, [item.code, pricing]);

  // Calculate line total
  const lineTotal = useMemo(() => {
    const selectedPricing = pricing.find(
      p => p.code === item.code && p.dose === item.dose
    );
    return selectedPricing ? selectedPricing.buyingPrice * item.qty : 0;
  }, [item.code, item.dose, item.qty, pricing]);

  const handleProductChange = (productName: string) => {
    // Find the first item for this product
    const firstItem = pricing.find(p => p.product === productName);
    if (firstItem) {
      onChange(index, 'code', firstItem.code);
      onChange(index, 'dose', firstItem.dose);
    }
  };

  const handleDoseChange = (code: string) => {
    const selected = pricing.find(p => p.code === code);
    if (selected) {
      onChange(index, 'code', code);
      onChange(index, 'dose', selected.dose);
    }
  };

  const incrementQty = () => {
    if (item.qty < 50) {
      onChange(index, 'qty', item.qty + 1);
    }
  };

  const decrementQty = () => {
    if (item.qty > 1) {
      onChange(index, 'qty', item.qty - 1);
    }
  };

  const selectedProduct = pricing.find(p => p.code === item.code)?.product || '';

  return (
    <div className="p-4 bg-white dark:bg-dark-bg-1 rounded-lg border border-gray-200 dark:border-dark-border-subtle dark:shadow-dark-sm">
      <div className="grid grid-cols-1 sm:grid-cols-12 gap-4 sm:items-center">
        <div className="sm:col-span-2">
          <label className="block text-sm font-medium text-gray-700 dark:text-dark-text-primary mb-1">
            Product
          </label>
          <select
            value={selectedProduct}
            onChange={(e) => handleProductChange(e.target.value)}
            className="block w-full rounded-lg border border-gray-300 dark:border-dark-border-default bg-white dark:bg-dark-bg-2 px-3 py-2 text-base sm:text-sm text-gray-900 dark:text-dark-text-primary shadow-sm focus:outline-none focus:border-dark-accent-blue focus:ring-2 focus:ring-dark-accent-blue focus:ring-offset-0 transition-colors duration-200"
        >
          <option value="">Select product</option>
          {products.map((product) => (
            <option key={product} value={product}>
              {product}
            </option>
          ))}
          </select>
        </div>

        <div className="sm:col-span-2">
        <label className="block text-sm font-medium text-gray-700 dark:text-dark-text-primary mb-1">
          Code
        </label>
          <div className="px-3 py-3 sm:py-2 border border-gray-300 dark:border-dark-border-default rounded-lg bg-gray-50 dark:bg-dark-bg-2 text-sm font-mono text-gray-900 dark:text-dark-text-primary">
            {item.code || '-'}
          </div>
        </div>

        <div className="sm:col-span-2">
        <label className="block text-sm font-medium text-gray-700 dark:text-dark-text-primary mb-1">
          Dose
        </label>
          <select
            value={item.code}
            onChange={(e) => handleDoseChange(e.target.value)}
            disabled={!selectedProduct}
            className="block w-full rounded-lg border border-gray-300 dark:border-dark-border-default bg-white dark:bg-dark-bg-2 px-3 py-2 text-base sm:text-sm text-gray-900 dark:text-dark-text-primary shadow-sm focus:outline-none focus:border-dark-accent-blue focus:ring-2 focus:ring-dark-accent-blue focus:ring-offset-0 transition-colors duration-200 disabled:bg-gray-100 dark:disabled:bg-dark-bg-3 disabled:text-gray-500 dark:disabled:text-dark-text-disabled"
        >
          <option value="">Select dose</option>
          {availableDoses.map((dose) => (
            <option key={dose.code} value={dose.code}>
              {dose.dose}
            </option>
          ))}
          </select>
        </div>

        <div className="sm:col-span-2">
        <label className="block text-sm font-medium text-gray-700 dark:text-dark-text-primary mb-1">
          Quantity
        </label>
          <div className="flex items-center">
            <button
              type="button"
              onClick={decrementQty}
              className="p-2 sm:p-1 rounded-l-md border border-r-0 border-gray-300 dark:border-dark-border-default hover:bg-gray-100 dark:hover:bg-dark-bg-hover focus:outline-none focus:ring-2 focus:ring-dark-accent-blue text-gray-600 dark:text-dark-text-primary"
          >
              <Minus className="h-5 w-5 sm:h-4 sm:w-4" />
            </button>
            <div className="px-4 sm:px-3 py-2 sm:py-1 border-t border-b border-gray-300 dark:border-dark-border-default bg-white dark:bg-dark-bg-2 text-center w-16 sm:w-12 text-base sm:text-sm text-gray-900 dark:text-dark-text-primary">
              {item.qty}
            </div>
            <button
              type="button"
              onClick={incrementQty}
              className="p-2 sm:p-1 rounded-r-md border border-l-0 border-gray-300 dark:border-dark-border-default hover:bg-gray-100 dark:hover:bg-dark-bg-hover focus:outline-none focus:ring-2 focus:ring-dark-accent-blue text-gray-600 dark:text-dark-text-primary"
          >
              <Plus className="h-5 w-5 sm:h-4 sm:w-4" />
            </button>
          </div>
        </div>

        <div className="sm:col-span-2 sm:text-right">
        <label className="block text-sm font-medium text-gray-700 dark:text-dark-text-primary mb-1">
          Line Total
        </label>
          <div className="text-lg font-semibold text-gray-900 dark:text-dark-text-primary">
            ${lineTotal.toFixed(2)}
          </div>
        </div>

        <div className="sm:col-span-2 text-right mt-4 sm:mt-0">
          <button
            type="button"
            onClick={() => onRemove(index)}
            className="inline-flex items-center justify-center w-full sm:w-auto px-3 py-2 border border-red-300 dark:border-red-900/20 text-sm leading-4 font-medium rounded-md text-red-700 dark:text-dark-accent-red bg-white dark:bg-dark-bg-1 hover:bg-red-50 dark:hover:bg-red-900/20 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-dark-accent-red"
        >
            Remove
          </button>
        </div>
      </div>
    </div>
  );
};