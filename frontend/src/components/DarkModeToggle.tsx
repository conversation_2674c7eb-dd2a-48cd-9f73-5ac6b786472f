import React from 'react';
import { <PERSON>, <PERSON>, Clock } from 'lucide-react';
import { useDarkMode } from '../contexts/DarkModeContext';

export const DarkModeToggle: React.FC = () => {
  const { isDarkMode, toggleDarkMode, autoDarkMode, darkModeStartTime, darkModeEndTime } = useDarkMode();

  const getModeDescription = () => {
    if (autoDarkMode) {
      return `Auto mode (${darkModeStartTime} - ${darkModeEndTime})`;
    }
    return isDarkMode ? 'Dark mode' : 'Light mode';
  };

  return (
    <div className="relative group">
      <button
        onClick={toggleDarkMode}
        className="relative inline-flex items-center justify-center p-2 rounded-lg text-gray-500 hover:text-gray-700 hover:bg-gray-100 dark:text-dark-text-secondary dark:hover:bg-dark-bg-hover dark:hover:text-dark-text-primary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-dark-accent-blue dark:focus:ring-offset-dark-bg-3 transition-colors duration-200"
        aria-label={`Switch to ${isDarkMode ? 'light' : 'dark'} mode`}
        title={`${getModeDescription()} - Click to manually toggle`}
      >
        {autoDarkMode && (
          <Clock className="absolute -top-1 -right-1 h-3 w-3 text-dark-accent-blue animate-pulse" />
        )}
        {isDarkMode ? (
          <Sun className="h-6 w-6 text-dark-accent-yellow transition-transform duration-200 hover:scale-110" />
        ) : (
          <Moon className="h-6 w-6 transition-transform duration-200 hover:scale-110" />
        )}
      </button>
      
      {/* Tooltip */}
      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 text-xs text-white bg-gray-900 rounded-md opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
        {getModeDescription()}
        <div className="absolute top-full left-1/2 transform -translate-x-1/2 -mt-1">
          <div className="border-4 border-transparent border-t-gray-900"></div>
        </div>
      </div>
    </div>
  );
};