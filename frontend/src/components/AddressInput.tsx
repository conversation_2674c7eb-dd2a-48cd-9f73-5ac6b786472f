import React, { useState, useEffect, useRef } from 'react';
import { Loader2, Info, X, Search } from 'lucide-react';
import { placesApi } from '../api/client';
import type { PlacePrediction } from '../api/client';
import { getCountryConfig } from '../utils/countryConfig';
import { COUNTRIES, getCountryFlag, POPULAR_COUNTRIES, getCountryByLocale } from '../utils/countries';
import { trackCountryUsage, getCountriesByUsage, getRecentCountries } from '../utils/countryUsage';

interface AddressInputProps {
  customerName: string;
  email: string;
  street1: string;
  street2: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  onChange: (field: string, value: string) => void;
  errors?: Record<string, string>;
}


const US_STATES = [
  { code: 'AL', name: 'Alabama' },
  { code: 'AK', name: 'Alaska' },
  { code: 'A<PERSON>', name: 'Arizona' },
  { code: 'AR', name: 'Arkansas' },
  { code: 'CA', name: 'California' },
  { code: 'CO', name: 'Colorado' },
  { code: 'CT', name: 'Connecticut' },
  { code: 'DE', name: 'Delaware' },
  { code: 'FL', name: 'Florida' },
  { code: 'GA', name: 'Georgia' },
  { code: 'HI', name: 'Hawaii' },
  { code: 'ID', name: 'Idaho' },
  { code: 'IL', name: 'Illinois' },
  { code: 'IN', name: 'Indiana' },
  { code: 'IA', name: 'Iowa' },
  { code: 'KS', name: 'Kansas' },
  { code: 'KY', name: 'Kentucky' },
  { code: 'LA', name: 'Louisiana' },
  { code: 'ME', name: 'Maine' },
  { code: 'MD', name: 'Maryland' },
  { code: 'MA', name: 'Massachusetts' },
  { code: 'MI', name: 'Michigan' },
  { code: 'MN', name: 'Minnesota' },
  { code: 'MS', name: 'Mississippi' },
  { code: 'MO', name: 'Missouri' },
  { code: 'MT', name: 'Montana' },
  { code: 'NE', name: 'Nebraska' },
  { code: 'NV', name: 'Nevada' },
  { code: 'NH', name: 'New Hampshire' },
  { code: 'NJ', name: 'New Jersey' },
  { code: 'NM', name: 'New Mexico' },
  { code: 'NY', name: 'New York' },
  { code: 'NC', name: 'North Carolina' },
  { code: 'ND', name: 'North Dakota' },
  { code: 'OH', name: 'Ohio' },
  { code: 'OK', name: 'Oklahoma' },
  { code: 'OR', name: 'Oregon' },
  { code: 'PA', name: 'Pennsylvania' },
  { code: 'RI', name: 'Rhode Island' },
  { code: 'SC', name: 'South Carolina' },
  { code: 'SD', name: 'South Dakota' },
  { code: 'TN', name: 'Tennessee' },
  { code: 'TX', name: 'Texas' },
  { code: 'UT', name: 'Utah' },
  { code: 'VT', name: 'Vermont' },
  { code: 'VA', name: 'Virginia' },
  { code: 'WA', name: 'Washington' },
  { code: 'WV', name: 'West Virginia' },
  { code: 'WI', name: 'Wisconsin' },
  { code: 'WY', name: 'Wyoming' },
];

export const AddressInput: React.FC<AddressInputProps> = ({
  customerName,
  email,
  street1,
  street2,
  city,
  state,
  postalCode,
  country,
  onChange,
  errors = {},
}) => {
  const [searchInput, setSearchInput] = useState('');
  const countryConfig = getCountryConfig(country);
  const [predictions, setPredictions] = useState<PlacePrediction[]>([]);
  const [showPredictions, setShowPredictions] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const [showPostalTooltip, setShowPostalTooltip] = useState(false);
  const [selectedPredictionIndex, setSelectedPredictionIndex] = useState(-1);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  
  // Get smart sorted countries
  const sortedCountries = getCountriesByUsage(COUNTRIES);
  const recentCountries = getRecentCountries(COUNTRIES);

  // Auto-detect country on mount if not set
  useEffect(() => {
    if (!country || country === 'United States') {
      const detectedCountry = getCountryByLocale();
      if (detectedCountry && detectedCountry !== country) {
        onChange('country', detectedCountry);
      }
    }
  }, []); // Only run on mount
  
  // Track country usage when it changes
  useEffect(() => {
    if (country) {
      trackCountryUsage(country);
    }
  }, [country]);

  // Handle clicks outside dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowPredictions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Debounced search
  useEffect(() => {
    if (searchInput.length < 3) {
      setPredictions([]);
      return;
    }

    // Clear previous timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // Set new timeout for debouncing
    searchTimeoutRef.current = setTimeout(async () => {
      setIsSearching(true);
      try {
        const result = await placesApi.autocomplete(searchInput);
        setPredictions(result.predictions);
        setShowPredictions(true);
      } catch (error) {
        console.error('Failed to fetch predictions:', error);
        setPredictions([]);
      } finally {
        setIsSearching(false);
      }
    }, 300);

    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, [searchInput]);

  const handlePredictionSelect = async (prediction: PlacePrediction) => {
    try {
      const details = await placesApi.getDetails(prediction.placeId);
      
      // Update country first as it affects other field behavior
      onChange('country', details.country);
      
      // Get the country config for the selected country
      const selectedCountryConfig = getCountryConfig(details.country);
      
      // Handle state mapping - Google returns codes, but we need to match with our dropdown
      let mappedState = details.state;
      if (selectedCountryConfig.hasStates && selectedCountryConfig.states && details.state) {
        // Try to find if the returned state is already a code
        const stateByCode = selectedCountryConfig.states.find(
          s => s.code === details.state
        );
        
        if (stateByCode) {
          // It's already a valid code
          mappedState = details.state;
        } else {
          // Try to find by name (case-insensitive)
          const stateByName = selectedCountryConfig.states.find(
            s => s.name.toLowerCase() === details.state.toLowerCase()
          );
          
          if (stateByName) {
            // Convert name to code for the dropdown
            mappedState = stateByName.code;
          } else {
            // If we can't map it, clear it and let user select manually
            console.warn(`Could not map state "${details.state}" for country "${details.country}"`);
            mappedState = '';
          }
        }
      } else if (!selectedCountryConfig.hasStates) {
        // Country doesn't use states, clear the field
        mappedState = '';
      }
      
      // Update form fields
      onChange('street1', details.street1);
      onChange('city', details.city);
      onChange('state', mappedState);
      onChange('postalCode', details.postalCode);
      
      // Clear search and hide predictions
      setSearchInput('');
      setShowPredictions(false);
      setPredictions([]);
    } catch (error) {
      console.error('Failed to fetch place details:', error);
    }
  };
  
  // Handle keyboard navigation in address search
  const handleSearchKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (!showPredictions || predictions.length === 0) return;
    
    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedPredictionIndex(prev => 
          prev < predictions.length - 1 ? prev + 1 : 0
        );
        break;
        
      case 'ArrowUp':
        e.preventDefault();
        setSelectedPredictionIndex(prev => 
          prev > 0 ? prev - 1 : predictions.length - 1
        );
        break;
        
      case 'Enter':
        e.preventDefault();
        if (selectedPredictionIndex >= 0 && selectedPredictionIndex < predictions.length) {
          handlePredictionSelect(predictions[selectedPredictionIndex]);
        }
        break;
        
      case 'Escape':
        e.preventDefault();
        setShowPredictions(false);
        setSelectedPredictionIndex(-1);
        break;
    }
  };
  return (
    <div className="space-y-6 sm:space-y-4">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-dark-text-primary">Shipping Address</h3>
      
      <div>
        <label htmlFor="customerName" className="block text-sm font-medium text-gray-700 dark:text-dark-text-primary">
          Customer Name *
        </label>
        <input
          type="text"
          id="customerName"
          value={customerName}
          onChange={(e) => onChange('customerName', e.target.value)}
          autoComplete="name"
          autoCapitalize="words"
          className={`mt-1 block w-full rounded-lg border px-3 py-2 text-base sm:text-sm focus:ring-2 focus:ring-opacity-20 transition-colors duration-200 ${
            errors.customerName
              ? 'border-red-300 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 bg-white dark:bg-dark-bg-2'
              : 'border-gray-300 dark:border-dark-border-default text-gray-900 dark:text-dark-text-primary focus:border-dark-accent-blue focus:ring-dark-accent-blue bg-white dark:bg-dark-bg-2 placeholder-gray-400 dark:placeholder-dark-text-disabled'
          }`}
          placeholder="John Doe"
        />
        {errors.customerName && (
          <p className="mt-1 text-sm text-red-600">{errors.customerName}</p>
        )}
      </div>
      
      <div>
        <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-dark-text-primary">
          Email (Optional)
        </label>
        <input
          type="email"
          id="email"
          value={email}
          onChange={(e) => onChange('email', e.target.value)}
          autoComplete="email"
          className={`mt-1 block w-full rounded-lg border px-3 py-2 text-base sm:text-sm focus:ring-2 focus:ring-opacity-20 transition-colors duration-200 ${
            errors.email
              ? 'border-red-300 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 bg-white dark:bg-dark-bg-2'
              : 'border-gray-300 dark:border-dark-border-default text-gray-900 dark:text-dark-text-primary focus:border-dark-accent-blue focus:ring-dark-accent-blue bg-white dark:bg-dark-bg-2 placeholder-gray-400 dark:placeholder-dark-text-disabled'
          }`}
          placeholder="<EMAIL>"
        />
        {errors.email && (
          <p className="mt-1 text-sm text-red-600">{errors.email}</p>
        )}
      </div>
      
      {/* Address Search - Highlighted */}
      <div className="relative" ref={dropdownRef}>
        <div className="flex items-center gap-2">
          <label htmlFor="addressSearch" className="flex items-center gap-1 text-sm font-medium text-indigo-600 dark:text-indigo-400">
            <Search className="w-4 h-4" />
            Search Address (Quick Fill)
          </label>
          <span className="text-xs bg-indigo-100 dark:bg-indigo-900/30 text-indigo-700 dark:text-indigo-300 px-2 py-0.5 rounded-full">
            Recommended
          </span>
        </div>
        <div className="mt-1 relative">
          <input
            type="text"
            id="addressSearch"
            value={searchInput}
            onChange={(e) => setSearchInput(e.target.value)}
            onKeyDown={handleSearchKeyDown}
            placeholder="Start typing an address to auto-fill form..."
            className="block w-full rounded-lg border-2 border-dark-accent-blue/30 dark:border-indigo-600/50 bg-indigo-50/30 dark:bg-indigo-900/10 px-3 py-2 text-base sm:text-sm text-gray-900 dark:text-dark-text-primary placeholder-indigo-400 dark:placeholder-indigo-300/70 focus:border-dark-accent-blue focus:ring-2 focus:ring-indigo-500 focus:ring-opacity-30 transition-all duration-200 hover:border-indigo-400 dark:hover:border-indigo-500"
            role="combobox"
            aria-expanded={showPredictions}
            aria-controls="address-suggestions"
            aria-autocomplete="list"
            aria-describedby="address-search-help"
            aria-activedescendant={selectedPredictionIndex >= 0 ? `prediction-${selectedPredictionIndex}` : undefined}
          />
          {isSearching && (
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2" aria-hidden="true">
              <Loader2 className="animate-spin h-5 w-5 text-gray-400" />
            </div>
          )}
          <span id="address-search-help" className="sr-only">
            Type at least 3 characters to search for addresses. Use arrow keys to navigate suggestions.
          </span>
        </div>
        
        {/* Predictions Dropdown */}
        {showPredictions && predictions.length > 0 && (
          <div 
            id="address-suggestions"
            className="absolute z-10 mt-1 w-full bg-white dark:bg-dark-bg-2 shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none sm:text-sm"
            role="listbox"
            aria-label="Address suggestions"
          >
            {predictions.map((prediction, index) => (
              <button
                key={prediction.placeId}
                id={`prediction-${index}`}
                type="button"
                onClick={() => handlePredictionSelect(prediction)}
                className={`w-full text-left px-4 py-2 cursor-pointer transition-colors duration-150 focus:outline-none ${
                  index === selectedPredictionIndex
                    ? 'bg-indigo-100 dark:bg-dark-accent-blue/20 text-indigo-900 dark:text-dark-accent-blue'
                    : 'hover:bg-gray-100 dark:hover:bg-dark-bg-hover text-gray-900 dark:text-dark-text-primary'
                }`}
                role="option"
                aria-selected={index === selectedPredictionIndex}
                tabIndex={-1}
              >
                <div className="font-medium">
                  {prediction.mainText}
                </div>
                <div className="text-sm opacity-75">
                  {prediction.secondaryText}
                </div>
              </button>
            ))}
            <div className="px-4 py-2 text-xs text-gray-500 dark:text-dark-text-secondary border-t border-gray-200 dark:border-dark-border-default" aria-hidden="true">
              Powered by Google
            </div>
          </div>
        )}
      </div>

      <div>
        <label htmlFor="street1" className="block text-sm font-medium text-gray-700 dark:text-dark-text-primary">
          Street Address 1 *
        </label>
        <input
          type="text"
          id="street1"
          value={street1}
          onChange={(e) => onChange('street1', e.target.value)}
          autoComplete="address-line1"
          autoCapitalize="words"
          className={`mt-1 block w-full rounded-lg border px-3 py-2 text-base sm:text-sm focus:ring-2 focus:ring-opacity-20 transition-colors duration-200 ${
            errors.street1
              ? 'border-red-300 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 bg-white dark:bg-dark-bg-2'
              : 'border-gray-300 dark:border-dark-border-default text-gray-900 dark:text-dark-text-primary focus:border-dark-accent-blue focus:ring-dark-accent-blue bg-white dark:bg-dark-bg-2 placeholder-gray-400 dark:placeholder-dark-text-disabled'
          }`}
          placeholder="123 Main St"
        />
        {errors.street1 && (
          <p className="mt-1 text-sm text-red-600">{errors.street1}</p>
        )}
      </div>

      <div>
        <label htmlFor="street2" className="block text-sm font-medium text-gray-700 dark:text-dark-text-primary">
          Street Address 2
        </label>
        <input
          type="text"
          id="street2"
          value={street2}
          onChange={(e) => onChange('street2', e.target.value)}
          autoComplete="address-line2"
          autoCapitalize="words"
          className="mt-1 block w-full rounded-lg border border-gray-300 dark:border-dark-border-default px-3 py-2 text-base sm:text-sm text-gray-900 dark:text-dark-text-primary focus:border-dark-accent-blue focus:ring-2 focus:ring-indigo-500 focus:ring-opacity-20 transition-colors duration-200 bg-white dark:bg-dark-bg-2 placeholder-gray-400 dark:placeholder-dark-text-disabled"
          placeholder="Apt, Suite, Unit, etc."
        />
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-6 gap-4">
        <div className="sm:col-span-3">
          <label htmlFor="city" className="block text-sm font-medium text-gray-700 dark:text-dark-text-primary">
            City *
          </label>
          <input
            type="text"
            id="city"
            value={city}
            onChange={(e) => onChange('city', e.target.value)}
            autoComplete="address-level2"
            autoCapitalize="words"
            className={`mt-1 block w-full rounded-lg border px-3 py-2 text-base sm:text-sm focus:ring-2 focus:ring-opacity-20 transition-colors duration-200 ${
              errors.city
                ? 'border-red-300 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 bg-white dark:bg-dark-bg-2'
                : 'border-gray-300 dark:border-dark-border-default text-gray-900 dark:text-dark-text-primary focus:border-dark-accent-blue focus:ring-dark-accent-blue bg-white dark:bg-dark-bg-2 placeholder-gray-400 dark:placeholder-dark-text-disabled'
            }`}
            placeholder="New York"
          />
          {errors.city && (
            <p className="mt-1 text-sm text-red-600">{errors.city}</p>
          )}
        </div>

        {countryConfig.hasStates && (
          <div className="sm:col-span-2">
            <label htmlFor="state" className="block text-sm font-medium text-gray-700 dark:text-dark-text-primary">
              {countryConfig.stateLabel || 'State'} *
            </label>
            <select
              id="state"
              value={state}
              onChange={(e) => onChange('state', e.target.value)}
              className={`mt-1 block w-full rounded-lg border bg-white dark:bg-dark-bg-2 px-3 py-2 text-base sm:text-sm shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-0 transition-colors duration-200 ${
                errors.state
                  ? 'border-red-300 text-red-900 focus:border-red-500 focus:ring-red-500'
                  : 'border-gray-300 dark:border-dark-border-default text-gray-900 dark:text-dark-text-primary focus:border-dark-accent-blue focus:ring-dark-accent-blue'
              }`}
            >
              <option value="">Select {countryConfig.stateLabel?.toLowerCase() || 'state'}</option>
              {(countryConfig.states || US_STATES).map((s) => (
                <option key={s.code} value={s.code}>
                  {s.name}
                </option>
              ))}
            </select>
            {errors.state && (
              <p className="mt-1 text-sm text-red-600">{errors.state}</p>
            )}
          </div>
        )}

        <div className={countryConfig.hasStates ? "sm:col-span-1" : "sm:col-span-3"}>
          <div className="flex items-center justify-between">
            <label htmlFor="postalCode" className="block text-sm font-medium text-gray-700 dark:text-dark-text-primary">
              {countryConfig.postalCodeLabel} *
            </label>
            {countryConfig.postalCodeExample && (
              <div className="relative">
                <button
                  type="button"
                  onMouseEnter={() => setShowPostalTooltip(true)}
                  onMouseLeave={() => setShowPostalTooltip(false)}
                  onClick={(e) => {
                    e.preventDefault();
                    setShowPostalTooltip(!showPostalTooltip);
                  }}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 p-1 -m-1 touch-target"
                  aria-label="Postal code format help"
                >
                  <Info className="h-4 w-4" />
                </button>
                {showPostalTooltip && (
                  <div className="absolute right-0 bottom-full mb-2 w-48 p-2 bg-gray-900 dark:bg-gray-800 text-white text-xs rounded shadow-lg z-10">
                    <div className="font-medium">{countryConfig.postalCodeExample}</div>
                    <button
                      type="button"
                      onClick={() => setShowPostalTooltip(false)}
                      className="absolute top-1 right-1 text-gray-400 hover:text-white md:hidden"
                      aria-label="Close"
                    >
                      <X className="h-3 w-3" />
                    </button>
                    <div className="absolute bottom-0 right-2 translate-y-1/2 rotate-45 w-2 h-2 bg-gray-900 dark:bg-gray-800"></div>
                  </div>
                )}
              </div>
            )}
          </div>
          <input
            type="text"
            id="postalCode"
            value={postalCode}
            onChange={(e) => onChange('postalCode', e.target.value)}
            maxLength={20}
            inputMode={countryConfig.code === 'US' || /^\d+$/.test(countryConfig.postalCodePlaceholder) ? 'numeric' : 'text'}
            autoComplete="postal-code"
            autoCapitalize="characters"
            className={`mt-1 block w-full rounded-lg border px-3 py-2 text-base sm:text-sm focus:ring-2 focus:ring-opacity-20 transition-colors duration-200 ${
              errors.postalCode
                ? 'border-red-300 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 bg-white dark:bg-dark-bg-2'
                : 'border-gray-300 dark:border-dark-border-default text-gray-900 dark:text-dark-text-primary focus:border-dark-accent-blue focus:ring-dark-accent-blue bg-white dark:bg-dark-bg-2 placeholder-gray-400 dark:placeholder-dark-text-disabled'
            }`}
            placeholder={countryConfig.postalCodePlaceholder}
          />
          {errors.postalCode && (
            <p className="mt-1 text-sm text-red-600">{errors.postalCode}</p>
          )}
        </div>
      </div>

      <div className="mt-4">
        <label htmlFor="country" className="block text-sm font-medium text-gray-700 dark:text-dark-text-primary">
          Country *
        </label>
        <select
          id="country"
          value={country}
          onChange={(e) => onChange('country', e.target.value)}
          className={`mt-1 block w-full rounded-lg border bg-white dark:bg-dark-bg-2 px-3 py-2 text-base sm:text-sm shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-0 transition-colors duration-200 ${
            errors.country
              ? 'border-red-300 text-red-900 focus:border-red-500 focus:ring-red-500'
              : 'border-gray-300 dark:border-dark-border-default text-gray-900 dark:text-dark-text-primary focus:border-dark-accent-blue focus:ring-dark-accent-blue'
          }`}
        >
          {/* Recently used countries (if any) */}
          {recentCountries.length > 0 && (
            <>
              <optgroup label="Recently Used">
                {recentCountries.map((c) => (
                  <option key={`recent-${c.code}`} value={c.name}>
                    {c.flag} {c.name}
                  </option>
                ))}
              </optgroup>
              <option disabled>──────────</option>
            </>
          )}
          
          {/* Popular countries section */}
          <optgroup label="Popular Countries">
            {POPULAR_COUNTRIES.map((countryName) => {
              const c = COUNTRIES.find(country => country.name === countryName);
              return c ? (
                <option key={`popular-${c.code}`} value={c.name}>
                  {c.flag} {c.name}
                </option>
              ) : null;
            })}
          </optgroup>
          
          {/* Divider */}
          <option disabled>──────────</option>
          
          {/* All countries (sorted by usage) */}
          <optgroup label="All Countries">
            {sortedCountries.map((c) => (
              <option key={c.code} value={c.name}>
                {c.flag} {c.name}
              </option>
            ))}
          </optgroup>
        </select>
        {errors.country && (
          <p className="mt-1 text-sm text-red-600">{errors.country}</p>
        )}
      </div>
    </div>
  );
};