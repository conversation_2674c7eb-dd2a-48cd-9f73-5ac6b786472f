import React, { useState, useEffect } from 'react';
import { X, Plus, Trash2, AlertCircle } from 'lucide-react';
import { productsApi } from '../api/productsApi';
import type { Product, CreateVariantData } from '../../../shared/types/product.types';
import { useToastContext } from '../contexts/ToastContext';
import { usePageVisibility } from '../hooks/usePageVisibility';
import { useFormPersistence } from '../hooks/useFormPersistence';

interface ProductModalV2Props {
  isOpen: boolean;
  onClose: () => void;
  onSave: () => void;
  product: Product | null;
}

interface VariantFormData extends CreateVariantData {
  id?: string;
  isNew?: boolean;
}

export const ProductModalV2: React.FC<ProductModalV2Props> = ({
  isOpen,
  onClose,
  onSave,
  product
}) => {
  const { showSuccess, showError } = useToastContext();
  const isPageVisible = usePageVisibility();
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [wasHiddenWithData, setWasHiddenWithData] = useState(false);

  // Use form persistence for product name and variants
  const persistenceKey = product ? `productModal-edit-${product.id}` : 'productModal-new';
  const [productName, setProductName, clearProductName] = useFormPersistence(
    `${persistenceKey}-name`,
    product?.name || '',
    isOpen // Only persist when modal is open
  );
  
  const [variants, setVariants, clearVariants] = useFormPersistence<VariantFormData[]>(
    `${persistenceKey}-variants`,
    product ? product.variants.map(v => ({
      id: v.id,
      dose: v.dose,
      code: v.code,
      buyingPrice: v.buyingPrice,
      sellingPrice: v.sellingPrice,
      isNew: false
    })) : [{
      dose: '',
      code: '',
      buyingPrice: 0,
      sellingPrice: 0,
      isNew: true
    }],
    isOpen // Only persist when modal is open
  );

  // Clear errors when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setErrors({});
    }
  }, [isOpen]);

  // Track visibility changes to prevent unwanted closure
  useEffect(() => {
    if (!isPageVisible && isOpen && (productName || variants.some(v => v.dose || v.code || v.buyingPrice || v.sellingPrice))) {
      // Page became hidden while modal has data
      setWasHiddenWithData(true);
    } else if (isPageVisible && wasHiddenWithData) {
      // Page became visible again after being hidden with data
      setWasHiddenWithData(false);
    }
  }, [isPageVisible, isOpen, productName, variants, wasHiddenWithData]);

  // Handle keyboard events
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Only close on Escape if there's no unsaved data and page is visible
      if (e.key === 'Escape' && !productName && variants.every(v => !v.dose && !v.code && !v.buyingPrice && !v.sellingPrice) && isPageVisible) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [isOpen, productName, variants, onClose, isPageVisible]);

  const addVariant = () => {
    setVariants([...variants, {
      dose: '',
      code: '',
      buyingPrice: 0,
      sellingPrice: 0,
      isNew: true
    }]);
  };

  const removeVariant = (index: number) => {
    setVariants(variants.filter((_, i) => i !== index));
  };

  const updateVariant = (index: number, field: keyof VariantFormData, value: any) => {
    const updated = [...variants];
    updated[index] = { ...updated[index], [field]: value };
    setVariants(updated);
  };

  const generateCode = (index: number) => {
    if (!productName || !variants[index].dose) {
      showError('Please enter product name and dose first');
      return;
    }

    const initials = productName
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase();
    const doseNumber = variants[index].dose.match(/\d+/)?.[0] || '';
    
    const generatedCode = `${initials}${doseNumber}`;
    updateVariant(index, 'code', generatedCode);
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!productName.trim()) {
      newErrors.productName = 'Product name is required';
    }

    if (variants.length === 0) {
      newErrors.variants = 'At least one variant is required';
    }

    variants.forEach((variant, index) => {
      if (!variant.dose.trim()) {
        newErrors[`variant_${index}_dose`] = 'Dose is required';
      }
      if (!variant.code.trim()) {
        newErrors[`variant_${index}_code`] = 'Code is required';
      }
      if (variant.buyingPrice <= 0) {
        newErrors[`variant_${index}_buying`] = 'Valid buying price required';
      }
      if (variant.sellingPrice <= 0) {
        newErrors[`variant_${index}_selling`] = 'Valid selling price required';
      }
      if (variant.sellingPrice <= variant.buyingPrice) {
        newErrors[`variant_${index}_margin`] = 'Selling price must be greater than buying price';
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      if (product) {
        // Update existing product
        if (product.name !== productName) {
          await productsApi.updateName(product.id, productName);
        }

        // Add new variants
        const newVariants = variants.filter(v => v.isNew);
        for (const variant of newVariants) {
          await productsApi.addVariant(product.id, {
            dose: variant.dose,
            code: variant.code,
            buyingPrice: variant.buyingPrice,
            sellingPrice: variant.sellingPrice
          });
        }

        showSuccess('Product updated successfully');
      } else {
        // Create new product
        await productsApi.create({
          name: productName,
          variants: variants.map(v => ({
            dose: v.dose,
            code: v.code,
            buyingPrice: v.buyingPrice,
            sellingPrice: v.sellingPrice
          }))
        });

        showSuccess('Product created successfully');
      }

      // Clear persisted data after successful save
      clearProductName();
      clearVariants();
      
      await onSave();
      // Small delay to ensure toast is displayed
      setTimeout(() => {
        onClose();
      }, 100);
    } catch (error: any) {
      console.error('Error saving product:', error);
      showError(error.response?.data?.error || 'Failed to save product');
    } finally {
      setLoading(false);
    }
  };

  const calculateMargin = (buying: number, selling: number): number => {
    if (selling === 0) return 0;
    return ((selling - buying) / selling) * 100;
  };

  if (!isOpen) return null;

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
      onClick={(e) => {
        // Only close if clicking directly on the background overlay and page is visible
        if (e.target === e.currentTarget && isPageVisible && !wasHiddenWithData) {
          onClose();
        }
      }}
    >
      <div 
        className="bg-white dark:bg-dark-bg-1 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-dark-border-default">
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-white">
            {product ? 'Edit Product' : 'Add New Product'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {/* Product Name */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-dark-text-primary mb-2">
              Product Name
            </label>
            <input
              type="text"
              value={productName}
              onChange={(e) => setProductName(e.target.value)}
              disabled={!!product}
              className={`w-full px-3 py-2 border ${errors.productName ? 'border-red-500' : 'border-gray-300 dark:border-dark-border-default'} 
                       rounded-lg bg-white dark:bg-dark-bg-2 text-gray-900 dark:text-dark-text-primary
                       focus:outline-none focus:ring-2 focus:ring-dark-accent-blue
                       disabled:bg-gray-100 dark:disabled:bg-dark-bg-3 disabled:cursor-not-allowed`}
              placeholder="e.g., Tesamorelin"
            />
            {errors.productName && (
              <p className="mt-1 text-sm text-red-600">{errors.productName}</p>
            )}
          </div>

          {/* Variants Section */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Variants
              </h3>
              <span className="text-sm text-gray-500 dark:text-gray-400">
                Add price per variant
              </span>
            </div>

            <div className="space-y-3">
              {variants.map((variant, index) => (
                <div key={index} className="border border-gray-200 dark:border-dark-border-subtle rounded-lg p-4">
                  <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                    {/* Dose */}
                    <div>
                      <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                        Dose
                      </label>
                      <input
                        type="text"
                        value={variant.dose}
                        onChange={(e) => updateVariant(index, 'dose', e.target.value)}
                        disabled={!variant.isNew}
                        className={`w-full px-3 py-2 border ${errors[`variant_${index}_dose`] ? 'border-red-500' : 'border-gray-300 dark:border-dark-border-default'} 
                                 rounded bg-white dark:bg-dark-bg-2 text-gray-900 dark:text-dark-text-primary
                                 focus:outline-none focus:ring-2 focus:ring-dark-accent-blue text-sm
                                 disabled:bg-gray-100 dark:disabled:bg-dark-bg-3 disabled:cursor-not-allowed`}
                        placeholder="e.g., 5 mg"
                      />
                    </div>

                    {/* Code */}
                    <div>
                      <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                        Code
                      </label>
                      <input
                        type="text"
                        value={variant.code}
                        onChange={(e) => updateVariant(index, 'code', e.target.value.toUpperCase())}
                        disabled={!variant.isNew}
                        className={`w-full px-3 py-2 border ${errors[`variant_${index}_code`] ? 'border-red-500' : 'border-gray-300 dark:border-dark-border-default'} 
                                 rounded bg-white dark:bg-dark-bg-2 text-gray-900 dark:text-dark-text-primary
                                 focus:outline-none focus:ring-2 focus:ring-dark-accent-blue text-sm font-mono
                                 disabled:bg-gray-100 dark:disabled:bg-dark-bg-3 disabled:cursor-not-allowed`}
                        placeholder="TSM5"
                        maxLength={10}
                      />
                    </div>

                    {/* Buying Price */}
                    <div>
                      <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                        Buying Price
                      </label>
                      <div className="relative">
                        <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500 text-sm">$</span>
                        <input
                          type="number"
                          value={variant.buyingPrice || ''}
                          onChange={(e) => updateVariant(index, 'buyingPrice', parseFloat(e.target.value) || 0)}
                          className={`w-full pl-8 pr-3 py-2 border ${errors[`variant_${index}_buying`] ? 'border-red-500' : 'border-gray-300 dark:border-dark-border-default'} 
                                   rounded bg-white dark:bg-dark-bg-2 text-gray-900 dark:text-dark-text-primary
                                   focus:outline-none focus:ring-2 focus:ring-dark-accent-blue text-sm`}
                          placeholder="0"
                          min="0"
                          step="0.01"
                        />
                      </div>
                    </div>

                    {/* Selling Price */}
                    <div>
                      <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                        Selling Price
                      </label>
                      <div className="relative">
                        <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500 text-sm">$</span>
                        <input
                          type="number"
                          value={variant.sellingPrice || ''}
                          onChange={(e) => updateVariant(index, 'sellingPrice', parseFloat(e.target.value) || 0)}
                          className={`w-full pl-8 pr-3 py-2 border ${errors[`variant_${index}_selling`] || errors[`variant_${index}_margin`] ? 'border-red-500' : 'border-gray-300 dark:border-dark-border-default'} 
                                   rounded bg-white dark:bg-dark-bg-2 text-gray-900 dark:text-dark-text-primary
                                   focus:outline-none focus:ring-2 focus:ring-dark-accent-blue text-sm`}
                          placeholder="0"
                          min="0"
                          step="0.01"
                        />
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex items-end justify-between">
                      <div>
                        <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                          Margin
                        </label>
                        <div className="text-sm font-semibold text-green-600 dark:text-green-400">
                          {calculateMargin(variant.buyingPrice, variant.sellingPrice).toFixed(1)}%
                        </div>
                      </div>
                      {(variant.isNew || variants.length > 1) && (
                        <button
                          type="button"
                          onClick={() => removeVariant(index)}
                          className="p-2 text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20 rounded"
                          title="Remove variant"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                  </div>

                  {/* Error Messages */}
                  {(errors[`variant_${index}_dose`] || errors[`variant_${index}_code`] || 
                    errors[`variant_${index}_buying`] || errors[`variant_${index}_selling`] ||
                    errors[`variant_${index}_margin`]) && (
                    <div className="mt-2 text-sm text-red-600">
                      {errors[`variant_${index}_dose`] || errors[`variant_${index}_code`] || 
                       errors[`variant_${index}_buying`] || errors[`variant_${index}_selling`] ||
                       errors[`variant_${index}_margin`]}
                    </div>
                  )}
                </div>
              ))}
            </div>

            {/* Add Variant Button */}
            <button
              type="button"
              onClick={addVariant}
              className="mt-4 w-full py-2 border-2 border-dashed border-gray-300 dark:border-dark-border-subtle 
                       rounded-lg text-gray-600 dark:text-gray-400 hover:border-gray-400 dark:hover:border-gray-500
                       hover:text-gray-700 dark:hover:text-gray-300 transition-colors
                       flex items-center justify-center gap-2"
            >
              <Plus className="h-4 w-4" />
              Add Another Variant
            </button>
          </div>

          {errors.variants && (
            <div className="mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-900/50 rounded-lg">
              <div className="flex items-center gap-2 text-red-600 dark:text-red-400">
                <AlertCircle className="h-4 w-4" />
                <span className="text-sm">{errors.variants}</span>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-end gap-3 p-6 border-t border-gray-200 dark:border-dark-border-default">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 dark:border-dark-border-default rounded-lg
                     text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-dark-bg-hover
                     focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
          >
            Cancel
          </button>
          <button
            type="button"
            onClick={handleSubmit}
            disabled={loading}
            className="px-4 py-2 bg-dark-accent-blue text-white rounded-lg hover:bg-blue-700
                     focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-dark-accent-blue
                     disabled:bg-gray-400 disabled:cursor-not-allowed"
          >
            {loading ? 'Saving...' : (product ? 'Save Changes' : 'Add Product')}
          </button>
        </div>
      </div>
    </div>
  );
};