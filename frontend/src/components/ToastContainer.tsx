import React from 'react';
import { Toast } from './Toast';
import type { ToastProps } from './Toast';

interface ToastContainerProps {
  toasts: Omit<ToastProps, 'onDismiss'>[];
  onDismiss: (id: string) => void;
}

export const ToastContainer: React.FC<ToastContainerProps> = ({ toasts, onDismiss }) => {
  return (
    <div 
      aria-live="assertive" 
      className="fixed inset-0 flex flex-col items-center sm:items-end justify-start px-3 py-4 sm:px-6 sm:py-6 pointer-events-none z-[100]"
    >
      <div className="w-full flex flex-col items-center sm:items-end space-y-3 sm:space-y-4 max-w-full">
        {toasts.map((toast, index) => (
          <div
            key={toast.id}
            className={`
              transform transition-all duration-300 w-full flex justify-center sm:justify-end
              ${index === 0 ? 'translate-y-0' : ''}
              ${toasts.length > index ? 'translate-y-0 opacity-100' : 'translate-y-2 opacity-0'}
            `}
            style={{
              transitionDelay: `${index * 50}ms`
            }}
          >
            <Toast
              {...toast}
              onDismiss={onDismiss}
            />
          </div>
        ))}
      </div>
    </div>
  );
};