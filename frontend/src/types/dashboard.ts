export type DashboardProfit = {
  totalUSD: number;
  totalINR: number;
  byProduct: {
    Tirzepatide: number;
    Retatrutide: number;
    Semaglutide: number;
    'BAC Water': number;
  };
}

export type DashboardRevenue = {
  totalUSD: number;
  totalINR: number;
}

export type DashboardProfitMargin = {
  percentage: number;
}

export type DashboardOrders = {
  total: number;
  byStatus: {
    pending: number;
    sent: number;
    shipped: number;
  };
}

export type DashboardProducts = {
  total: number;
}

export type DashboardMetrics = {
  profit: DashboardProfit;
  revenue: DashboardRevenue;
  profitMargin: DashboardProfitMargin;
  orders: DashboardOrders;
  products: DashboardProducts;
}

export type DailyRevenueProfitData = {
  date: string;
  revenue: number;
  profit: number;
}

export type DailyOrderCountData = {
  date: string;
  count: number;
}

export type ShippingStatusDistribution = {
  status: string;
  count: number;
  percentage: number;
}