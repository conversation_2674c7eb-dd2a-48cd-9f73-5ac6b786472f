import React from 'react';
import { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';
import { useState } from 'react';
import { OrderForm } from './pages/OrderForm';
import { OrderDetail } from './pages/OrderDetail';
import { EditOrder } from './pages/EditOrder';
import { OrderHistory } from './pages/OrderHistory';
import { Settings } from './pages/Settings';
import Dashboard from './pages/Dashboard';
import ApiTest from './pages/ApiTest';
import { ProductManagementV2 } from './pages/ProductManagementV2';
import { DarkModeToggle } from './components/DarkModeToggle';
import { EnvironmentIndicator } from './components/EnvironmentIndicator';
import { ToastProvider } from './contexts/ToastContext';
import { Menu, X } from 'lucide-react';

function App() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  return (
    <ToastProvider>
      <Router>
        <div className="min-h-screen bg-gray-50 dark:bg-dark-bg-0">
          <nav className="relative bg-white dark:bg-dark-bg-3 shadow dark:shadow-dark-lg z-40">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="flex justify-between h-16">
                <div className="flex items-center">
                  <Link
                    to="/dashboard"
                    className="flex items-center px-2 py-2 text-gray-900 dark:text-dark-text-primary font-semibold text-base sm:text-lg hover:text-gray-700 dark:hover:text-white"
                  >
                    <span className="hidden sm:inline">Peptide Order Portal</span>
                    <span className="sm:hidden">POP</span>
                  </Link>
                  <div className="hidden sm:flex ml-10 items-center space-x-4">
                    <Link
                      to="/dashboard"
                      className="text-gray-500 dark:text-dark-text-secondary hover:text-gray-900 dark:hover:text-dark-text-primary px-3 py-2 rounded-md text-sm font-medium hover:bg-gray-100 dark:hover:bg-dark-bg-hover"
                    >
                      Dashboard
                    </Link>
                    <Link
                      to="/new-order"
                      className="text-gray-500 dark:text-dark-text-secondary hover:text-gray-900 dark:hover:text-dark-text-primary px-3 py-2 rounded-md text-sm font-medium hover:bg-gray-100 dark:hover:bg-dark-bg-hover"
                    >
                      New Order
                    </Link>
                    <Link
                      to="/orders"
                      className="text-gray-500 dark:text-dark-text-secondary hover:text-gray-900 dark:hover:text-dark-text-primary px-3 py-2 rounded-md text-sm font-medium hover:bg-gray-100 dark:hover:bg-dark-bg-hover"
                    >
                      Order History
                    </Link>
                    <Link
                      to="/products"
                      className="text-gray-500 dark:text-dark-text-secondary hover:text-gray-900 dark:hover:text-dark-text-primary px-3 py-2 rounded-md text-sm font-medium hover:bg-gray-100 dark:hover:bg-dark-bg-hover"
                    >
                      Products
                    </Link>
                    <Link
                      to="/settings"
                      className="text-gray-500 dark:text-dark-text-secondary hover:text-gray-900 dark:hover:text-dark-text-primary px-3 py-2 rounded-md text-sm font-medium hover:bg-gray-100 dark:hover:bg-dark-bg-hover"
                    >
                      Settings
                    </Link>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <EnvironmentIndicator />
                  <DarkModeToggle />
                  <button
                    onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                    className="sm:hidden inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-dark-bg-hover focus:outline-none focus:ring-2 focus:ring-inset focus:ring-dark-accent-blue"
                  >
                    {isMobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
                  </button>
                </div>
              </div>
            </div>

            {/* Mobile menu */}
            {isMobileMenuOpen && (
              <div className="sm:hidden absolute top-16 left-0 right-0 z-50 bg-white dark:bg-dark-bg-3 border-t border-gray-200 dark:border-dark-border-subtle shadow-lg">
                <div className="px-2 pt-2 pb-3 space-y-1">
                  <Link
                    to="/dashboard"
                    onClick={() => setIsMobileMenuOpen(false)}
                    className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-dark-text-primary hover:text-gray-900 hover:bg-gray-100 dark:hover:bg-dark-bg-hover"
                  >
                    Dashboard
                  </Link>
                  <Link
                    to="/new-order"
                    onClick={() => setIsMobileMenuOpen(false)}
                    className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-dark-text-primary hover:text-gray-900 hover:bg-gray-100 dark:hover:bg-dark-bg-hover"
                  >
                    New Order
                  </Link>
                  <Link
                    to="/orders"
                    onClick={() => setIsMobileMenuOpen(false)}
                    className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-dark-text-primary hover:text-gray-900 hover:bg-gray-100 dark:hover:bg-dark-bg-hover"
                  >
                    Order History
                  </Link>
                  <Link
                    to="/products"
                    onClick={() => setIsMobileMenuOpen(false)}
                    className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-dark-text-primary hover:text-gray-900 hover:bg-gray-100 dark:hover:bg-dark-bg-hover"
                  >
                    Products
                  </Link>
                  <Link
                    to="/settings"
                    onClick={() => setIsMobileMenuOpen(false)}
                    className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-dark-text-primary hover:text-gray-900 hover:bg-gray-100 dark:hover:bg-dark-bg-hover"
                  >
                    Settings
                  </Link>
                </div>
              </div>
            )}
          </nav>

          <main>
            <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="/dashboard" element={<Dashboard />} />
              <Route path="/new-order" element={<OrderForm />} />
              <Route path="/orders/:id" element={<OrderDetail />} />
              <Route path="/orders/:id/edit" element={<EditOrder />} />
              <Route path="/orders" element={<OrderHistory />} />
              <Route path="/products" element={<ProductManagementV2 />} />
              <Route path="/settings" element={<Settings />} />
              <Route path="/api-test" element={<ApiTest />} />
            </Routes>
          </main>
        </div>
      </Router>
    </ToastProvider>
  )
}

export default App
