/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  darkMode: 'class',
  theme: {
  	extend: {
  		colors: {
  			dark: {
  				bg: {
  					'0': '#1b1b1b',        // Main background - dark gray, not black
  					'1': '#222222',        // Surface level 1 - slightly lighter
  					'2': '#242424',        // Surface level 2 - input backgrounds
  					'3': '#2a2a2a',        // Surface level 3 - elevated components
  					'4': '#2e2e2e',        // Surface level 4 - highest elevation
  					hover: '#333333',      // Hover state - more pronounced
  					overlay: '#1a1a1aef'   // Modal/overlay background
  				},
  				text: {
  					primary: 'rgba(255, 255, 255, 0.87)',    // 87% white opacity - high emphasis
  					secondary: 'rgba(255, 255, 255, 0.60)',  // 60% white opacity - medium emphasis
  					disabled: 'rgba(255, 255, 255, 0.38)',   // 38% white opacity - disabled text
  					hint: 'rgba(255, 255, 255, 0.50)',       // 50% white opacity - placeholder text
  					inverse: '#1b1b1b'                       // Dark text for light backgrounds
  				},
  				border: {
  					subtle: 'rgba(255, 255, 255, 0.08)',     // Very subtle borders
  					default: 'rgba(255, 255, 255, 0.12)',    // Default borders
  					strong: 'rgba(255, 255, 255, 0.16)',     // Strong borders
  					focus: 'rgba(255, 255, 255, 0.24)'       // Focus ring borders
  				},
  				accent: {
  					blue: '#5890ff',      // Desaturated blue - accessible
  					purple: '#9c88ff',    // Desaturated purple - accessible
  					green: '#4db6ac',     // Desaturated green - accessible
  					red: '#ef5350',       // Desaturated red - accessible
  					yellow: '#ffb74d',    // Desaturated yellow - accessible
  					orange: '#ff8a65'     // Desaturated orange - accessible
  				},
  				surface: {
  					low: '#242424',       // Low emphasis surface
  					medium: '#2a2a2a',    // Medium emphasis surface
  					high: '#2e2e2e'       // High emphasis surface
  				}
  			},
  			background: 'hsl(var(--background))',
  			foreground: 'hsl(var(--foreground))',
  			card: {
  				DEFAULT: 'hsl(var(--card))',
  				foreground: 'hsl(var(--card-foreground))'
  			},
  			popover: {
  				DEFAULT: 'hsl(var(--popover))',
  				foreground: 'hsl(var(--popover-foreground))'
  			},
  			primary: {
  				DEFAULT: 'hsl(var(--primary))',
  				foreground: 'hsl(var(--primary-foreground))'
  			},
  			secondary: {
  				DEFAULT: 'hsl(var(--secondary))',
  				foreground: 'hsl(var(--secondary-foreground))'
  			},
  			muted: {
  				DEFAULT: 'hsl(var(--muted))',
  				foreground: 'hsl(var(--muted-foreground))'
  			},
  			accent: {
  				DEFAULT: 'hsl(var(--accent))',
  				foreground: 'hsl(var(--accent-foreground))'
  			},
  			destructive: {
  				DEFAULT: 'hsl(var(--destructive))',
  				foreground: 'hsl(var(--destructive-foreground))'
  			},
  			border: 'hsl(var(--border))',
  			input: 'hsl(var(--input))',
  			ring: 'hsl(var(--ring))',
  			chart: {
  				'1': 'hsl(var(--chart-1))',
  				'2': 'hsl(var(--chart-2))',
  				'3': 'hsl(var(--chart-3))',
  				'4': 'hsl(var(--chart-4))',
  				'5': 'hsl(var(--chart-5))'
  			}
  		},
  		boxShadow: {
  			'dark-sm': '0 1px 2px 0 rgba(0, 0, 0, 0.3)',
  			'dark-md': '0 4px 6px -1px rgba(0, 0, 0, 0.4)',
  			'dark-lg': '0 10px 15px -3px rgba(0, 0, 0, 0.5)'
  		},
  		transitionProperty: {
  			colors: 'color, background-color, border-color, text-decoration-color, fill, stroke'
  		},
  		borderRadius: {
  			lg: 'var(--radius)',
  			md: 'calc(var(--radius) - 2px)',
  			sm: 'calc(var(--radius) - 4px)'
  		}
  	}
  },
  plugins: [require("tailwindcss-animate")],
}

