## Development Environment

### Port Configuration
- **Development**: backend port 3000, frontend port 5173
- **Production**: backend port 3001, frontend port 5174
- If ports are already in use, kill those processes and then use them. Other ports simply won't work.

### URLs
- **Development**: http://localhost:5173 (frontend) | http://localhost:3000 (backend)
- **Production**: http://localhost:5174 (frontend) | http://localhost:3001 (backend)

## Server Management (PM2)

### Quick Start (Recommended)

#### Development Servers - Simple Commands
```bash
# Start both development servers (easiest way)
npm run dev

# Stop both development servers
npm run dev:stop
```

#### Development Servers - Direct PM2 Commands
```bash
# Start both development servers
pm2 start "npm run dev:api" --name backend
pm2 start "npm run dev:ui" --name frontend

# Start individual development server
pm2 start backend   # Start backend only
pm2 start frontend  # Start frontend only
```

#### Production Servers
```bash
# Start both production servers
pm2 start "npm run prod:api" --name backend-prod
pm2 start "npm run prod:ui" --name frontend-prod

# Start individual production server
pm2 start backend-prod   # Start production backend only
pm2 start frontend-prod  # Start production frontend only
```

### Stopping Servers
```bash
# Stop all servers
pm2 stop all

# Stop development servers
pm2 stop backend frontend

# Stop production servers
pm2 stop backend-prod frontend-prod

# Stop individual server
pm2 stop backend        # Stop dev backend
pm2 stop frontend       # Stop dev frontend
pm2 stop backend-prod   # Stop prod backend
pm2 stop frontend-prod  # Stop prod frontend
```

### Restarting Servers
```bash
# Restart all servers
pm2 restart all

# Restart development servers
pm2 restart backend frontend

# Restart production servers
pm2 restart backend-prod frontend-prod

# Restart individual server
pm2 restart backend        # Restart dev backend
pm2 restart frontend       # Restart dev frontend
pm2 restart backend-prod   # Restart prod backend
pm2 restart frontend-prod  # Restart prod frontend
```

### Other Useful Commands
```bash
pm2 list          # Show all running processes
pm2 logs          # Show logs for all processes
pm2 logs backend  # Show logs for backend only
pm2 logs frontend # Show logs for frontend only
pm2 monit         # Monitor CPU/memory usage
pm2 delete all    # Remove all processes from pm2
```

### Development Workflow Benefits
- **Persistence**: Services stay running even if you close terminal/IDE
- **Hot Reload**: Both services maintain hot reload (tsx watch + Vite HMR)
- **Crash Recovery**: PM2 automatically restarts if processes crash
- **Easy Management**: Simple `npm run dev` to start everything
- **Centralized Logging**: Use `pm2 logs` to view all service logs

### VS Code Integration
For even easier development, VS Code status bar buttons are available:
- **🚀 Start Servers** - One-click server startup (green)
- **🛑 Stop Servers** - One-click server shutdown (red)
- **🔄 Restart Servers** - One-click server restart (orange)
- **📊 Server Status** - Check PM2 process status (blue)

**Colors**: Optimized for both Solarized Light and Dracula themes
**Setup**: Install the "Action Buttons" extension and open `pop.code-workspace`
**Documentation**: See `docs/vscode-setup.md` for detailed setup instructions

## Communication Guideline

- Do not ask me to do your job. You are my assistant.

## Testing Guidelines

- Always test the fix yourself. Do not ask me to test it when you are capable of testing it. For frontend tasks use Playwright MCP server.

## Current Context

- Current year is 2025.

## Notes
- psql is installed through homebrew
- Do not kill the development servers before providing the summary because I need to test your work.