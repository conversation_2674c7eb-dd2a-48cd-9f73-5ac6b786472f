# Dependencies
node_modules/
.pnp
.pnp.js

# Testing
coverage/
.nyc_output/

# Production
build/
dist/
exports/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# OS
.DS_Store
Thumbs.db

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Database
*.sqlite
*.sqlite3
prisma/migrations/
*.sql
backup_*.sql
database_backups/
backup.log

# Temporary files
tmp/
temp/
.cache/

# TypeScript
*.tsbuildinfo

# Package manager
yarn.lock
pnpm-lock.yaml
pop.code-workspace
backend/env

# Test artifacts
test-results.txt
playwright-report/
frontend/test-results/
frontend/screenshots/*.png
.last-run.json
error-context.md
database_backups/backup_summary_20250728_151444.md
.claude/settings.local.json

# Project archives (do not version)
archive/

# Backup files (do not version - contains sensitive data)
backup/
docs
