# Peptide Order Portal

A local-only web application for managing peptide orders with crypto payment tracking and automated supplier communications.

## Features
- Create and manage peptide orders
- US address validation with Google Places
- Crypto payment tracking (BTC, USDT, USDC)
- Automated PDF/XLS generation
- Email integration for supplier communications
- Shipping tracking with Parcels App integration
- Profit margin calculations

## Tech Stack
- **Backend**: Node.js 20, Fastify, TypeScript, Prisma ORM
- **Frontend**: React 19, Vite, Tailwind CSS
- **Database**: PostgreSQL 16
- **File Generation**: pdf-lib, ExcelJS

## Prerequisites
- Node.js 20+
- PostgreSQL 16
- Gmail account with App Password

## Setup

### 1. Clone the repository
```bash
git clone https://github.com/yourusername/peptide-order-portal.git
cd peptide-order-portal
```

### 2. Install dependencies
```bash
npm install
```

### 3. Database Setup
Create a PostgreSQL database:
```sql
CREATE DATABASE peptide_portal_dev;
```

### 4. Configure environment
Copy the example environment file in the backend:
```bash
cp backend/.env.example backend/.env
```

Update the `.env` file with your credentials:
- `DATABASE_URL` - PostgreSQL connection string
- `GMAIL_USER` - Your Gmail address
- `GOOGLE_APP_PASSWORD` - Gmail app password
- `GOOGLE_API_KEY` - Google Places API key
- `PARCELS_APP_API_KEY` - Parcels App API key

### 5. Initialize database
```bash
# Generate Prisma client
cd backend
npx prisma generate

# Push schema to database
npm run db:push

# Seed pricing data
npm run db:seed
```

### 6. Start development servers
In separate terminals:

```bash
# Backend (runs on http://localhost:3000)
npm run dev:api

# Frontend (runs on http://localhost:5173)
npm run dev:ui
```

## Project Structure
```
peptide-order-portal/
├── backend/
│   ├── prisma/          # Database schema
│   ├── src/
│   │   ├── routes/      # API endpoints
│   │   ├── services/    # Business logic
│   │   ├── scripts/     # Utility scripts
│   │   └── types/       # TypeScript types
├── frontend/
│   └── src/
│       ├── pages/       # React pages
│       ├── components/  # React components
│       ├── api/         # API client
│       └── utils/       # Utilities
└── shared/
    └── types/           # Shared TypeScript types
```

## Documentation

Additional documentation can be found in the `docs/` directory:

- **Development**
  - [CLI Guide](docs/development/CLI_GUIDE.md) - Command-line interface for managing the development environment
  - [Environment Setup](docs/development/ENVIRONMENT_SETUP.md) - Detailed setup and configuration guide

- **Project Planning**
  - [Global Address Project](docs/project-planning/GLOBAL_ADDRESS_PROJECT.md) - International address handling implementation
  - [Global Address Tasks](docs/project-planning/GLOBAL_ADDRESS_PROJECT_TASKS.md) - Detailed task tracking for address features
  - [Add Products Plan](docs/project-planning/add-products-plan.md) - Product management feature planning

- **Troubleshooting**
  - [Disable Address Validation](docs/troubleshooting/DISABLE_ADDRESS_VALIDATION.md) - How to disable address validation
  - [Fix Address Validation](docs/troubleshooting/FIX_ADDRESS_VALIDATION.md) - Fixing Google API errors

## License
MIT