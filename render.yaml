services:
  - type: web
    name: peptide-portal-backend
    runtime: node
    repo: https://github.com/iambaseddev/peptide-order-portal
    branch: main
    region: singapore
    rootDir: backend
    buildCommand: npm install && npm run build
    startCommand: npm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 3000
      - key: DATABASE_URL
        fromDatabase:
          name: peptide-portal-db
          property: connectionString
      - key: GMAIL_USER
        sync: false
      - key: GOOGLE_APP_PASSWORD
        sync: false
      - key: GOOGLE_API_KEY
        sync: false
      - key: SEVENTEEN_TRACK_API_KEY
        sync: false
      - key: PARCELS_APP_API_KEY
        sync: false
      - key: COINGECKO_URL
        value: https://api.coingecko.com/api/v3

  - type: web
    name: peptide-portal-frontend
    runtime: static
    repo: https://github.com/iambaseddev/peptide-order-portal
    branch: main
    region: singapore
    rootDir: frontend
    buildCommand: npm install && npm run build
    staticPublishPath: ./dist
    envVars:
      - key: VITE_API_BASE_URL
        value: https://peptide-portal-backend.onrender.com/api
    headers:
      - path: /*
        name: X-Frame-Options
        value: SAMEORIGIN

databases:
  - name: peptide-portal-db
    region: singapore
    plan: starter
    postgresMajorVersion: 16